<template>
  <div class="point-control">
    <div class="options-wrapper">
      <OptionList :options="options" :used-option-ids="usedOptionIds" :disabled="disabled"
        @drag-start="handleOptionDragStart" />
    </div>
    <div class="base-content content">
      <div class="section-title-light">
        <div class="title-label">{{ taskDetail.questionName }}</div>
      </div>
      <div class="content-wrapper">
        <div class="timeline-container">
          <div class="y-axis">
            <div class="axis-arrow"></div>
          </div>

          <div class="x-axis">
            <div class="axis-arrow"></div>
          </div>
          <div class="curve">
            <img src="@/assets/images/train/curve.png" alt="s-curve">

          </div>

          <div class="periods-container">
            <div v-for="(period, periodIndex) in timelineData" :key="periodIndex"
              :class="`time-period period-${periodIndex + 1}`">
              <div class="period-content">
                <div class="period-content-wrapper" :class="{ single: period.lifecycle.length === 1 }">
                  <el-scrollbar height="100%">
                    <div class="drop-area">
                      <div class="drop-area-content">
                        <OptionItem width="100%" height="74px" v-for="(content, index) in period.lifecycle" :key="index"
                          :name="content" :placeholder="period.lifecycleHint" @dragover.prevent
                          :show-delete="!disabled && !!content" :disabled="disabled" :empty="!content"
                          @dragstart="handleItemDragStart($event, content)"
                          @delete="handleDeleteItem(periodIndex, index, 'lifecycle', content)"
                          @dragenter="handleDragEnter($event)"
                          @drop="handleCellDrop($event, periodIndex, index, 'lifecycle')"
                          @dragleave="handleDragLeave($event)" />
                      </div>
                    </div>
                  </el-scrollbar>
                </div>
              </div>
              <div class="period-time" :title="period.period">
                {{ period.period }}
              </div>
              <div class="period-strategy">
                <el-scrollbar height="100%">
                  <div class="strategy-content">
                    <OptionItem width="100%" v-for="(content, index) in period.strategy" :key="index" :name="content"
                      :placeholder="period.strategyHint" @dragover.prevent :show-delete="!disabled && !!content"
                      :disabled="disabled" :empty="!content" @dragstart="handleItemDragStart($event, content)"
                      @delete="handleDeleteItem(periodIndex, index, 'strategy', content)"
                      @dragenter="handleDragEnter($event)"
                      @drop="handleCellDrop($event, periodIndex, index, 'strategy')"
                      @dragleave="handleDragLeave($event)" />
                  </div>
                </el-scrollbar>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import OptionList from './OptionList.vue'
import OptionItem from './OptionItem.vue'
import type { OptionItem as OptionItemType } from '@/views/admin/types'
import type { TaskDetailItem } from '../../types'
import * as api from '@/api/student'

interface IProps {
  taskDetail: TaskDetailItem,
  disabled?: boolean
  apiExtraParams?: Record<string, string>
  mode?: 'viewAnswer' | 'answer'
}

interface TimelinePeriod {
  period: string
  lifecycle: string[]  // 产品生命周期（多个）
  strategy: string[]   // 战略特点（多个）
  lifecycleHint: string     // 生命周期提示
  strategyHint: string      // 战略特点提示
}

const props = withDefaults(defineProps<IProps>(), {
  disabled: false,
  apiExtraParams: () => ({}),
  mode: 'answer'
})

const rawTimeline = ref<TimelinePeriod[]>([
  {
    period: '',
    lifecycle: ['',],
    strategy: ['',],
    lifecycleHint: '',
    strategyHint: ''
  }
])
const isDragging = ref(false)
const options = ref<OptionItemType[]>([])
const timelineData = ref<TimelinePeriod[]>([...rawTimeline.value])
const currentTarget = ref<HTMLElement | null>(null)

const handleOptionDragStart = (option: OptionItemType, event: DragEvent) => {
  if (props.disabled) return
  event.dataTransfer?.setData('text/plain', JSON.stringify(option))
  isDragging.value = true
  currentTarget.value = event.currentTarget as HTMLElement
}

const handleItemDragStart = (event: DragEvent, optionName: string) => {
  if (props.disabled) return
  const option = options.value.find(opt => opt.name === optionName)
  if (option) {
    event.dataTransfer?.setData('text/plain', JSON.stringify(option))
    currentTarget.value = event.currentTarget as HTMLElement
    isDragging.value = true
  }
}

const handleDragEnter = (event: DragEvent) => {
  if (props.disabled) return
  const target = event.currentTarget as HTMLElement
  target.classList.add('highlight-drop')
}

const handleDragLeave = (event: DragEvent) => {
  const target = event.currentTarget as HTMLElement
  target.classList.remove('highlight-drop')
}

const handleSaveStep = async (answer: {
  content: string,
  index: number,
  operationType: 0 | 1,
  type: 0 | 1,
  columnIndex: number
}) => {
  return new Promise(async (resolve, reject) => {
    const params = {
      questionId: props.taskDetail.questionId,
      ...props.apiExtraParams,
      ...answer
    }
    try {
      const { data: { code, msg, data } } = await api.analysisRealTime(params)
      if (code === 200) {
        if (data === 1) {
          ElMessage.success('回答正确')
        } else if (data === 0) {
          ElMessage.error('回答错误')
        }
        resolve(true)
      } else {
        ElMessage.error(msg)
        reject(msg)
      }
    } catch (error) {
      console.error(error)
      reject(error)
    }
  })
}

const removeOptionFromTimeline = (optionName: string) => {
  return new Promise(async (resolve, reject) => {
    try {
      let timelineIndex = -1
      let innerIndex = -1
      let type: 'lifecycle' | 'strategy' = 'lifecycle'
      timelineData.value.forEach((period, index) => {
        const lifecycleIndex = period.lifecycle.indexOf(optionName)
        if (lifecycleIndex > -1) {
          timelineIndex = index
          innerIndex = lifecycleIndex
          type = 'lifecycle'
        }
        const strategyIndex = period.strategy.indexOf(optionName)
        if (strategyIndex > -1) {
          timelineIndex = index
          innerIndex = strategyIndex
          type = 'strategy'
        }
      })
      if (timelineIndex > -1 && innerIndex > -1) {
        await handleSaveStep({
          content: optionName,
          index: timelineIndex,
          operationType: 1,
          type: type === 'lifecycle' ? 0 : 1,
          columnIndex: innerIndex
        })
        timelineData.value[timelineIndex][type][innerIndex] = ''
      }
      resolve(true)
    } catch (error) {
      console.error(error)
      reject(error)
    }
  })
}

const handleCellDrop = async (event: DragEvent, periodIndex: number, index: number, type: 'lifecycle' | 'strategy') => {
  if (props.disabled || !isDragging.value || currentTarget.value === event.currentTarget) return
  const target = event.currentTarget as HTMLElement
  target.classList.remove('highlight-drop')
  const data = event.dataTransfer?.getData('text/plain')
  if (!data) return

  try {
    if (isDragging.value) {
      const option = JSON.parse(data) as OptionItemType
      if (!option || !option.name || !option.id) return

      await removeOptionFromTimeline(option.name)
      await handleSaveStep({
        content: option.name,
        index: periodIndex,
        operationType: 0,
        type: type === 'lifecycle' ? 0 : 1,
        columnIndex: index
      })
      timelineData.value[periodIndex][type][index] = option.name
      isDragging.value = false
    }
  } catch (e) {
    isDragging.value = false
    console.error('Invalid drag data format:', e)
  }
}


const handleDeleteItem = async (periodIndex: number, itemIndex: number, type: 'lifecycle' | 'strategy', content: string) => {
  if (props.disabled) return
  await handleSaveStep({
    content,
    index: periodIndex,
    operationType: 1,
    type: type === 'lifecycle' ? 0 : 1,
    columnIndex: itemIndex
  })
  timelineData.value[periodIndex][type][itemIndex] = ''
}

const optionsMap = computed(() => {
  const map = new Map<string, string>()
  options.value.forEach(item => {
    map.set(item.name, item.id)
  })
  return map
})

const usedOptionIds = computed(() => {
  const ids = new Set<string>()
  timelineData.value.forEach(period => {
    period.lifecycle.forEach(name => {
      const id = optionsMap.value.get(name)
      if (id) ids.add(id)
    })
    period.strategy.forEach(name => {
      const id = optionsMap.value.get(name)
      if (id) ids.add(id)
    })
  })
  return ids
})

const handleDragEnd = () => {
  isDragging.value = false
}


onMounted(() => {
  options.value = props.taskDetail.taskOptions ? JSON.parse(props.taskDetail.taskOptions) : []

  if (props.taskDetail.stuAnswer) {
    const savedData = JSON.parse(props.taskDetail.stuAnswer)
    timelineData.value = savedData
  } else if (props.taskDetail.initContent) {
    const initContent = JSON.parse(props.taskDetail.initContent)
    timelineData.value = initContent.timeline.map((item: TimelinePeriod) => ({
      period: item.period,
      lifecycle: Array(item.lifecycle.length).fill(''),
      strategy: Array(item.lifecycle.length).fill(''),
      lifecycleHint: item.lifecycleHint,
      strategyHint: item.strategyHint
    }))
  }

  document.addEventListener('dragend', handleDragEnd)
})

onUnmounted(() => {
  document.removeEventListener('dragend', handleDragEnd)
})

const getStuAnswer = () => {
  return Promise.resolve(JSON.stringify(timelineData.value))
}

defineExpose({
  getStuAnswer
})

watch(() => props.taskDetail, (newVal) => {
  if (newVal.stuAnswer) {
    timelineData.value = JSON.parse(newVal.stuAnswer)
  }
})

</script>

<style scoped lang="scss">
.point-control {
  @include flex(row, flex-start, flex-start, nowrap);
  height: 100%;
  gap: 16px;
  --x-axis-bottom: 200px;
  --y-axis-bottom: 80px;

  .base-content {
    height: 100%;
    gap: 20px;
    @include flex(column, flex-start, flex-start, nowrap);
    position: relative;

    .content-wrapper {
      width: 100%;
      flex: 1;
      min-height: 0;
      padding: 20px;
      background: url('@/assets/images/train/bg-light-task.png') no-repeat bottom center;
      background-size: 100% auto;
    }
  }

  .options-wrapper {
    width: 240px;
    height: 100%;
  }

  .content {
    min-width: 0;
    flex: 1;
    background-image: linear-gradient(to bottom, #fff 0%, rgba(255, 255, 255, 0.5) 100%);
    box-shadow: 0px 2px 4px 0px rgba(120, 147, 187, 0.15);
    border-radius: 6px;
    border: solid 1px #ffffff;
    overflow: hidden;
  }
}

.timeline-container {
  position: relative;
  width: 100%;
  height: 100%;

  // Y轴
  .y-axis {
    position: absolute;
    left: 20px;
    top: 0;
    bottom: var(--y-axis-bottom);
    width: 2px;
    background-color: #8a9de1;
    z-index: 1;

    .axis-arrow {
      position: absolute;
      top: -8px;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      border-bottom: 8px solid #8a9de1;
    }
  }

  // X轴
  .x-axis {
    position: absolute;
    left: 5px;
    right: 0;
    bottom: var(--x-axis-bottom);
    height: 2px;
    background-color: #8a9de1;
    z-index: 1;

    .axis-arrow {
      position: absolute;
      right: -8px;
      top: 50%;
      transform: translateY(-50%);
      width: 0;
      height: 0;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
      border-left: 8px solid #8a9de1;
    }
  }

  // S型曲线
  .s-curve {
    position: absolute;
    left: 50px;
    top: 20px;
    right: 20px;
    bottom: 100px;
    z-index: 2;
    pointer-events: none;
  }

  // 统一的时间段和拖拽区域容器
  .periods-container {
    width: 100%;
    height: 100%;
    padding-left: 50px;
    padding-right: 10px;
    position: relative;
    @include flex(row, flex-start, flex-start, nowrap);
    gap: 24px;
  }

  .curve {
    position: absolute;
    left: 80px;
    top: 60px;
    right: 20px;
    bottom: calc(var(--x-axis-bottom) + 60px);
    z-index: 2;
    pointer-events: none;

    img {
      width: 100%;
      max-height: 100%;
    }
  }

  .time-period {
    width: 100%;
    min-height: 0;
    flex: 1;
    margin-bottom: 10px;
    @include flex(column, flex-start, flex-start, nowrap);
    height: 100%;
    overflow: hidden;
    position: relative;

    &.period-1 {
      .period-content {
        background-color: #e5eaff;
        border: solid 1px #acbeff;
      }

      .single {
        top: 55%;
        height: auto;
      }

    }

    &.period-2 {
      .period-content {
        background-color: #d9efff;
        border: solid 1px #9ad4ff;
      }

      .single {
        top: 1%;
        height: auto;
      }
    }

    &.period-3 {
      .period-content {
        background-color: #d4f3f0;
        border: solid 1px #77e4d9;
      }

      .single {
        top: 20%;
        height: auto;
      }
    }

    &.period-4 {
      .period-content {
        background-color: #fff2e5;
        border: solid 1px #ffdab6;
      }

      .single {
        top: 32%;
        height: auto;
      }
    }
  }

  .period-content {
    width: 100%;
    flex-shrink: 0;
    height: calc(100% - var(--x-axis-bottom) + 60px);
  }

  .period-content-wrapper {
    position: relative;
    width: 100%;
    height: 90%;
    overflow: hidden;
    z-index: 9;
  }

  .drop-area {
    padding: 18px;
  }

  .drop-area-content {
    @include flex(column, flex-start, flex-start, nowrap);
    gap: 12px;
    height: calc((100% - 72px));
  }

  .period-time {
    position: absolute;
    left: 0;
    bottom: calc(var(--x-axis-bottom) - 24px);
    width: 100%;
    padding: 0 8px;
    text-align: center;
    font-size: var(--font-size);
    color: #333333;
    @include text-ellipsis();
  }

  .period-strategy {
    z-index: 9;
    position: relative;
    width: 100%;
    margin-top: 24px;
    height: calc(var(--x-axis-bottom) - 60px);
    overflow: hidden;


    .strategy-content {
      @include flex(column, flex-start, flex-start, nowrap);
      gap: 12px;
      padding: 0 18px;
    }
  }

  @media screen and (max-width: 1440px) {
    --x-axis-bottom: 120px;
    --y-axis-bottom: 40px;

    .options-wrapper {
      width: 200px;
    }

    .periods-container {
      gap: 12px;
      padding-left: 35px;
    }

    .time-period.period-1 .single {
      top: 35%;
    }

    .period-content {
      height: calc(100% - var(--x-axis-bottom) + 32px);

      .drop-area {
        padding: 12px;
      }
    }

    .period-strategy {
      margin-top: 12px;

      .strategy-content {
        padding: 0 10px;
      }
    }
  }

}
</style>
