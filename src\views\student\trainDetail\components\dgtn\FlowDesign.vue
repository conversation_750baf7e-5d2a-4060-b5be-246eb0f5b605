<template>
  <div class="flow-degisn">
    <div class="options-wrapper">
      <div class="section-title-light">
        <div class="title-label">任务列表</div>
      </div>
      <div class="flow-shapes-wrapper">
        <el-scrollbar height="100%">
          <div class="flow-shapes">
            <ShapeItem v-for="item in flowShapes" :key="item.type" :item="item" :draggable="!disabled"
              @dragstart="(e: DragEvent) => handleShapeDragStart(e, item)" />
          </div>
        </el-scrollbar>
      </div>
    </div>
    <div class="content">
      <div class="graph" ref="graphRef"></div>
      <GraphTools class="graph-tools" @undo="handleUndo" @redo="handleRedo" @zoom-out="handleZoomOut"
        @zoom-in="handleZoomIn" @reset-zoom="handleResetZoom" />
    </div>
    <PropertyPanel :visible="visible" :enterprise-id="taskDetail.enterpriseId" :disabled="disabled"
      :node="(currentNode! as Cell)" @close="handleClosePanel">
    </PropertyPanel>
  </div>
</template>

<script setup lang="ts">
import GraphTools from '@/components/flow/components/GraphTools.vue'
import ShapeItem from '@/components/flow/components/ShapeItem.vue'
import PropertyPanel from '@/components/flow/components/PropertyPanel.vue'
import { flowShapes } from '@/components/flow/config/constant'
import { watch, onMounted, nextTick } from 'vue'
import { useFlowDesign } from '@/components/flow/hooks/useFlowDesign'
import type { TaskDetailItem } from '../../types'
import type { Cell } from '@antv/x6'

interface Props {
  taskDetail: TaskDetailItem
  disabled?: boolean
  mode?: 'viewAnswer' | 'answer'
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  mode: 'answer'
})


const {
  graphRef,
  currentNode,
  visible,
  handleShapeDragStart,
  handleClosePanel,
  getGraphData,
  graph,
  injectData,
  initGraph,
  handleUndo,
  handleRedo,
  handleZoomOut,
  handleZoomIn,
  handleResetZoom
} = useFlowDesign({
  disabled: props.disabled,
  mode: props.mode,
})

const getStuAnswer = () => {
  return getGraphData()
}

//为了提交之后，更新disabled状态
watch(() => props.disabled, (newVal) => {
  //如果禁止了，要重新更新一下画布
  if (newVal) {
    graph.value?.clearCells()
    injectData(props.taskDetail.stuAnswer)
  }
})

const filterData = (data: string) => {
  const list: any[] = JSON.parse(data)
  const exitIds = new Set()
  list.forEach((item) => {
    if (!item.shape.includes('edge') && item.data?.isPreSet) {
      exitIds.add(item.id)
    }
  })
  const res = list.filter((item) => {
    if (!item.shape.includes('edge') && item.data?.isPreSet) {
      return true
    }
    if (item.shape.includes('edge') && exitIds.has(item.target.cell) && exitIds.has(item.source.cell)) {
      return true
    }
    return false
  })
  return JSON.stringify(res)
}

const makeError = (data: string) => {
  const rightNodeIdOrEdgeIds = props.taskDetail.rightNodeIdOrEdgeIds || []
  const list: any[] = JSON.parse(data)
  list.forEach((item) => {
    if (!rightNodeIdOrEdgeIds.includes(item.id)) {
      const tool = {
        name: 'boundary',
        args: {
          padding: 5,
          attrs: {
            fill: '#7c68fc',
            stroke: 'red',
            'stroke-width': 2,
            'fill-opacity': 0.3,
          },
        },
      }
      if (!item.shape.includes('edge')) {
        //如果是节点，直接判断有没有在正确答案id里
        item.tools = [tool]
      }
      if (item.shape.includes('edge') && item.data?.name) {
        //如果是线，要判断线是否设置了条件，参与判分
        item.tools = [tool]
      }
    }
  })
  return JSON.stringify(list)
}

onMounted(async () => {
  await nextTick()
  initGraph({
    background: {
      color: "rgba(0,0,0,0)", // 设置画布背景颜色
    },
  })
  if (props.taskDetail.stuAnswer) {
    if (props.mode === 'viewAnswer') {
      //查看答案，需要展示出未画出的节点和线
      injectData(makeError(props.taskDetail.stuAnswer))
      return
    }
    injectData(props.taskDetail.stuAnswer)
  } else {
    injectData(filterData(props.taskDetail.initContent))
  }
})

defineExpose({
  getStuAnswer
})
</script>

<style scoped lang="scss">
.flow-degisn {
  @include flex(row, flex-start, flex-start, nowrap);
  height: 100%;
  gap: 16px;
  position: relative;
  overflow: hidden;

  .options-wrapper {
    width: 136px;
    height: 100%;
    flex-shrink: 0;
    background-image: linear-gradient(to bottom, #fff 0%, rgba(255, 255, 255, 0.5) 100%);
    box-shadow: 0px 2px 4px 0px rgba(120, 147, 187, 0.15);
    border-radius: 6px;
    border: solid 1px #ffffff;
    @include flex(column, flex-start, flex-start, nowrap);
    overflow: hidden;
  }

  .flow-shapes-wrapper {
    flex: 1;
    min-height: 0;
    padding: 0 16px
  }

  .flow-shapes {
    @include flex(column, flex-start, stretch, nowrap);
    gap: 32px;
    margin-top: 12px;

    .shape-item {
      cursor: move;
      color: #001E22;
      user-select: none;

      &.used {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .shape {
        height: 34px;
        font-size: 14px;
        color: #fff;
        width: 104px;
        padding: 0 14px;
        @include flex(row, center, center, nowrap);

        .text {
          @include text-ellipsis();
        }

        &.start,
        &.end {
          background-color: #4ad189;
          border-radius: 17px;
          box-shadow: 0px 1px 0px 0px rgba(0, 0, 0, 0.1);
        }

        &.process {
          background-color: #677cf6;
        }

        &.decision {
          width: 96px;
          height: 96px;
          clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
          background-color: #ffa272;
        }
      }
    }
  }

  .content {
    height: 100%;
    min-width: 0;
    flex: 1;
    background-image: linear-gradient(to bottom, #fff 0%, rgba(255, 255, 255, 0.5) 100%);
    box-shadow: 0px 2px 4px 0px rgba(120, 147, 187, 0.15);
    border-radius: 6px;
    border: solid 1px #ffffff;
    position: relative;
  }

  .graph {
    height: 100%;
  }
}

:deep {
  .x6-widget-transform {
    margin: -1px 0 0 -1px;
    padding: 0px;
    border: 1px solid #239edd;
  }

  .x6-widget-transform>div {
    border: 1px solid #239edd;
  }

  .x6-widget-transform>div:hover {
    background-color: #3dafe4;
  }

  .x6-widget-transform-active-handle {
    background-color: #3dafe4;
  }

  .x6-widget-transform-resize {
    border-radius: 0;
  }

  .x6-widget-selection-inner {
    border: 1px solid #239edd;
  }

  .x6-widget-selection-box {
    opacity: 0;
  }
}
</style>
<style>
@keyframes ant-line {
  to {
    stroke-dashoffset: -1000
  }
}
</style>
