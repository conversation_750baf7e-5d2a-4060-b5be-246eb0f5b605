import { Shape } from '@antv/x6'
export const adminDefaultConfig = {
  background: {
    color: "#002752", // 设置画布背景颜色
  },
  translating: {
    restrict: true,
  },
  //画布拖拽
  panning: {
    enabled: true,
  },
  interacting: {
    nodeMovable: true, //节点拖动
  },
  highlighting: {
    magnetAvailable: {
      name: "stroke",
      args: {
        padding: 4,
        attrs: {
          "stroke-width": 4,
          stroke: "skyblue",
        },
      },
    },
    magnetAdsorbed: {
      name: "stroke",
      args: {
        padding: 4,
        attrs: {
          "stroke-width": 6,
          stroke: "skyblue",
        },
      },
    },
  },
  connecting: {
    snap: true,
    allowBlank: false,
    allowLoop: false,
    highlight: true,
    allowMulti: false,
    sourceAnchor: {
      name: "center",
    },
    targetAnchor: "center",
    connectionPoint: "anchor",
    router: "manhattan",
    validateMagnet(data: any) {
      const { magnet, cell } = data;
      return magnet.getAttribute("port-group") !== "in";
    },
    createEdge() {
      return new Shape.Edge({
        zIndex: -1,
        attrs: {
          line: {
            stroke: '#fff',
            strokeWidth: 1,
            targetMarker: {
              name: "block",
              args: {
                size: "6",
              },
            },
          },
        },
      });
    },
    validateConnection(data: any) {
      const { sourceView, targetView, sourceMagnet, targetMagnet } = data;
      if (!sourceMagnet || !targetMagnet) {
        return false;
      }

      // 判断目标链接桩是否可连接
      const portId = targetMagnet.getAttribute("port");
      const node: any = targetView?.cell;

      const port = node?.getPort(portId);
      if (!port) {
        return false;
      }

      return true;
    },
  },
}

export const ports = {
  items: [{
    group: 'in',
    id: 'p_top'
  },
  {
    group: 'right-out',
    id: 'p_right'
  },
  {
    group: 'bottom-out',
    id: 'p_bottom'
  },
  {
    group: 'left-out',
    id: 'p_left'
  }
  ],
  groups: {
    in: {
      position: 'top',
      zIndex: 1,
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: '#31d0c6',
          strokeWidth: 2,
          fill: '#fff'
        }
      }
    },
    "bottom-out": {
      position: 'bottom',
      zIndex: 1,
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: '#31d0c6',
          strokeWidth: 2,
          fill: '#fff'
        }
      }
    },
    "right-out": {
      position: 'right',
      zIndex: 20,
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: '#31d0c6',
          strokeWidth: 2,
          fill: '#fff'
        }
      }
    },
    "left-out": {
      position: 'left',
      zIndex: 20,
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: '#31d0c6',
          strokeWidth: 2,
          fill: '#fff'
        }
      }
    }
  }
}

export const lightPorts = {
  groups: {
    top: {
      position: 'top',
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: '#5F95FF',
          strokeWidth: 1,
          fill: '#fff',
          style: {
            visibility: 'hidden',
          },
        },
      },
    },
    right: {
      position: 'right',
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: '#5F95FF',
          strokeWidth: 1,
          fill: '#fff',
          style: {
            visibility: 'hidden',
          },
        },
      },
    },
    bottom: {
      position: 'bottom',
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: '#5F95FF',
          strokeWidth: 1,
          fill: '#fff',
          style: {
            visibility: 'hidden',
          },
        },
      },
    },
    left: {
      position: 'left',
      attrs: {
        circle: {
          r: 4,
          magnet: true,
          stroke: '#5F95FF',
          strokeWidth: 1,
          fill: '#fff',
          style: {
            visibility: 'hidden',
          },
        },
      },
    },
  },
  items: [
    {
      group: 'top',
    },
    {
      group: 'right',
    },
    {
      group: 'bottom',
    },
    {
      group: 'left',
    },
  ],
}
