import { ElMessage } from "element-plus";
import appSettings from '@/settings'

/**
 * 设置页面标题
 */
export const getPageTitle = (pageTitle: any) => {
    const title = appSettings.title || 'home'
    if (pageTitle) {
        return `${pageTitle} - ${title}`
    }
    return `${title}`
}

//中文参数转换
export function encodeName(str: string | number) {
    return encodeURIComponent(str);
}
export function decodeName(str: string) {
    return decodeURIComponent(str);
}

export const isJSON = (str: string) => {
    try {
        const obj = JSON.parse(str);
        if (typeof obj === 'object' && obj) {
            return true;
        } else {
            return false;
        }
    } catch (e) {
        return false;
    }
}
/**
 * 生产随机字符串
 * @param {*} len
 */
export const randomString = (len = 32) => {
    const $chars =
        'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'; /****默认去掉了容易混淆的字符oOLl,9gq,Vv,Uu,I1****/
    const maxPos = $chars.length;
    let pwd = '';
    for (let i = 0; i < len; i++) {
        pwd += $chars.charAt(Math.floor(Math.random() * maxPos));
    }
    return pwd;
};

/**
 * 下载文件
 */
export function downloadFileFormate(res: any, fileName: string, type: string) {
    let flieType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
    if (type != '') {
        flieType = type
    }
    const blob = new Blob([res], {
        type: flieType,
    })
    const downloadElement = document.createElement('a')
    const href = window.URL.createObjectURL(blob)
    downloadElement.href = href
    downloadElement.download = fileName
    document.body.appendChild(downloadElement)
    downloadElement.click()
    document.body.removeChild(downloadElement) // 下载完成移除元素
    window.URL.revokeObjectURL(href) // 释放掉blo
    ElMessage({
        type: 'success',
        message: '下载成功！'
    });
}

/**
 * 防抖函数
 * @param {Function} fn  
 * @param {Number} wait 延迟时间
 * @param {Boolean} immediate  是否立即执行
 */
export function _debounce(fn: any, wait = 200, immediate = false) {
    if (typeof fn !== 'function') throw "argument[0] must be the function";
    let timeout: NodeJS.Timeout | null = null;
    const debounce: any = function (this: any, ...args: any[]) {
        const context: any = this;
        timeout && clearTimeout(timeout);
        if (immediate && !timeout) fn.apply(context, args);
        timeout = setTimeout(function () {
            if (!immediate) fn.apply(context, args);
            timeout = null;
        }, wait);
    }
    //取消执行函数
    debounce.cancel = function () {
        timeout && clearTimeout(timeout);
        timeout = null;
    }
    return debounce;
}

export const isEmpty = (value: unknown): boolean => {
    if (typeof value === "string") {
        return value.trim().length === 0;
    }
    if (typeof value === "number") {
        return false;
    }
    if (Array.isArray(value)) {
        return value.length === 0;
    }
    if (typeof value === "object" && value !== null) {
        return Object.keys(value).length === 0;
    }
    return true;
}

// 读取文件为Base64
export const readAsBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(file);
    });
}
// base64转file
export const base64ToFile = (base64String: string, filename: string): File => {
    // 获取base64数据内容
    const arr = base64String.split(',');
    // 获取文件类型
    const mime = arr[0].match(/:(.*?);/)?.[1] || '';
    // 解码base64
    const bstr = atob(arr[1]);
    let n = bstr.length;
    const u8arr = new Uint8Array(n);

    while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
    }

    return new File([u8arr], filename, { type: mime });
}
