<template>
  <BaseLayout class="train-detail" v-loading="loading">
    <template #header>
      <Header :taskList="taskList" :taskDetail="taskDetail" @submit="handleSave" :disabled="disabled"></Header>
    </template>
    <div class="main-wrapper flex">
      <div class="left-wrapper">
        <TaskList v-model:currentTask="currentTask" v-model:taskList="taskList" @change="getStuTrainDetail"></TaskList>
      </div>
      <div class="right-wrapper">
        <component :key="taskDetail?.questionId" :is="currentComponent" ref="componentRef" :taskDetail="taskDetail"
          :disabled="disabled" />
      </div>
    </div>
  </BaseLayout>
</template>

<script setup lang="ts">
import BaseLayout from '@/layout/components/BaseLayout.vue'
import TaskList from './TaskList.vue'
import Header from './Header.vue'
import System from './System.vue'
import RiskCoordinate from './RiskCoordinate.vue'
import PointControl from './PointControl.vue'
import StrategyMap from './StrategyMap.vue'
import Empty from './Empty.vue'
import Organizational from './Organizational.vue'
import BusinessFlow from './BusinessFlow.vue'
import { computed } from 'vue'
import { useTrainDetail } from '../hooks/useTrainDetail'

const {
  loading,
  taskList,
  currentTask,
  taskDetail,
  disabled,
  componentRef,
  getStuTrainDetail,
  handleSave
} = useTrainDetail()

const currentComponent = computed(() => {
  const map = [Organizational, StrategyMap, BusinessFlow, PointControl, System, RiskCoordinate]
  return taskDetail.value ? map[taskDetail.value?.questionType] : Empty
})
</script>

<style scoped lang="scss">
.train-detail {
  .left-wrapper {
    flex: 1;
    max-width: 250px;
    flex-shrink: 0;
  }

  .main-wrapper {
    height: 100%;
    padding: 20px 156px 0;
    @include clamp(padding-left, 40, 156);
    @include clamp(padding-right, 40, 156);
    gap: 16px;
  }

  .right-wrapper {
    flex: 2;
    min-width: 0;
  }

  @media screen and (max-width: 1440px) {
    .left-wrapper {
      max-width: 200px;
    }

    .main-wrapper {
      padding: 14px 40px;
    }
  }
}
</style>
