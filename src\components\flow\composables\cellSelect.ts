import { Selection } from '@antv/x6-plugin-selection';
import { type Graph } from "@antv/x6";

export default function (graph: Graph) {
  graph.use(
    new Selection({
      enabled: true,
      pointerEvents: 'none',
      showNodeSelectionBox: true,
    }),
  )
  graph.on('cell:selected', function (data) {
    let removeBtnCfg;
    const cell = data.cell
    if (cell.isEdge()) {
      cell.attr('line', {
        strokeWidth: 3
      });
      removeBtnCfg = {
        distance: '30%',
        color: '#A0331F'
      };
      cell.addTools({
        name: 'button-remove', // 工具名称
        args: removeBtnCfg // 工具对应的参数
      });
    }
  });

  graph.on('cell:unselected', (data) => {
    const cell = data.cell;
    if (cell.isEdge()) {
      cell.attr('line', {
        strokeWidth: 1
      });
      cell.removeTools()
    }
  });
}
