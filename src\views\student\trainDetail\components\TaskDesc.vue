<template>
  <el-dialog :class='theme === "light" ? "cus-dialog-dgth" : "cus-dialog"' :model-value='visible' title='任务说明'
    width='750px' :modal="false" :before-close='handleClose'>
    <template #header>
      <div :class="theme === 'light' ? 'section-title-light' : 'section-title'">
        <div class="title-label">{{ title }}</div>
      </div>
    </template>
    <div class="task-desc">
      <RichPreview class="content" :str="taskDesc">
      </RichPreview>
    </div>
  </el-dialog>
</template>
<script setup lang='ts'>
import RichPreview from '@/components/global/RichPreview.vue'
import { computed } from 'vue'
import { useStore } from 'vuex';
const store = useStore()
const theme = computed(() => {
  return store.getters.theme
})
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  taskDesc: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: '任务说明'
  }
})
const emits = defineEmits(['update:visible'])
const handleClose = () => {
  emits('update:visible')
}
</script>

<style scoped lang='scss'>
.task-desc {
  max-height: 60vh;
  overflow-y: auto;
}
</style>
