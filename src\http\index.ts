import axios from 'axios'
import qs from 'qs'
import { ElMessage } from 'element-plus'
import { isJSON } from '@/utils/tools'
import store from '@/store'
import router from '@/router';

import type { AxiosRequestConfig, AxiosInstance, AxiosPromise } from 'axios'
interface httpConfig extends AxiosRequestConfig {
  upload?: boolean,
  download?: boolean,
  useJson?: boolean,
}
interface instance extends AxiosInstance {
  (config: httpConfig): AxiosPromise;
}

const service: instance = axios.create({
  baseURL: "",
  transformResponse: [function (data: any) {
    return data
  }],
  paramsSerializer: (params: any) => {
    return qs.stringify(params)
  },
  timeout: 50000,
  withCredentials: true,
  responseType: 'json',
  xsrfCookieName: 'XSRF-TOKEN',
  xsrfHeaderName: 'X-XSRF-TOKEN',
  maxContentLength: 2000,
  validateStatus: (status: number) => {
    return status >= 200 && status < 510
  },
})

// http请求拦截器
service.interceptors.request.use((config: any) => {
  const token = store.getters.token
  if (token) {
    config.headers.token = token
  }

  // 从路由获取 platformCode 并添加到请求参数中
  const platformCode = router.currentRoute.value.query.platformCode
  if (platformCode) {
    if (config.method?.toLowerCase() === 'get') {
      config.params = {
        ...config.params,
        courseCode: platformCode
      }
    } else {
      config.data = {
        ...config.data,
        courseCode: platformCode
      }
    }
  }

  if (config.download) {
    config.responseType = 'blob';
  }
  if (config.upload) {
    config.headers['Content-Type'] = 'multipart/form-data';
    config.transformRequest = [function (data: Record<string, any>) {
      const oData = new FormData();
      for (const k in data) {
        if (Array.isArray(data[k])) {
          data[k].forEach((item: any) => {
            oData.append(k, item);
          })
        } else {
          oData.append(k, data[k]);
        }
      }
      return oData
    }]
  } else {
    config.transformRequest = [function (data: string) {
      if (config.headers['Content-Type'] != 'application/json;charset=UTF-8') {
        data = qs.stringify(data)
      }
      return data
    }]
  }

  if (config.useJson) {
    config.headers['Content-Type'] = 'application/json;charset=UTF-8'
    config.transformRequest = [function (data: string) {
      return JSON.stringify(data)
    }]
  }
  return config
}, error => {
  ElMessage.error('加载超时');
  return Promise.reject(error)
})


// http响应拦截器
service.interceptors.response.use((response: any) => {
  if (response.data && (typeof response.data == 'string') && isJSON(response.data)) {
    response.data = JSON.parse(response.data)
  }
  if (response.status === 429) { //频繁请求处理
    response = {
      data: {
        code: 429,
        msg: '请勿频繁重复点击'
      }
    }
    return response
  }
  switch (response.status) {
    case 401: //token 过期
      ElMessage({
        message: "未授权，请重新登录！",
        type: "error",
        duration: 1500,
        onClose: function () {
          // store.dispatch('userLogout');
          router.replace('/404')
        }
      })
      router.replace('/404')
      return;
    case 403:
      //没有页面操作权限，直接跳转到404页面
      ElMessage({
        message: '很抱歉,您没有该页面的操作权限！',
        type: "error",
        duration: 1500,
        onClose: function () {
          router.replace('/404')
        }
      })
      break;

    default:
      break;
  }
  return response
}, error => {
  if (error && error.response) {
    switch (error.response.status) {
      case 400:
        error.message = '错误请求'
        break
      case 401:
        error.message = '未授权，请重新登录'
        break
      case 403:
        error.message = '拒绝访问'
        break
      case 404:
        error.message = '请求错误，未找到该资源'
        break
      case 405:
        error.message = '请求方法为允许'
        break
      case 408:
        error.message = '请求超时'
        break
      case 500:
        error.message = '服务端错误'
        break
      case 501:
        error.message = '网络未实现'
        break
      case 502:
        error.message = '网络错误'
        break
      case 503:
        error.message = '服务不可用'
        break
      case 504:
        error.message = '网络超时'
        break
      case 505:
        error.message = 'http版本不支持该请求'
        break
      default:
        error.message = `连接错误${error.response.status}`
    }
  } else {
    error.message = '网络出现问题，请稍后再试！'
  }
  return Promise.reject(error)
})

export default service
