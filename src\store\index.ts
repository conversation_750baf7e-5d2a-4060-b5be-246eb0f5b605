import { createStore } from 'vuex'
import getters from './getters'
import user from "@/store/modules/user"
import app from "@/store/modules/app"
import createPersistedState from "vuex-persistedstate"
import appSettings from "@/settings"

export default createStore({
  plugins: [
    createPersistedState({
      reducer(value: any) {
        return {
          app: {
            token: value.app.token,
            theme: value.app.theme
          },
          user: {
            userInfo: value.user.userInfo,
            productInfoList: value.user.productInfoList,
          }
        }
      },
      key: appSettings.series.toLowerCase() + 'Vuex'
    })
  ],
  modules: {
    app,
    user
  },
  getters
})

