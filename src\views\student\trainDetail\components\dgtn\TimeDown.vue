<template>
  <div class="time-down-container">
    <div class="time-down-content">
      <div class="time-labels">
        <span class="time-label start">0</span>
        <span class="time-value">{{ formatTimeDisplay(remainingTime) }}</span>
        <span class="time-label end">{{ formatTimeDisplay(totalSeconds) }}</span>
      </div>
      <div class="arc-progress-container">
        <svg :viewBox="`0 0 ${svgWidth} ${svgHeight}`" class="progress-svg">
          <defs>
            <linearGradient id="progress-gradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" style="stop-color:#4ee7e5" />
              <stop offset="100%" style="stop-color:#21c0be" />
            </linearGradient>
          </defs>
          <path :d="arcPathD" class="arc-path-bg" :stroke-width="strokeWidth" stroke-linecap="round" />
          <path :d="arcPathD" class="arc-path-progress" :stroke-width="strokeWidth" :style="progressStyle"
            stroke-linecap="round" />
        </svg>
        <div class="progress-indicator" :style="indicatorStyle"></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  totalSeconds: {
    type: Number,
    default: 30
  },
  initialSeconds: {
    type: Number,
    default: 30
  }
});

const emits = defineEmits(['time-end']);

const isStart = ref(false)

// SVG and Arc constants
const svgWidth = 154;
const strokeWidth = 4;
const radius = 190;
const startAngle = -110; // -150 degrees (left side)
const endAngle = -70;   // -30 degrees (right side)
const svgHeight = 20;
const cx = svgWidth / 2;
const cy = radius;

// Helper to convert polar to cartesian coordinates
const polarToCartesian = (centerX: number, centerY: number, r: number, angleInDegrees: number) => {
  const angleInRadians = (angleInDegrees * Math.PI) / 180.0;
  return {
    x: centerX + r * Math.cos(angleInRadians),
    y: centerY + r * Math.sin(angleInRadians) + 2,
  };
};

// SVG Arc Path 'd' attribute
const arcPathD = computed(() => {
  const start = polarToCartesian(cx, cy, radius, startAngle);
  const end = polarToCartesian(cx, cy, radius, endAngle);
  return [
    'M', start.x, start.y,
    'A', radius, radius, 0, 0, 1, end.x, end.y
  ].join(' ');
});

// Arc length
const totalAngle = endAngle - startAngle;
const arcLength = computed(() => (totalAngle / 360) * 2 * Math.PI * radius);

// 剩余时间（秒）
const remainingTime = ref(props.initialSeconds);
// 总时间（秒）
const totalTime = computed(() => props.totalSeconds);

// 进度百分比
const progressPercentage = computed(() => {
  if (totalTime.value === 0) return 0;
  return (remainingTime.value / totalTime.value) * 100;
});

// Progress path style
const progressStyle = computed(() => ({
  strokeDasharray: arcLength.value,
  strokeDashoffset: arcLength.value * (1 - progressPercentage.value / 100),
}));

// Indicator style
const indicatorStyle = computed(() => {
  const currentAngle = startAngle + (progressPercentage.value / 100) * totalAngle;
  const { x, y } = polarToCartesian(cx, cy, radius, currentAngle);
  return {
    left: `${(x / svgWidth) * 100}%`,
    top: `${(y / svgHeight) * 100}%`,
  };
});

// 格式化时间为可读格式 (2h30min30s)
const formatTimeDisplay = (seconds: number) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  let result = '';

  if (hours > 0) {
    result += `${hours}h`;
  }

  if (minutes > 0) {
    result += `${minutes}m`;
  }

  if (secs > 0 || (hours === 0 && minutes === 0)) {
    result += `${secs}s`;
  }

  return result;
};

// 倒计时逻辑
let timer: number | null = null;

const startTimer = () => {
  timer = window.setInterval(() => {
    if (remainingTime.value > 0) {
      if (!isStart.value) {
        isStart.value = true
      }
      remainingTime.value -= 1;
    } else {
      if (isStart.value) {
        emits('time-end');
        isStart.value = false
      }
      if (timer) {
        clearInterval(timer);
      }
    }
  }, 1000);
};

onMounted(() => {
  startTimer();
});

onUnmounted(() => {
  if (timer) {
    clearInterval(timer);
  }
});
</script>

<style scoped lang="scss">
.time-down-container {
  width: 100%;
  max-width: 200px;
  position: absolute;
  top: 8px;
  right: 16px;
  z-index: 99;
}

.time-down-content {
  position: relative;
}

.time-label {
  font-size: 14px;
  color: #666;
  position: absolute;
  bottom: 10px;
  font-size: 10px;

  &.start {
    left: 0;
  }

  &.end {
    right: 0;
  }
}

.time-value {
  position: absolute;
  bottom: 50%;
  font-size: 16px;
  color: #333;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  color: #333333;
  font-size: 12px;
}

.arc-progress-container {
  position: relative;
  width: 100%;
  max-width: 200px;
  margin: 10px auto 0;
}

.progress-svg {
  width: 100%;
  display: block;
}

.arc-path-bg {
  fill: none;
  stroke: #e0e0e0;
  stroke-linecap: round;
}

.arc-path-progress {
  fill: none;
  stroke: url(#progress-gradient);
  stroke-linecap: round;
  transition: stroke-dashoffset 1s linear;
}

.progress-indicator {
  position: absolute;
  transform: translate(-50%, -50%);
  width: 16px;
  height: 16px;
  background-color: #21c0be;
  border: 4px solid #fff;
  border-radius: 50%;
  transition: left 1s linear, top 1s linear;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}
</style>
