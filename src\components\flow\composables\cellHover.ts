import { type Graph, Node } from "@antv/x6";


const changePortsVisible = (node: Node, visible: boolean) => {
  const ports = document.querySelectorAll(`g[data-cell-id="${node.id}"] .x6-port-body`)
  ports.forEach((port) => {
    (port as SVGGElement).style.visibility = visible ? 'visible' : 'hidden';
  });
};

export default function cellHover(graph: Graph) {
  // 监听鼠标进入节点事件
  graph.on('node:mouseenter', ({ node }) => {
    changePortsVisible(node, true);
  });

  // 监听鼠标离开节点事件
  graph.on('node:mouseleave', ({ node }) => {
    changePortsVisible(node, false);
  });

}
