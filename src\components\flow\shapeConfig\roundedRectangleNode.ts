import { Shape } from "@antv/x6"
import { lightPorts } from "./staticConfig"

class RoundedRectangleNode extends Shape.Rect {
}

RoundedRectangleNode.config({
  width: 110,
  height: 34,
  zIndex: 100,
  markup: [
    {
      tagName: 'rect',
      selector: 'main',
      attrs: {
        fill: '#4ad189'
      }
    },
    {
      tagName: 'title',
      selector: 'title',
    },
    {
      tagName: 'text',
      selector: 'label',
      attrs: {
        fill: '#000',
        'font-size': 14,
        'text-anchor': 'middle',
        'pointer-events': 'none',
      }
    }
  ],
  data: {
    fontColor: '#ffffff',
    backgroundColor: '#4ad189',
    borderColor: '#29a663'
  },
  attrs: {
    main: {
      stroke: '#29a663',
      fill: '#4ad189',
      refWidth: '100%',
      refHeight: '100%',
      rx: 17,
      ry: 17
    },
    title: {
      text: '',
    },
    label: {
      text: '',
      fill: '#ffffff',
      strokeWidth: 1,
      fontSize: 14,
      textWrap: {
        refWidth: '90%', // 宽度减少 10px
        height: '80%', // 高度为参照元素高度的一半
        ellipsis: true, // 文本超出显示范围时，自动添加省略号
        breakWord: true, // 是否截断单词
      },
    }
  },
  ports: { ...lightPorts }
})
export default RoundedRectangleNode
