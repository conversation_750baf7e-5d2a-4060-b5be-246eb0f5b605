<template>
  <div class="teacher-wrap">
    <div class="teacher-main">
      <BackTop>
        <template #default>
          <el-button type="primary" @click="switchStatus">
            <el-icon>
              <Switch />
            </el-icon>
            {{ switchStatusText }}
          </el-button>
        </template>
      </BackTop>

      <div class="head-search">
        <el-form :inline="true" :model="ninSearchForm">
          <el-form-item label="所属班级：">
            <el-select v-model="ninSearchForm.classId" placeholder="请选择班级" style="width: 120px" clearable>
              <el-option :value="item.classId" :label="item.className" v-for="item of classList" :key="item.classId" />
            </el-select>
          </el-form-item>
          <el-form-item label="账号：">
            <el-input v-model="ninSearchForm.studentCode" placeholder="请输入账号" class="mini" />
          </el-form-item>
          <el-form-item label="姓名：">
            <el-input v-model="ninSearchForm.studentName" placeholder="请输入姓名" class="mini" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleCurrentChange(1, getTableList, true)">
              查询
            </el-button>
            <el-button type="primary" plain :icon="Plus" :disabled="disableTrain" @click="addVisible = true;">
              新增学生
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="head-operation">
        <el-button type="danger" plain :disabled="buttonDisabled || disableTrain"
          @click="handleBatchDelete">批量删除</el-button>
      </div>
      <div>
        <el-table :data="tableData" border @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="40" />
          <el-table-column label="序号" type="index" width="55" />
          <el-table-column label="姓名" prop="studentName" />
          <el-table-column label="账号" prop="studentCode" />
          <el-table-column label="所属班级" prop="className" />
          <el-table-column label="加入时间" prop="createTime" />
          <el-table-column label="操作" width="100">
            <template #default="scope">
              <el-link :underline="false" type="primary" :disabled="disableTrain" @click="handleSingleDelete(scope.row)">
                删除
              </el-link>
            </template>
          </el-table-column>
        </el-table>
        <Pagination :total="pageTotal" :pageSize="pageSize" :currentPage="pageNum"
          @currentChange="handleCurrentChange($event, getTableList)" @sizeChange="handleSizeChange($event, getTableList)">
        </Pagination>
      </div>
    </div>
  </div>

  <AddStudent :visible="addVisible" @close="addVisible = false" @submit="addVisible = false; getTableList()" />
</template>

<script setup lang="ts">
import BackTop from './components/BackTop.vue';
import * as api from "@/api/teacher";
import { Search, Plus, Switch } from '@element-plus/icons-vue';
import Pagination from "@/components/global/Pagination.vue";
import { commonConfirmMethods, TableOptions } from "@/utils/tableOptions";
import { computed, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import AddStudent from './components/AddStudent.vue';
import { ElMessage, ElMessageBox } from 'element-plus';

interface TrainingRecord {
  trainId: string;
  studentId: string;
  studentCode: string;
  studentName: string;
  classId: string;
  className: string;
  createTime: string;
}

const t2 = new TableOptions();
const { loading, pageNum, pageSize, pageTotal, tableData, ninSearchForm, tempSearchForm } = t2;
const { handleCurrentChange, handleSizeChange } = t2;


const addVisible = ref(false),
  ids = ref<string[]>([]),
  route = useRoute(),
  router = useRouter(),
  classList = ref<{ classId: string, className: string }[]>([]);

const trainId = ref(route.query.trainId as string)
const switchStatusText = computed(() => {
  return route.query.status === "0" ? '启用' : '重新设置'
})

const disableTrain = computed(() => {
  return route.query.status === "1"
})

const buttonDisabled = computed(() => {
  return !ids.value.length
})
const getClassList = async () => {
  const { data: { code, data } } = await api.getClassByTrain({ trainId: trainId.value });
  if (code === 200) {
    classList.value = data
  }
}

const getTableList = async () => {
  try {
    loading.value = true;
    const params: Record<string, string | number> = {
      page: pageNum.value,
      size: pageSize.value,
      trainId: trainId.value,
      ...tempSearchForm.value
    };
    for (const key in tempSearchForm.value) {
      if (!tempSearchForm.value[key]) {
        delete params[key];
      }
    }
    const { data: { code, msg, data } } = await api.getStudentList(params);
    if (code === 200) {
      tableData.value = data.list;
      pageTotal.value = parseInt(data.total);
    } else {
      ElMessage.error(msg)
    }
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}
const handleSingleDelete = (row: TrainingRecord) => {
  const param = { trainId: trainId.value, studentIds: `[${row.studentId}]` };
  commonConfirmMethods(
    api,
    "delTrainStudent",
    param,
    "删除成功！",
    "你确定要删除吗？",
    () => { getTableList() }
  );
}
const handleBatchDelete = () => {
  const param = { studentIds: JSON.stringify(ids.value), trainId: trainId.value };
  commonConfirmMethods(
    api,
    "delTrainStudent",
    param,
    "批量删除成功！",
    "你确定要批量删除吗？",
    () => { getTableList() }
  );
}
const handleSelectionChange = (val: TrainingRecord[]) => {
  ids.value = val.map((item) => item.studentId);
};

const switchStatusApi = async (params: { trainId: string, status: number }) => {
  loading.value = true
  const { data: { code, msg } } = await api.stuStartUp(params);
  loading.value = false
  if (code === 200) {
    router.replace({
      path: route.path,
      query: {
        ...route.query,
        status: params.status,
        stamp: new Date().getTime().toString()
      },
    })
  } else {
    ElMessage.error(msg)
  }
}
const switchStatus = async () => {
  try {
    const status = Number(route.query.status);
    const params = {
      trainId: trainId.value,
      status: status ? 0 : 1
    }
    const tipText = status ? '重新设置将清空原有学生的做题数据，确定进行重新设置吗？' : '启用后本综合实训将按您设置的学生名单开始执行，且不可修改。确认启用吗？'
    ElMessageBox.confirm(
      tipText,
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    ).then(async () => {
      switchStatusApi(params)
    }).catch(() => { })
  } catch (error) {
    console.log(error)
  }
}


watch(() => route.query, () => {
  getClassList()
  getTableList()
}, { deep: true, immediate: true })
</script>

<style lang="scss" scoped>
@use "@/styles/teacher";
</style>
