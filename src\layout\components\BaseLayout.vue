<template>
  <div class="base-layout">
    <Background></Background>
    <div class="header">
      <slot name="header"></slot>
    </div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import Background from './Background.vue'

</script>

<style scoped lang="scss">
.base-layout {
  @include flex(column, center, center);
  overflow: hidden;
  width: 100vw;
  height: 100vh;

  .header {
    width: 100%;
    height: 64px;
    background: url('@/assets/images/bg-header.png') no-repeat;
    background-size: 100% 100%;
    flex-shrink: 0;
  }

  .content {
    width: 100%;
    flex: 1;
    min-height: 0;
    overflow: hidden;
    background: center bottom url('@/assets/images/bg-bottom.png') no-repeat;
    background-size: 100% 32px;
    padding-bottom: 32px;
  }
}
</style>
