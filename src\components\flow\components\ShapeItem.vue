<template>
  <div class="shape-item">
    <div :class="['shape', item.type]">
      <span>{{ item.name }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { type FlowShape } from '@/components/flow/hooks/useFlowDesign'
defineProps<{
  item: FlowShape
}>()
</script>

<style scoped lang="scss">
.shape-item {
  cursor: move;
  color: #001E22;

  .shape {
    height: 34px;
    @include flex(row, center, center, nowrap);
    font-size: 14px;
    color: #fff;
    cursor: pointer;
    width: 104px;

    &.start,
    &.end {
      background-color: #4ad189;
      border-radius: 17px;
      box-shadow: 0px 1px 0px 0px rgba(0, 0, 0, 0.1);
      border: 2px solid #29a663;
    }

    &.process {
      background-color: #677cf6;
      border: 2px solid #31449e;
    }

    &.text {
      background-color: #f180f0;
      border: 2px solid #d007cf;
    }

    &.decision {
      width: 96px;
      height: 64px;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
        background-color: #ffa272;
        z-index: 1;
      }

      &::after {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);
        background-color: #da7b48;
        z-index: 0;
      }

      span {
        position: relative;
        z-index: 2;
        color: #fff;
      }
    }
  }
}
</style>
