import { Button } from '@antv/x6/es/registry/tool/button'
import { Node, CellView } from '@antv/x6'

// 扩展Button.Options类型，但不继承onClick
type ButtonBaseOptions = Omit<Button.Options, 'onClick'>

interface CustomDeleteOptions extends ButtonBaseOptions {
  onClick?: (node: Node) => void
  size?: number
  fill?: string
  stroke?: string
}

const MyRemoveButton = Button.define({
  markup: [
    {
      tagName: 'g',
      selector: 'button-group',
      children: [
        {
          tagName: 'circle',
          selector: 'bg',
          attrs: {
            r: 8,
            fill: '#ff0000',
            strokeWidth: 1,
          },
        },
        {
          tagName: 'path',
          selector: 'cross',
          attrs: {
            d: 'M -4 -0.5 L 4 -0.5',
            stroke: '#fff',
            strokeWidth: 2,
            strokeLinecap: 'round',
          },
        }
      ]
    }
  ],

  // 初始化时应用自定义属性
  attrs(cellView: CellView, args: CustomDeleteOptions) {
    const size = args.size || 8
    const fill = args.fill || '#ff0000'
    const stroke = args.stroke || '#fff'

    return {
      'circle': {
        r: size,
        fill: fill,
      },
      'path': {
        stroke: stroke,
      }
    }
  },

  // 使用any临时解决类型问题
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onClick(args: any) {
    const options = args.view.options as CustomDeleteOptions
    if (options?.onClick) {
      options.onClick(args.cell)
    } else {
      args.cell.remove()
    }
  }
})

export default MyRemoveButton


