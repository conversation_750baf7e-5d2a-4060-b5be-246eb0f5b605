<template>
  <div class="key-point-control">
    <OptionList v-model:options="options" :used-option-ids="usedOptionIds" />
    <div class="control-right">
      <div class="right-header">
        <span>波士顿矩阵分析</span>
        <span class="tip-text">请先设置选项后再拖拽到波士顿矩阵中</span>
      </div>
      <div class="boston-matrix-wrapper">
        <!-- 矩阵标题 -->
        <div class="matrix-title">
          <h3>波士顿矩阵 (BCG Matrix)</h3>
        </div>

        <!-- 波士顿矩阵 -->
        <div class="boston-matrix">
          <div class="matrix-row">
            <!-- 左上象限 -->
            <div class="matrix-cell question-marks" @drop="handleCellDrop($event, 0)" @dragover.prevent
              @dragenter="handleDragEnter($event)" @dragleave="handleDragLeave($event)">
              <div class="cell-header">
                <el-input v-model.trim="matrixData[0].name" placeholder="请输入象限名称" class="quadrant-input" size="small" />
              </div>
              <div class="cell-content">
                <el-scrollbar>
                  <div v-if="!matrixData[0].contents.length" class="content-placeholder">
                    请将产品拖动到此区域
                  </div>
                  <div v-else class="content-items">
                    <div v-for="(item, itemIndex) in matrixData[0].contents" :key="itemIndex" class="content-item"
                      draggable="true" @dragstart="handleItemDragStart($event, item)" :title="item">
                      {{ item }}
                      <div class="delete-icon" @click.stop="handleDeleteItem(0, itemIndex)">
                        <el-icon>
                          <Close />
                        </el-icon>
                      </div>
                    </div>
                  </div>
                </el-scrollbar>
              </div>
            </div>

            <!-- 右上象限 -->
            <div class="matrix-cell stars" @drop="handleCellDrop($event, 1)" @dragover.prevent
              @dragenter="handleDragEnter($event)" @dragleave="handleDragLeave($event)">
              <div class="cell-header">
                <el-input v-model="matrixData[1].name" placeholder="请输入象限名称" class="quadrant-input" size="small" />
              </div>
              <div class="cell-content">
                <el-scrollbar>
                  <div v-if="!matrixData[1].contents.length" class="content-placeholder">
                    请将产品拖动到此区域
                  </div>
                  <div v-else class="content-items">

                    <div v-for="(item, itemIndex) in matrixData[1].contents" :key="itemIndex" class="content-item"
                      draggable="true" @dragstart="handleItemDragStart($event, item)" :title="item">
                      {{ item }}
                      <div class="delete-icon" @click.stop="handleDeleteItem(1, itemIndex)">
                        <el-icon>
                          <Close />
                        </el-icon>
                      </div>
                    </div>
                  </div>
                </el-scrollbar>
              </div>
            </div>
          </div>

          <div class="matrix-row">
            <!-- 左下象限 -->
            <div class="matrix-cell dogs" @drop="handleCellDrop($event, 2)" @dragover.prevent
              @dragenter="handleDragEnter($event)" @dragleave="handleDragLeave($event)">
              <div class="cell-header">
                <el-input v-model="matrixData[2].name" placeholder="请输入象限名称" class="quadrant-input" size="small" />
              </div>
              <div class="cell-content">
                <el-scrollbar>

                  <div v-if="!matrixData[2].contents.length" class="content-placeholder">
                    请将产品拖动到此区域
                  </div>
                  <div v-else class="content-items">

                    <div v-for="(item, itemIndex) in matrixData[2].contents" :key="itemIndex" class="content-item"
                      draggable="true" @dragstart="handleItemDragStart($event, item)" :title="item">
                      {{ item }}
                      <div class="delete-icon" @click.stop="handleDeleteItem(2, itemIndex)">
                        <el-icon>
                          <Close />
                        </el-icon>
                      </div>
                    </div>
                  </div>
                </el-scrollbar>
              </div>
            </div>

            <!-- 右下象限 -->
            <div class="matrix-cell cash-cows" @drop="handleCellDrop($event, 3)" @dragover.prevent
              @dragenter="handleDragEnter($event)" @dragleave="handleDragLeave($event)">
              <div class="cell-header">
                <el-input v-model="matrixData[3].name" placeholder="请输入象限名称" class="quadrant-input" size="small" />
              </div>
              <div class="cell-content">
                <el-scrollbar>
                  <div v-if="!matrixData[3].contents.length" class="content-placeholder">
                    请将产品拖动到此区域
                  </div>
                  <div v-else class="content-items">
                    <div v-for="(item, itemIndex) in matrixData[3].contents" :key="itemIndex" class="content-item"
                      draggable="true" @dragstart="handleItemDragStart($event, item)" :title="item">
                      {{ item }}
                      <div class="delete-icon" @click.stop="handleDeleteItem(3, itemIndex)">
                        <el-icon>
                          <Close />
                        </el-icon>
                      </div>
                    </div>
                  </div>
                </el-scrollbar>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Close } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import OptionList from '../OptionList.vue'
import type { OptionItem } from '../../../types'

const props = defineProps({
  initContent: {
    type: String,
    default: ''
  },
  taskOptions: {
    type: String,
    default: ''
  }
})

interface MatrixQuadrant {
  name: string
  contents: string[]
}

// 合并配置和数据到单一数组
const matrixData = ref<MatrixQuadrant[]>([
  { name: '', contents: [] },
  { name: '', contents: [] },
  { name: '', contents: [] },
  { name: '', contents: [] }
])

const options = ref<OptionItem[]>([])

const optionsMap = computed(() => {
  const map = new Map<string, string>()
  options.value.forEach(item => {
    map.set(item.name, item.id)
  })
  return map
})

// 计算已使用的选项ID集合
const usedOptionIds = computed(() => {
  const ids = new Set<string>()

  // 遍历所有矩阵象限中放置的选项
  matrixData.value.forEach(item => {
    item.contents.forEach(optName => {
      const id = optionsMap.value.get(optName)
      if (id) ids.add(id)
    })
  })

  return ids
})

// 处理拖拽进入
const handleDragEnter = (event: DragEvent) => {
  const cell = event.currentTarget as HTMLElement
  cell.classList.add('highlight-drop')
}

// 处理拖拽离开
const handleDragLeave = (event: DragEvent) => {
  const cell = event.currentTarget as HTMLElement
  cell.classList.remove('highlight-drop')
}

// 处理拖拽放置
const handleCellDrop = (event: DragEvent, index: number) => {
  const data = event.dataTransfer?.getData('text/plain')
  if (!data) return

  const option = JSON.parse(data) as OptionItem

  // 移除其他位置的相同选项
  removeOptionFromOtherQuadrants(option.name)

  // 设置到目标象限
  matrixData.value[index].contents.push(option.name)

  const cell = event.currentTarget as HTMLElement
  cell.classList.remove('highlight-drop')
}

// 移除选项从其他象限
const removeOptionFromOtherQuadrants = (optionName: string) => {
  matrixData.value.forEach(item => {
    const index = item.contents.indexOf(optionName)
    if (index !== -1) {
      item.contents.splice(index, 1)
    }
  })
}

// 拖拽开始处理
const handleItemDragStart = (event: DragEvent, itemName: string) => {
  const option = options.value.find(opt => opt.name === itemName)
  if (option) {
    event.dataTransfer?.setData('text/plain', JSON.stringify(option))
    createDragImage(event, itemName)
  }
}

// 创建拖拽图像
const createDragImage = (event: DragEvent, text: string) => {
  const dragImage = document.createElement('div')
  dragImage.textContent = text
  dragImage.style.cssText = `
    position: absolute;
    left: -9999px;
    background: rgb(101, 151, 247);
    color: #fff;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: var(--font-size-small);
    white-space: nowrap;
    pointer-events: none;
    z-index: 9999;
    width: 200px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
  `
  document.body.appendChild(dragImage)
  event.dataTransfer!.setDragImage(dragImage, 0, 0)

  setTimeout(() => {
    document.body.removeChild(dragImage)
  }, 0)
}

// 删除项目
const handleDeleteItem = (quadrantIndex: number, itemIndex: number) => {
  matrixData.value[quadrantIndex].contents.splice(itemIndex, 1)
}

// 验证函数
const validate = () => {
  return new Promise((resolve, reject) => {
    if (options.value.length === 0) {
      ElMessage.error('请添加选项')
      reject(new Error('请添加选项'))
      return
    }

    const hasItems = matrixData.value.some(item => item.contents.length > 0 || !item.name)
    if (!hasItems) {
      ElMessage.error('请在波士顿矩阵中添加至少一个产品')
      reject(new Error('请在波士顿矩阵中添加产品'))
      return
    }

    resolve({
      initContent: JSON.stringify(matrixData.value),
      taskOptions: JSON.stringify(options.value)
    })
  })
}

onMounted(() => {
  if (props.initContent) {
    try {
      const parsedData = JSON.parse(props.initContent)
      matrixData.value = parsedData
    } catch (e) {
      console.error('解析初始数据失败', e)
    }
  }

  options.value = props.taskOptions ? JSON.parse(props.taskOptions) : []
})

defineExpose({
  validate
})
</script>

<style scoped lang="scss">
.key-point-control {
  @include flex(row, flex-start, flex-start, nowrap);
  gap: 20px;
  position: relative;

  .control-right {
    flex: 1;
    min-width: 0;
    @include flex(column, flex-start, stretch, nowrap);
    gap: 16px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 14px;
    border-radius: 6px;
    max-height: 80vh;
    overflow-y: auto;

    .right-header {
      @include flex(row, flex-start, flex-start, nowrap);
      gap: 14px;

      span {
        font-weight: bold;
        color: var(--el-text-color-primary);
      }

      .tip-text {
        color: var(--el-text-color-secondary);
        font-size: 14px;
        font-weight: normal;
      }
    }
  }

  .boston-matrix-wrapper {
    @include flex(column, flex-start, stretch, nowrap);
    gap: 20px;

    .matrix-title {
      text-align: center;

      h3 {
        margin: 0;
        color: var(--el-text-color-primary);
        font-size: 20px;
        font-weight: bold;
      }
    }

    .boston-matrix {
      display: grid;
      grid-template-rows: 1fr 1fr;
      gap: 2px;
      border: 2px solid #409EFF;
      border-radius: 8px;
      overflow: hidden;

      .matrix-row {
        @include flex(row, flex-start, stretch, nowrap);
        gap: 2px;
      }

      .matrix-cell {
        padding: 16px;
        @include flex(column, flex-start, stretch, nowrap);
        gap: 12px;
        transition: all 0.2s ease;
        position: relative;
        width: 50%;

        &.highlight-drop {
          box-shadow: inset 0 0 0 3px #67C23A;
          transform: scale(1.02);
        }

        .cell-header {
          text-align: center;
          width: 100%;

          .quadrant-input {
            margin-bottom: 4px;

            .el-input__wrapper {
              background: rgba(255, 255, 255, 0.2);
              box-shadow: none;
              border: 1px solid rgba(255, 255, 255, 0.3);
            }

            :deep(.el-input__inner) {
              text-align: center;

              &::placeholder {
                color: rgba(255, 255, 255, 0.7);
              }
            }
          }
        }

        .cell-content {
          height: 120px;
          border: 2px dashed rgba(255, 255, 255, 0.3);
          border-radius: 6px;
          overflow: hidden;

          .content-placeholder {
            height: 100px;
            @include flex(row, center, center, nowrap);
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            text-align: center;

          }

          .content-items {
            gap: 8px;
            @include flex(row, flex-start, flex-start, wrap);
            padding: 10px;

            .content-item {
              width: calc(50% - 4px);
              background: rgba(255, 255, 255, 0.2);
              color: white;
              padding: 6px 12px;
              border-radius: 4px;
              cursor: move;
              position: relative;
              font-size: 14px;
              border: 1px solid rgba(255, 255, 255, 0.3);
              @include text-ellipsis();

              &:hover {
                background: rgba(255, 255, 255, 0.3);

                .delete-icon {
                  opacity: 1;
                }
              }
            }
          }
        }

        // 不同象限的背景色
        &.stars {
          background: linear-gradient(135deg, #FFD700, #FFA500); // 金色渐变
        }

        &.question-marks {
          background: linear-gradient(135deg, #FF6B6B, #FF8E8E); // 红色渐变
        }

        &.cash-cows {
          background: linear-gradient(135deg, #4ECDC4, #44A08D); // 绿色渐变
        }

        &.dogs {
          background: linear-gradient(135deg, #95A5A6, #7F8C8D); // 灰色渐变
        }
      }
    }
  }

  // 删除图标样式
  .delete-icon {
    position: absolute;
    top: 50%;
    right: 2px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    transition: all 0.2s ease;
    color: white;
    background-color: rgba(0, 0, 0, 0.4);
    transform: translateY(-50%);
    @include flex(row, center, center, nowrap);

    &:hover {
      transform: translateY(-50%) scale(1.2);
      background-color: rgba(0, 0, 0, 0.6);
    }

    .el-icon {
      font-size: 10px;
      font-weight: bold;
    }
  }
}
</style>
