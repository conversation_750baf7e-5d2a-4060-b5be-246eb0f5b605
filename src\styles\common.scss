@font-face {
  font-family: "YouSheBiaoTiHei";
  src: url('@/assets/font/YouSheBiaoTi.ttf');
  font-weight: normal;
  font-style: normal;
}

//清除浮动
.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

.fl {
  float: left;
}

.fr {
  float: right;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}


//滚动条
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.06);
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.08);
}

//滚动条滑块
::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: rgba(0, 0, 0, 0.12);
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.2);
}

.section-title {
  padding: 0 20px;
  color: #ffffff;
  position: relative;
  height: 47px;
  display: inline-flex;
  align-items: center;

  &::before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 6px;
    transform: skewX(-20deg);
    width: 100%;
    min-width: 180px;
    height: 22px;
    background: linear-gradient(90deg, #cccccc33 0%, #27f2ef4d 9.75%, #2ba0ad33 19.9%, #11456900 88.5%, #2ba0ad33 100%);
  }

  .title-label {
    margin-right: 8px;
    font-family: "YouSheBiaoTiHei";
    font-size: 36px;
    position: relative;
    z-index: 1;
  }

  .title-desc {
    font-size: 14px;
    padding-top: 12px;
  }

  @media screen and (max-width: 1440px) {
    height: 36px;

    &::before {
      bottom: 0;
    }

    .title-label {
      font-size: 24px;
    }

    .title-desc {
      font-size: 12px;
      padding-top: 8px;
    }
  }
}

.section-title-light {
  height: 48px;
  position: relative;
  width: 100%;
  padding: 0 15px;
  display: flex;
  align-items: center;

  &::after {
    display: inline-block;
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 48px;
    background-image: linear-gradient(to bottom,
        #d7e1fa 0%,
        transparent 100%);
  }

  .title-label {
    position: relative;
    z-index: 1;
    height: 100%;
    z-index: 1;
    border-bottom: 1px solid #c8ddfb;
    position: relative;
    display: flex;
    align-items: center;
    flex: 1;

    &::after {
      display: inline-block;
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 24px;
      height: 3px;
      background-color: #507af1;
    }
  }

  .title-desc {
    z-index: 1;
    font-size: 14px;
  }
}
