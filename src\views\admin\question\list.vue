<template>
  <div class="list">
    <div class="list-header">
      <el-form :inline="true" :model="ninSearchForm">
        <el-form-item label="题型：">
          <el-select class="w-200" v-model="ninSearchForm.questionType" placeholder="不限" clearable>
            <el-option v-for="item in QUESTION_TYPE" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="题目名称：">
          <el-input v-model="ninSearchForm.questionName" placeholder="请输入" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :icon="Search" @click="handleCurrentChange(1, getTableList, true)">查询
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="list-body">

      <div class="list-body-toolbar clearfix">
        <div class="toolbar-right">
          <el-button type="primary" @click="addVisible = true; dialogType = 'add'; editData = {}">
            新增题目
          </el-button>
          <el-button type="danger" @click="batchDel" :disabled="buttonDisabled">
            批量删除
          </el-button>
        </div>
      </div>
      <el-alert type="warning" class="tips" :closable="false" v-if="ids.length">
        已选择
        <b>{{ ids.length }}</b> 项
      </el-alert>
      <el-table :data="tableData" v-loading="loading" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column type="index" label="序号" width="60"></el-table-column>
        <el-table-column prop="questionName" label="题目名称" min-width="120px"></el-table-column>
        <el-table-column prop="categoryName" label="所属分类" min-width="120px"></el-table-column>
        <el-table-column prop="questionType" label="题型" min-width="120px">
          <template #default="scope">
            {{ QUESTION_TYPE_MAP.get(scope.row.questionType) }}
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="120px"></el-table-column>
        <el-table-column prop="createUserCode" label="创建人" min-width="120px"></el-table-column>
        <el-table-column label="操作" width="180">
          <template #default="scope">
            <el-button type="primary" @click="addVisible = true; editData = scope.row; dialogType = 'edit';" link
              :underline="false">编辑</el-button>
            <el-button type="primary" @click="handleContent(scope.row)" link :underline="false">内容管理</el-button>
            <el-button type="danger" link :disabled="scope.row.roleCode == 'super_admin'"
              @click="singleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <Pagination :total="pageTotal" :pageSize="pageSize" :currentPage="pageNum"
        @currentChange="handleCurrentChange($event, getTableList)" @sizeChange="handleSizeChange($event, getTableList)">
      </Pagination>
    </div>
    <AddQuestion v-model:visible="addVisible" :dialog-type="dialogType" :edit-data="editData" @success="getTableList" />
  </div>
</template>
<script setup lang="ts">
import { Search } from "@element-plus/icons-vue";
import AddQuestion from "./components/AddQuestion.vue";
import Pagination from "@/components/global/Pagination.vue";
import { TableOptions } from "@/utils/tableOptions";
import { computed, onMounted, ref, toRefs } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import * as api from "@/api/admin";
import { QUESTION_TYPE } from "@/utils/constant";
import type { Question } from "../types";
import { encodeName } from '@/utils/tools'

const QUESTION_TYPE_MAP = Object.values(QUESTION_TYPE).reduce((acc, cur) => {
  acc.set(cur.value, cur.label);
  return acc;
}, new Map());

const t = new TableOptions();
const {
  loading,
  pageNum,
  pageSize,
  pageTotal,
  tableData,
  ninSearchForm,
  tempSearchForm,
} = toRefs(t);
const router = useRouter();
const route = useRoute();
const { handleCurrentChange, handleSizeChange } = t;
const addVisible = ref(false);
const dialogType = ref("add");
const editData = ref({});


const buttonDisabled = computed(() => {
  return !ids.value.length
})

const getTableList = async () => {
  loading.value = true;
  const params: Record<string, any> = {
    page: pageNum.value,
    size: pageSize.value,
    ...tempSearchForm.value,
  };
  const { data: { code, msg, data } } = await api.getList(params);
  loading.value = false
  if (code === 200) {
    tableData.value = data.list;
    pageTotal.value = data.total;
  } else {
    ElMessage.error(msg)
  }
};
const batchDel = () => {
  const params = {
    questionIds: JSON.stringify(ids.value),
  };
  handleDelete(params, true);
};

const ids = ref<string[]>([]);
const handleSelectionChange = (val: Question[]) => {
  ids.value = val.map((item) => item.questionId);
};

const handleDelete = (params: { questionIds: string }, isBatch = false) => {
  ElMessageBox.confirm(`你确定要${isBatch ? "批量" : ""}删除吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      loading.value = true;
      const { data: { code, msg } } = await api.delBcQuestion(params);
      loading.value = false
      if (code === 200) {
        ElMessage.success("删除成功！");
        getTableList();
      } else {
        ElMessage.error(msg)
      }
    })
    .catch(() => { });
};

const singleDelete = (row: Question) => {
  const params = {
    questionIds: `[${row.questionId}]`,
  };
  handleDelete(params);
};

const handleContent = (row: Question) => {
  router.push({
    path: "/admin/questionContent",
    query: {
      questionId: row.questionId,
      questionName: encodeName(row.questionName),
      questionType: row.questionType,
      questionSecondType: row.questionSecondType,
      platformCode: route.query.platformCode,
      enterpriseId: row.enterpriseId
    }
  })
}

onMounted(() => {
  getTableList();
});
</script>
<style scoped lang="scss">
.list {

  .w-200 {
    width: 200px;
  }

  .list-header {
    padding: 20px 20px 0;
    background-color: #fff;
  }

  .list-body {
    background-color: #fff;
    margin: 20px;
    padding: 20px;
  }

  .list-body-toolbar {
    border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
    padding-bottom: 5px;
  }
}
</style>
