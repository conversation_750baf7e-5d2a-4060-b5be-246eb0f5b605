<template>
  <div class="property-panel" :class="{ 'hidden': !visible }">
    <div class="panel-content">
      <div class="panel-header">
        <div class="panel-title">任务属性</div>
        <el-icon class="close-btn" size="18" @click="handleClose">
          <CloseBold />
        </el-icon>
      </div>

      <div class="panel-section">
        <el-scrollbar>
          <el-form label-width="85px" :disabled="disabled">
            <el-collapse v-model="activeNames">
              <el-collapse-item title="通用" name="1">
                <el-form-item v-if="mode === 'admin' && isNode" label="是否预设">
                  <el-switch v-model="formData.isPreSet" />
                </el-form-item>
                <el-form-item :label="isNode ? '任务名称' : '标签内容'">
                  <el-input v-model="formData.name" :placeholder="isNode ? '请输入任务名称' : '请输入标签内容'" maxlength="50"
                    show-word-limit @change="handleChangeName" />
                </el-form-item>
              </el-collapse-item>
              <el-collapse-item v-if="isNode" title="操作人" name="2">
                <el-form-item label="操作人类型">
                  <el-select v-model="(formData as NodeData).initiatorType" placeholder="请选择发起人类型" clearable
                    @change="handleChangeInitiatorType">
                    <el-option v-for="item in initiatorTypeList" :key="item.value" :label="item.label"
                      :value="item.value" />
                  </el-select>
                </el-form-item>
                <el-form-item v-if="isNode" label="选择操作人">
                  <div class="select-tag-box">
                    <el-scrollbar height="100%">
                      <div class="tag-list">
                        <el-tag type="info" :closable="!disabled" v-for="item in initiatorsName" :key="item.id"
                          @close="handleCloseTag(item)">{{ item.name
                          }}</el-tag>
                      </div>
                      <el-button class="add-btn" color="#e7ebff" circle size="small" @click="handleAddInitiator"
                        :disabled="disabled">
                        <el-icon>
                          <Plus />
                        </el-icon>
                      </el-button>
                    </el-scrollbar>
                  </div>
                </el-form-item>
              </el-collapse-item>
              <el-collapse-item v-if="isNode" title="操作" name="3">
                <el-form-item label="选择表单">
                  <el-select v-model="(formData as NodeData).formId" multiple clearable placeholder="请选择">
                    <el-option v-for="item in formList" :key="item.basicId" :label="item.basicName"
                      :value="item.basicId" />
                  </el-select>
                </el-form-item>
              </el-collapse-item>
              <el-collapse-item title="配置" name="4">
                <template v-if="formData.name">
                  <el-form-item label="字体颜色">
                    <div class="color-display" v-if="disabled">
                      <div class="color-block" :style="{ backgroundColor: formData.fontColor }"></div>
                      <span class="color-text">{{ formData.fontColor }}</span>
                    </div>
                    <el-color-picker v-else v-model="formData.fontColor" @change="handleStyleChange" />
                  </el-form-item>
                  <el-form-item label="背景颜色">
                    <div class="color-display" v-if="disabled">
                      <div class="color-block" :style="{ backgroundColor: formData.backgroundColor }"></div>
                      <span class="color-text">{{ formData.backgroundColor }}</span>
                    </div>
                    <el-color-picker v-else v-model="formData.backgroundColor" @change="handleStyleChange" />
                  </el-form-item>
                  <el-form-item label="边框颜色">
                    <div class="color-display" v-if="disabled">
                      <div class="color-block" :style="{ backgroundColor: formData.borderColor }"></div>
                      <span class="color-text">{{ formData.borderColor }}</span>
                    </div>
                    <el-color-picker v-else v-model="formData.borderColor" @change="handleStyleChange" />
                  </el-form-item></template>
                <el-form-item v-if="isNode" label="字体大小">
                  <div class="number-display" v-if="disabled">
                    <span class="number-text">{{ (formData as NodeData).fontSize }}</span>
                  </div>
                  <el-input-number v-else v-model="(formData as NodeData).fontSize" :min="12" :max="36" :step="1"
                    @change="handleStyleChange" />
                </el-form-item>
                <el-form-item v-if="isEdge" label="线条宽度">
                  <div class="number-display" v-if="disabled">
                    <span class="number-text">{{ (formData as EdgeData).lineWidth }}</span>
                  </div>
                  <el-input-number v-else v-model="(formData as EdgeData).lineWidth" :min="2" :max="8" :step="1"
                    @change="handleStyleChange" />
                </el-form-item>
                <el-form-item v-if="isEdge" label="流动线条">
                  <el-switch v-model="(formData as EdgeData).isAnimated" @change="handleStyleChange" />
                </el-form-item>
                <el-form-item v-if="isEdge" label="线条样式">
                  <el-select v-model="(formData as EdgeData).lineStyle" placeholder="请选择线条样式" clearable
                    @change="handleStyleChange">
                    <el-option v-for="item in lineStyleList" :key="item.value" :label="item.label"
                      :value="item.value" />
                  </el-select>
                </el-form-item>
                <el-form-item v-if="isEdge" label="线条颜色">
                  <div class="color-display" v-if="disabled">
                    <div class="color-block" :style="{ backgroundColor: formData.lineColor }"></div>
                    <span class="color-text">{{ formData.lineColor }}</span>
                  </div>
                  <el-color-picker v-else v-model="(formData as EdgeData).lineColor" @change="handleStyleChange" />
                </el-form-item>
              </el-collapse-item>
            </el-collapse>
          </el-form>
        </el-scrollbar>
      </div>
    </div>
    <TreeSelect v-if="treeDialogVisible" v-model:visible="treeDialogVisible" :treeData="treeData"
      :checkedList="formData.initiators" @change="handleSelectNode" />
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import TreeSelect from './TreeSelect.vue'
import { CloseBold, Plus } from '@element-plus/icons-vue'
import { ref, computed, watch } from 'vue'
import * as api from '@/api/admin'
import type { BaseDataItem, BaseDataItemTree } from '@/views/admin/types'
import type { PropType } from 'vue'
import type { Edge, Cell } from '@antv/x6'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  node: {
    type: Object as PropType<Cell | null>,
    default: () => ({})
  },
  columns: {
    type: Array,
    default: () => ['1', '2', '3', '4']
  },
  disabled: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String as PropType<'admin' | 'answer'>,
    default: 'answer'
  },
  enterpriseId: {
    type: String,
    default: ''
  }
})
const emits = defineEmits(['close'])

const lineStyleList = ref([
  {
    label: '直角',
    value: 'normal'
  },
  {
    label: '圆角',
    value: 'rounded'
  },
  {
    label: '平滑',
    value: 'smooth'
  },
  {
    label: '对称(两线交叉)',
    value: 'jumpover'
  }
])

// 基础样式接口
interface BaseStyle {
  fontColor: string
  backgroundColor: string
  borderColor: string
}

// 节点数据接口
interface NodeData extends BaseStyle {
  isPreSet: boolean
  name: string
  initiatorType: string
  initiators: string[]
  formId: string[]
  fontSize: number
}

// 连线数据接口
interface EdgeData extends BaseStyle {
  name: string
  lineWidth: number
  isAnimated: boolean
  lineStyle: string
  lineColor: string
}

// 统一的表单数据对象
const formData = ref<any>({})

// 是否是节点
const isNode = computed(() => {
  return props.node?.isNode()
})

// 是否是连线
const isEdge = computed(() => {
  return props.node?.isEdge()
})

const initiatorsName = computed(() => {
  if (!isNodeData(formData.value)) return []
  return formData.value.initiators.reduce((acc: { name: string, id: string }[], cur) => {
    const name = allRawDataMap.value.get(cur)?.basicName
    if (name) {
      acc.push({ name, id: cur })
    }
    return acc
  }, [])
})

// 重置表单数据
const resetFormData = () => {
  formData.value = {
    name: '',
    fontColor: '',
    backgroundColor: '',
    borderColor: '',
    fontSize: 14,
    initiatorType: '',
    initiators: [],
    formId: [],
    lineWidth: 2,
    isAnimated: false,
    lineStyle: 'normal',
    lineColor: ''
  }
}


// 处理样式变更
const handleStyleChange = () => {
  if (!props.node) return
  if (isNode.value) {
    const nodeData = formData.value as NodeData
    props.node.setAttrs({
      main: {
        fill: nodeData.backgroundColor,
        stroke: nodeData.borderColor
      },
      label: {
        fill: nodeData.fontColor,
        fontSize: nodeData.fontSize
      }
    })
  } else if (isEdge.value) {
    const edgeData = formData.value as EdgeData
    props.node.setAttrs({
      line: {
        stroke: edgeData.lineColor,
        strokeWidth: edgeData.lineWidth,
        strokeDasharray: edgeData.isAnimated ? 5 : 0,
        style: edgeData.isAnimated ? {
          animation: 'ant-line 30s infinite linear',
        } : {},
        targetMarker: {
          name: "block",
          args: {
            size: edgeData.lineWidth + 4,
          },
        },
      }
    })
    const node = props.node as Edge
    (props.node as Edge).setLabels([{
      attrs: {
        body: {
          fill: edgeData.backgroundColor,
          stroke: edgeData.borderColor,
        },
        label: {
          fill: edgeData.fontColor,
          text: formData.value.name

        },
      }
    }])
    const connectorArgs: { [key: string]: number | string } = {}
    if (edgeData.lineStyle === 'jumpover') {
      connectorArgs['type'] = 'arc'
      connectorArgs['gap'] = 6
      connectorArgs['radius'] = 10
    }
    node.setConnector(edgeData.lineStyle, connectorArgs)
  }
}

// 处理名称变更
const handleChangeName = () => {
  if (!props.node) return

  if (isEdge.value) {
    (props.node as Edge).setLabels([{
      attrs: { label: { text: formData.value.name } }
    }])
  } else {
    if (!formData.value.name) {
      formData.value.name = props.node.data.name
    }
    props.node.setAttrs({
      title: { text: formData.value.name },
      label: { text: formData.value.name }
    })
  }
}

const handleClose = () => {
  resetFormData()
  emits('close')
}

const activeNames = ref(['1', '2', '3', '4'])
const initiatorTypeList = ref([
  {
    label: '指定执行人',
    value: '1'
  },
  {
    label: '指定角色',
    value: '2'
  },
  {
    label: '指定职级',
    value: '3'
  }
])

const allRawData = ref<BaseDataItem[]>([])
const allRawDataMap = ref(new Map<string, BaseDataItem>())
const formList = ref<BaseDataItem[]>([])

const treeDialogVisible = ref(false)
const treeData = computed(() => {
  const filteredData = allRawData.value.filter(item => item.basicType === +formData.value.initiatorType)
  return buildTree(filteredData)
})

// 将列表数据转换为树形结构
const buildTree = (list: BaseDataItem[]): BaseDataItemTree[] => {
  const map: { [key: string]: BaseDataItemTree } = {}
  const result: BaseDataItemTree[] = []

  // 先将所有项转换为树节点并建立映射
  list.forEach(item => {
    map[item.basicId] = { ...item, children: [] }
  })

  // 构建树形结构
  list.forEach(item => {
    const node = map[item.basicId]
    if (item.parentId !== '0') {
      // 如果有父节点，将当前节点添加到父节点的children中
      const parent = map[item.parentId]
      if (parent) {
        parent.children = parent.children || []
        parent.children.push(node)
      }
    } else {
      // 如果没有父节点，则为根节点
      result.push(node)
    }
  })
  return result
}

const getBaseDataList = async () => {
  try {
    const { data: { code, data } } = await api.getBaseDataList({ enterpriseId: props.enterpriseId })
    if (code === 200) {
      allRawData.value = data
      formList.value = allRawData.value.filter(item => item.basicType === 4)
      allRawData.value.forEach(item => {
        allRawDataMap.value.set(item.basicId, item)
      })
    }
  } catch (error) {
    console.error(error)
  }
}

getBaseDataList()

const handleSelectNode = (values: string[]) => {
  if (values && isNodeData(formData.value)) {
    formData.value.initiators = [...values]
    treeDialogVisible.value = false
  }
}

const handleChangeInitiatorType = () => {
  if (isNodeData(formData.value)) {
    formData.value.initiators = []
  }
}

const handleCloseTag = (item: { name: string, id: string }) => {
  if (isNodeData(formData.value)) {
    formData.value.initiators = formData.value.initiators.filter(i => i !== item.id)
  }
}

const handleAddInitiator = () => {
  if (!isNodeData(formData.value) || !formData.value.initiatorType) {
    return ElMessage.warning('请选择操作人类型')
  }
  treeDialogVisible.value = true
}


// 监听node变化，更新表单数据
watch(() => props.node, (newNode) => {
  if (!newNode) {
    resetFormData()
    return
  }

  const data = newNode.data || {}
  if (isNode.value) {
    formData.value = {
      isPreSet: data.isPreSet || false,
      name: data.name || '',
      initiatorType: data.initiatorType || '',
      initiators: data.initiators ? data.initiators.split(',') : [],
      formId: data.formId ? data.formId.split(',') : [],
      fontColor: data.fontColor || '',
      backgroundColor: data.backgroundColor || '',
      borderColor: data.borderColor || '',
      fontSize: data.fontSize || 14
    } as NodeData
  } else if (isEdge.value) {
    formData.value = {
      name: data.name || '',
      fontColor: data.fontColor || '',
      backgroundColor: data.backgroundColor || '',
      borderColor: data.borderColor || '',
      lineWidth: data.lineWidth || 2,
      isAnimated: data.isAnimated || false,
      lineStyle: data.lineStyle || 'normal',
      lineColor: data.lineColor || ''
    } as EdgeData
  }
}, { immediate: true })

// 监听表单数据变化，更新node.data
watch(formData, (newFormData) => {
  if (!props.node) return

  const data = { ...newFormData }
  if (isNode.value) {
    const nodeData = data as NodeData
    props.node.setData({
      ...nodeData,
      initiators: nodeData.initiators.join(','),
      formId: nodeData.formId.join(',')
    }, { deep: false })
  } else if (isEdge.value) {
    const edgeData = data as EdgeData
    props.node.setData(edgeData, { deep: false })
  }
}, { deep: true })

// 添加类型守卫函数
const isNodeData = (data: NodeData | EdgeData): data is NodeData => {
  return 'initiatorType' in data
}

</script>

<style scoped lang="scss">
.property-panel {
  width: 340px;
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  z-index: 2000;
  background-color: #ffffff;
  box-shadow: 0px 2px 4px 0px rgba(120, 147, 187, 0.15);
  border-radius: 0px 12px 12px 0px;
  border: solid 1px #ffffff;
  border-radius: 12px;
  overflow: hidden;
  padding: 0 15px;
  transition: all 0.3s ease;

  &.hidden {
    transform: translateX(100%);
  }

  &::after {
    display: inline-block;
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 48px;
    background-image: linear-gradient(to bottom,
        #d7e1fa 0%,
        transparent 100%);
  }
}

.panel-content {
  position: relative;
  color: #313052;
  font-size: 14px;
  z-index: 1;
  height: 100%;
  @include flex(column, flex-start, stretch, nowrap);
}

.panel-header {
  height: 48px;
  @include flex(row, space-between, center, nowrap);
  z-index: 1;
  border-bottom: 1px solid #c8ddfb;
  position: relative;

  &::after {
    display: inline-block;
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 24px;
    height: 3px;
    background-color: #507af1;
  }
}

.panel-section {
  min-height: 0;
  flex: 1;
}

.panel-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.close-btn {
  cursor: pointer;

  &:hover {
    background: #d7e1fa;
  }
}

.el-collapse {
  :deep {
    .el-form-item {
      margin-bottom: 0;
    }

    .el-collapse-item__header {
      height: 36px;
      color: #313052;
      font-size: 14px;
    }

    .el-collapse-item__arrow.is-active {
      color: #4b5ee7;
    }

    .el-collapse-item__wrap {
      border-bottom: 0;
    }

    .el-collapse-item__content {
      padding-bottom: 0;
      padding: 12px 16px;
      background-color: #eff1ff;
    }

    .el-form-item__label {
      margin-bottom: 0;
      line-height: 32px;
      text-align: right;
      color: #313052;
      font-weight: normal;
    }

    .el-form-item+.el-form-item {
      margin-top: 8px;
    }
  }
}

.select-tag-box {
  position: relative;
  width: 202px;
  height: 120px;
  background-color: #ffffff;
  border-radius: 4px;
  border: solid 1px #d0dae9;
  padding: 6px;

  overflow: hidden;

  .tag-list {
    @include flex(row, flex-start, flex-start, wrap);
    gap: 4px;
  }

  .add-btn {
    width: 22px;
    height: 22px;
    position: absolute;
    right: 0;
    bottom: 0;
    border-radius: 3px;
  }
}

.color-display {
  display: flex;
  align-items: center;
  gap: 8px;

  .color-block {
    width: 24px;
    height: 24px;
    border-radius: 4px;
    border: 1px solid #dcdfe6;
  }

  .color-text {
    font-size: 14px;
    color: #606266;
  }
}

.number-display {
  display: flex;
  align-items: center;

  .number-text {
    font-size: 14px;
    color: #606266;
  }
}

:deep {

  .el-select__wrapper.is-disabled,
  .el-input.is-disabled .el-input__wrapper,
  .el-input-number.is-disabled .el-input__wrapper {
    background-color: #fff !important;

    .el-input__inner,
    .el-select__selected-item:not(.is-transparent) {
      color: var(--el-text-color-regular);
      -webkit-text-fill-color: var(--el-text-color-regular);
    }
  }
}
</style>
