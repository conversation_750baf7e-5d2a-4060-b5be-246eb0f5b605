<template>
  <div class="list">
    <div class="teacher-main">
      <!-- 搜索栏 -->
      <div class="list-header">
        <el-form :inline="true" :model="ninSearchForm">
          <el-form-item label="实训名称：">
            <el-input v-model="ninSearchForm.trainName" placeholder="请输入" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleCurrentChange(1, getTableList, true)">查询
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="list-body">
        <!-- 操作按钮 -->
        <div class="list-body-toolbar clearfix">
          <el-button type="danger" @click="handleBatchDelete" :disabled="buttonDisabled">批量移除</el-button>
          <el-button type="primary" @click="handleAddNew">新增实训</el-button>
        </div>
        <el-alert type="warning" class="tips" :closable="false" v-if="ids.length">
          已选择
          <b>{{ ids.length }}</b> 项
        </el-alert>
        <!-- 表格 -->
        <el-table :data="tableData" v-loading="loading" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" />
          <el-table-column label="序号" type="index" width="80" />
          <el-table-column prop="trainName" label="实训名称" />
          <el-table-column prop="number" label="任务数量" width="120" />
          <!-- <el-table-column prop="beginTime" label="实训时间" width="200">
            <template #default="{ row }">
              {{ row.beginTime.slice(0, 10) }} - {{ row.endTime.slice(0, 10) }}
            </template>
</el-table-column> -->
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
              <!-- <el-button type="primary" link @click="handleStudentManage(row)">学生管理</el-button>
              <el-button type="primary" link @click="handleTrainingResult(row)">实训成绩</el-button> -->
              <el-button type="danger" link @click="handleSingleDelete(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <Pagination :total="pageTotal" :pageSize="pageSize" :currentPage="pageNum"
          @currentChange="handleCurrentChange($event, getTableList)"
          @sizeChange="handleSizeChange($event, getTableList)">
        </Pagination>
      </div>

      <!-- 新增/编辑弹窗 -->
      <AddTraining v-model:visible="addVisible" :type="dialogType" :editData="editData" @success="getTableList" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Search } from "@element-plus/icons-vue";
import { TableOptions } from "@/utils/tableOptions";
import Pagination from "@/components/global/Pagination.vue";
import * as api from '@/api/teacher'
import { computed, onMounted, ref, toRefs } from "vue";
import { encodeName } from '@/utils/tools'
import { ElMessage, ElMessageBox } from "element-plus";
import AddTraining from './components/AddTraining.vue'
import { useRouter } from 'vue-router';

interface TrainingItem {
  trainId: string;
  trainName: string;
  number: number;
  beginTime: string;
  endTime: string;
  createTime: string;
  questionIds: string;
  status: number;
}

const router = useRouter();

const t = new TableOptions();
const {
  loading,
  pageNum,
  pageSize,
  pageTotal,
  tableData,
  ninSearchForm,
  tempSearchForm,
} = toRefs(t);
const { handleCurrentChange, handleSizeChange } = t;

const ids = ref<string[]>([]);
const handleSelectionChange = (val: TrainingItem[]) => {
  ids.value = val.map((item) => item.trainId);
};
const buttonDisabled = computed(() => {
  return !ids.value.length
})

const getTableList = async () => {
  loading.value = true;
  const params: Record<string, string | number> = {
    page: pageNum.value,
    size: pageSize.value,
    ...tempSearchForm.value,
  };
  const { data: { code, msg, data } } = await api.getList(params);
  loading.value = false
  if (code === 200) {
    tableData.value = data.list;
    pageTotal.value = data.total;
  } else {
    ElMessage.error(msg)
  }
}


// 处理批量删除
const handleBatchDelete = () => {
  const params = {
    trainIds: JSON.stringify(ids.value),
  };
  handleDelete(params, true);
}

// 处理删除
const handleSingleDelete = (row: TrainingItem) => {
  const params = {
    trainIds: `[${row.trainId}]`,
  };
  handleDelete(params);
}

const handleDelete = (params: { trainIds: string }, isBatch = false) => {
  ElMessageBox.confirm(`你确定要${isBatch ? "批量" : ""}删除吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      loading.value = true;
      const { data: { code, msg } } = await api.delTrain(params);
      loading.value = false
      if (code === 200) {
        ElMessage.success("删除成功！");
        getTableList();
      } else {
        ElMessage.error(msg)
      }
    })
    .catch(() => { });
}

const addVisible = ref(false);
const dialogType = ref<'add' | 'edit'>("add");
const editData = ref<TrainingItem | null>(null);

// 处理新增
const handleAddNew = () => {
  dialogType.value = "add"
  editData.value = null
  addVisible.value = true
}
// 处理编辑
const handleEdit = (row: TrainingItem) => {
  dialogType.value = "edit"
  editData.value = row
  addVisible.value = true
}

// 处理学生管理
const handleStudentManage = (row: TrainingItem) => {
  router.push({
    path: '/teacher/stuManage',
    query: { trainId: row.trainId, trainName: encodeName(row.trainName), status: row.status }
  })
}

// 处理实训成绩
const handleTrainingResult = (row: TrainingItem) => {
  router.push({
    path: '/teacher/achievement',
    query: { trainId: row.trainId, trainName: encodeName(row.trainName) }
  })
}


onMounted(() => {
  getTableList();
});
</script>

<style scoped lang="scss">
.list {

  .w-200 {
    width: 200px;
  }

  .list-header {
    padding: 20px 20px 0;
    background-color: #fff;
  }

  .list-body {
    background-color: #fff;
    margin: 20px;
    padding: 20px;
  }

  .list-body-toolbar {
    border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
    padding-bottom: 5px;
  }
}
</style>
