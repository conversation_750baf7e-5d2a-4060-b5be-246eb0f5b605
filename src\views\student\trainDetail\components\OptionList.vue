<template>
  <div class="options">
    <div class="section-title">
      <div class="title-label">工具箱</div>
    </div>
    <div class="content-wrapper">
      <el-scrollbar height="100%">
        <div class="option-list">
          <div v-for="item in options" :key="item.id" :title="item.name"
            :class="['option-item', { 'used': usedOptionIds.has(item.id) }]"
            :draggable="!usedOptionIds.has(item.id) && !disabled" @dragstart="handleDragStart(item, $event)">
            <img v-if="icons" :src="icons" class="img" />
            {{ item.name }}
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { OptionItem } from '@/views/admin/types'
import flag from '@/assets/svg/flag.svg'
import improve from '@/assets/svg/improve.svg'
import { computed } from 'vue'

const props = defineProps<{
  options: OptionItem[]
  usedOptionIds: Set<string>
  disabled?: boolean,
  iconSrc?: 'flag' | 'improve'
}>()

const emit = defineEmits<{
  'drag-start': [option: OptionItem, event: DragEvent]
}>()

const icons = computed(() => {
  if (!props.iconSrc) return null
  return {
    flag,
    improve
  }[props.iconSrc]
})

const handleDragStart = (option: OptionItem, event: DragEvent) => {
  if (event.target instanceof HTMLElement) {
    const dragPreview = event.target.cloneNode(true) as HTMLElement
    dragPreview.style.width = `${event.target.offsetWidth}px`
    dragPreview.classList.add('drag-preview')
    document.body.appendChild(dragPreview)

    event.dataTransfer?.setDragImage(dragPreview, 10, 20)

    requestAnimationFrame(() => {
      document.body.removeChild(dragPreview)
    })
  }
  emit('drag-start', option, event)
}
</script>

<style scoped lang="scss">
.options {
  width: 100%;
  height: 100%;
  gap: 20px;
  @include flex(column, flex-start, flex-start, nowrap);

  .content-wrapper {
    width: 100%;
    flex: 1;
    min-height: 0;
    @include border-1px()
  }

  .option-list {
    padding: 20px;
  }

  .option-item {
    flex-shrink: 0;
    margin-bottom: 8px;
    background: linear-gradient(180deg, #E7F7FF 0%, #6F92AA 100%);
    text-align: center;
    color: #001E22;
    height: 38px;
    line-height: 38px;
    font-size: var(--font-size-small);
    font-weight: 700;
    cursor: pointer;
    user-select: none;
    @include text-ellipsis();
    padding: 0 14px;
    @include flex(row, center, center, nowrap);

    .img {
      width: 16px;
      height: 16px;
      margin-right: 4px;
    }

    &.used {
      background: transparent;
      color: #fff;
      cursor: not-allowed;
      font-weight: normal;
      border: 1px solid;
      line-height: 36px;
      border-image: linear-gradient(90deg, rgba(#2BA0AD, 40%) 0, rgba(#2BA0AD, 60%) 80%, rgba(#2BA0AD, 80%) 90%, rgba(#2BA0AD, 40%) 100%) 1;
    }

    &.drag-preview {
      position: fixed;
      top: -9999px;
      left: -9999px;
      z-index: -1;
      opacity: 0.8;
      pointer-events: none;
    }
  }

  @media screen and (max-width: 1440px) {
    .option-item {
      height: 32px;
      line-height: 32px;

      &.used {
        line-height: 30px;
      }
    }
  }
}

:global(.drag-preview) {
  position: fixed;
  top: -9999px;
  left: -9999px;
  z-index: -1;
  opacity: 0.8;
  transform: scale(1.05);
  pointer-events: none;
  background: linear-gradient(180deg, #E7F7FF 0%, #6F92AA 100%);
  padding: 0 14px;
  height: 36px;
  line-height: 36px;
  font-size: 14px;
  font-weight: 700;
  color: #001E22;
  text-align: center;
  border-radius: 2px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  @media screen and (max-width: 1440px) {
    .option-item {
      height: 32px;
      line-height: 32px;
    }
  }
}
</style>
