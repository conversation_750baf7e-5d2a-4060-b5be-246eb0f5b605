<template>
  <div class="key-point-control">
    <OptionList v-model:options="options" :used-option-ids="usedOptionIds" />
    <div class="control-right">
      <div class="right-header">
        <span>关键点控制</span>
        <span class="tip-text">请先设置选项后再拖拽到表格中</span>
      </div>
      <div class="button-group">
        <el-button type="primary" @click="handleAddRow">新增行</el-button>
      </div>
      <div class="control-list">
        <div class="table-wrapper">
          <table border="0" class="table">
            <tbody>
              <tr v-for="(item, rowIndex) in tableList" :key="rowIndex">
                <td>
                  <div class="row-title">
                    <el-input v-model="item.name" placeholder="请输入大项名称" maxlength="50" />
                  </div>
                </td>
                <td class="cell-wrapper">
                  <div v-for="(child, index) in item.children" :key="index" class="cell" :data-row-id="rowIndex"
                    :data-index="index" @dragover.prevent @dragenter="handleDragEnter($event)"
                    @dragleave="handleDragLeave($event)" @drop="handleDrop($event, rowIndex, index)" draggable="true"
                    @dragstart="handleCellDragStart($event, rowIndex, index)">
                    <div v-if="child" class="cell-content" :title="child">
                      {{ child }}
                      <div class="delete-icon" @click.stop="handleDelete(rowIndex, index)">
                        <el-icon>
                          <Close />
                        </el-icon>
                      </div>
                    </div>
                  </div>
                </td>
                <td class="cell-wrapper">
                  <div class="cell add" v-for="(child, index) in item.children" :key="index">
                    <el-button type="primary" :icon="Plus" link @click="handleAddItem(rowIndex, index)">添加</el-button>
                    <el-button type="danger" :icon="Delete" link
                      @click="handleDeleteItem(rowIndex, index)">删除</el-button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Delete, Plus, Close } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import OptionList from './OptionList.vue'
import type { OptionItem } from '../../types'

const props = defineProps({
  initContent: {
    type: String,
    default: ''
  },
  taskOptions: {
    type: String,
    default: ''
  }
})


interface TableItem {
  name: string
  children: (string)[]  // 改为存储选项名称
}

const options = ref<OptionItem[]>([])


const optionsMap = computed(() => {
  const map = new Map<string, string>()
  options.value.forEach(item => {
    map.set(item.name, item.id)
  })
  return map
})

// 计算已使用的选项名称集合
const usedOptionIds = computed(() => {
  const names = new Set<string>()
  tableList.value.forEach(item => {
    item.children.forEach(name => {
      if (name) {
        const id = optionsMap.value.get(name)
        if (id) {
          names.add(id)
        }
      }
    })
  })
  return names
})

const tableList = ref<TableItem[]>([
  {
    name: '',
    children: ['', '']
  }
])

// 新增行
const handleAddRow = () => {
  tableList.value.push({
    name: '',
    children: ['']
  })
}

// 添加列
const handleAddItem = (rowIndex: number, index: number) => {
  const currentRow = tableList.value[rowIndex]
  if (currentRow) {
    currentRow.children.splice(index + 1, 0, '')
  }
}

// 删除列
const handleDeleteItem = (rowIndex: number, index: number) => {
  const currentRow = tableList.value[rowIndex]
  if (currentRow) {
    currentRow.children.splice(index, 1)
    if (currentRow.children.length === 0) {
      tableList.value.splice(rowIndex, 1)
    }
  }
}
// 处理拖拽进入
const handleDragEnter = (event: DragEvent) => {
  const cell = event.currentTarget as HTMLElement
  cell.classList.add('highlight-drop')
}

// 处理拖拽离开
const handleDragLeave = (event: DragEvent) => {
  const cell = event.currentTarget as HTMLElement
  cell.classList.remove('highlight-drop')
}

// 处理放置
const handleDrop = (event: DragEvent, rowIndex: number, index: number) => {
  const data = event.dataTransfer?.getData('text/plain')
  if (!data) return

  const option = JSON.parse(data) as OptionItem
  const currentRow = tableList.value[rowIndex]

  // 如果该选项已经在其他位置，先移除
  tableList.value.forEach(item => {
    item.children = item.children.map(name =>
      name === option.name ? '' : name
    )
  })

  // 设置新位置的值
  currentRow.children[index] = option.name

  // 移除高亮效果
  const cell = event.currentTarget as HTMLElement
  cell.classList.remove('highlight-drop')
}


const handleCellDragStart = (event: DragEvent, rowIndex: number, index: number) => {
  const row = tableList.value[rowIndex]
  const optionName = row?.children[index]
  if (optionName) {
    const option = options.value.find(opt => opt.name === optionName)
    if (option) {
      event.dataTransfer?.setData('text/plain', JSON.stringify(option))
      event.dataTransfer!.effectAllowed = 'move'

      // 创建拖拽图像
      const dragImage = document.createElement('div')
      dragImage.textContent = optionName
      dragImage.style.cssText = `
        position: absolute;
        left: -9999px;
        background: rgb(101, 151, 247);
        color: #fff;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: var(--font-size-small);
        white-space: nowrap;
        pointer-events: none;
        z-index: 9999;
        width: 200px;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
      `
      document.body.appendChild(dragImage)
      event.dataTransfer!.setDragImage(dragImage, 0, 0)

      // 在下一帧移除临时元素
      setTimeout(() => {
        document.body.removeChild(dragImage)
      }, 0)
    }
  }
}

// 添加删除处理函数
const handleDelete = (rowIndex: number, index: number) => {
  const currentRow = tableList.value[rowIndex]
  if (currentRow) {
    currentRow.children[index] = ''
  }
}

const validate = () => {
  return new Promise((resolve, reject) => {
    if (tableList.value.length === 0 || options.value.length === 0) {
      ElMessage.error('请添加选项和关键控制点')
      reject(false)
      return
    }
    if (tableList.value.some(row => !row.name)) {
      ElMessage.error('关键控制点名称不能为空')
      reject(false)
      return
    }
    resolve({
      initContent: JSON.stringify(tableList.value),
      taskOptions: JSON.stringify(options.value)
    })
  })
}

onMounted(() => {
  tableList.value = props.initContent ? JSON.parse(props.initContent) : [{
    name: '',
    children: ['', '']
  }]
  options.value = props.taskOptions ? JSON.parse(props.taskOptions) : []
})

defineExpose({
  validate
})
</script>

<style scoped lang="scss">
.key-point-control {
  @include flex(row, flex-start, flex-start, nowrap);
  gap: 20px;
  position: relative;

  .control-right {
    flex: 1;
    min-width: 0;
    @include flex(column, flex-start, stretch, nowrap);
    gap: 16px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 14px;
    border-radius: 6px;

    .right-header {
      @include flex(row, flex-start, flex-start, nowrap);
      gap: 14px;

      span {
        font-weight: bold;
        color: var(--el-text-color-primary);
      }

      .tip-text {
        color: var(--el-text-color-secondary);
        font-size: 14px;
        font-weight: normal;
      }
    }

    .table {
      width: 100%;
      border-collapse: collapse;
      table-layout: fixed;
      font-family: "Microsoft YaHei UI";

      td {
        max-width: 200px;
        width: 30%;
        border: 1px solid var(--el-border-color);
        word-break: break-all;
        height: 40px;
        line-height: 40px;

        &.cell-wrapper {
          width: 70%;

          &:last-child {
            width: 140px;
          }
        }
      }

      .row-title {
        font-size: var(--font-size);
        font-weight: 700;
        max-width: 200px;
        color: #fff;
        text-align: center;
        line-height: 24px;
        margin: 0 auto;
        padding: 0 12px;
        @include multi-ellipsis();
      }

      .cell {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: var(--font-size-small);
        position: relative;
        line-height: 40px;

        .cell-content {
          background-color: rgb(101, 151, 247, 70%);
          color: #fff;
          cursor: move;
          width: 100%;
          text-align: center;
          padding: 0 14px;
          height: 100%;
          @include text-ellipsis();
          box-shadow: 0 0 0 1px #409EFF;


          &:hover {
            .delete-icon {
              opacity: 1;
            }
          }
        }

        &+.cell {
          border-top: 1px solid var(--el-border-color);
        }

        &.highlight-drop {
          box-shadow: 0 0 0 2px #409EFF;
          border-color: #409EFF;
          background: rgba(64, 158, 255, 0.3);
          z-index: 1;

          &::after {
            content: '';
            position: absolute;
            inset: -2px;
            border: 2px dashed #409EFF;
            pointer-events: none;
            animation: borderDash 20s linear infinite;
          }
        }

        .delete-icon {
          position: absolute;
          top: 50%;
          right: 10px;
          width: 20px;
          height: 20px;
          border-radius: 50%;
          cursor: pointer;
          opacity: 0;
          transition: all 0.2s ease;
          color: #fff;
          transform: translateY(-50%);
          @include flex(row, center, center, nowrap);
          background-color: rgba(0, 0, 0, 0.2);

          &:hover {
            transform: translateY(-50%) scale(1.2);
            background-color: rgba(0, 0, 0, 0.4);
            color: #fff;
          }

          .el-icon {
            font-size: var(--font-size-small);
            font-weight: bold;
          }
        }

        &.add {
          padding: 0 4px;
          justify-content: space-around;
        }
      }

      @media screen and (max-width: 1440px) {
        .cell {
          height: 34px;
          line-height: 34px;
        }
      }
    }
  }
}
</style>
