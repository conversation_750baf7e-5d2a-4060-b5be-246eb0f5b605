<template>
  <div class="business-flow">
    <div class="business-flow-left">
      <div class="sub-title">流程图设计</div>
      <div class="flow-shapes">
        <div class="shape-item" v-for="item in flowShapes" :key="item.type" draggable="true"
          @dragstart="(e) => handleShapeDragStart(e, item)">
          <div :class="['shape', item.type]">{{ item.name }}</div>
        </div>
      </div>
    </div>
    <div class="business-flow-main">
      <div class="graph-wrapper">
        <div class="graph" ref="graphRef"></div>
      </div>
    </div>
    <div class="business-flow-right">
      <div class="sub-title">做题选项</div>
      <div class="option-list">
        <div v-for="option in options" :key="option.id" class="option-item">
          <span class="option-name" :title="option.name">{{ option.name }}</span>
          <el-button type="danger" link size="small" @click="handleRemoveOption(option.id)">
            删除
          </el-button>
        </div>
      </div>
    </div>
    <!-- 添加编辑弹窗 -->
    <el-dialog v-model="editDialogVisible" title="编辑节点" width="550px" :close-on-click-modal="false"
      @close="handleEditDialogClose">
      <el-form :model="editForm" label-width="80px">
        <el-form-item label="节点名称">
          <el-input v-model="editForm.name" placeholder="请输入节点名称" maxlength="50" show-word-limit
            @keyup.enter="handleEditConfirm" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleEditConfirm">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, nextTick, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import { Graph } from '@antv/x6'
import { Dnd } from '@antv/x6-plugin-dnd'
import type { Cell, Node } from '@antv/x6'
import type { OptionItem } from '../../types'
import cellHover from "@/components/flow/composables/cellHover";
import cellSelect from "@/components/flow/composables/cellSelect";
import { adminDefaultConfig } from '@/components/flow/shapeConfig/staticConfig'
import { registerNode, unregisterNode } from "@/components/flow/shapeConfig/registerNode";
import { registerNodeTools, unregisterNodeTools } from "@/components/flow/tools/registerNodeTools";

const props = defineProps({
  initContent: {
    type: String,
    default: ''
  },
  taskOptions: {
    type: String,
    default: ''
  }
})


const optionsMap = ref<Map<string, OptionItem>>(new Map())
const options = computed(() => {
  return Array.from(optionsMap.value.entries()).reduce((pre, cur) => {
    return pre.concat({
      id: cur[0],
      name: cur[1].name
    })
  }, [] as OptionItem[])
})

const graphRef = ref<HTMLDivElement | null>()
const graph = ref<Graph>()
const dnd = ref<Dnd>();

interface FlowShape {
  type: string
  name: string
  shape: string
}

const flowShapes = ref<FlowShape[]>([
  { type: 'start', name: '开始', shape: 'start-node' },
  { type: 'end', name: '结束', shape: 'start-node' },
  { type: 'process', name: '过程', shape: 'general-node' },
  { type: 'decision', name: '决策', shape: 'gateway-node' },
  { type: 'data', name: '数据', shape: 'data-node' }
])

//注入流程数据
const injectData = () => {
  if (props.initContent) {
    const data = JSON.parse(props.initContent)
    if (graph.value) {
      graph.value.fromJSON(data)
      // 隐藏节点上的连线
      data.forEach((node: Cell) => {
        setTimeout(() => {
          const ports = document.querySelectorAll(
            `g[data-cell-id="${node.id}"] .x6-port`
          );
          ports.forEach((port) => {
            (port as SVGElement).style.visibility = "hidden";
          });
        }, 100)
      })
    }
  }
}

// 初始化图形
const initGraph = async () => {
  await nextTick();
  if (!graphRef.value) return
  graph.value = new Graph({
    container: graphRef.value,
    ...adminDefaultConfig,
  });
  dnd.value = new Dnd({
    target: graph.value,
    validateNode() {
      return true
    },
  });

  injectData()
  resigterEvent()
  cellSelect(graph.value)
}

// 编辑弹窗相关
const editDialogVisible = ref(false)
const editForm = ref({
  name: ''
})
const currentEditNode = ref<Node | null>(null)

// 注册事件
const resigterEvent = () => {
  if (!graph.value) return
  cellHover(graph.value)
  graph.value.on('node:dblclick', ({ node }) => {
    currentEditNode.value = node
    editForm.value.name = node.data?.name || node.attr('label/text') || ''
    // 如果是做题就不让编辑了
    if (!node.data.answer) {
      editDialogVisible.value = true
    }
  })

  graph.value.on('node:removed', ({ node }) => {
    optionsMap.value.delete(node.id)
  })

  graph.value.on('node:change:data', ({ node }) => {
    const data = node.data
    if (data.answer) {
      optionsMap.value.set(node.id, data)
    } else {
      optionsMap.value.delete(node.id)
    }
  })

  graph.value.on('node:mouseenter', ({ node }) => {
    node.addTools({
      name: 'answer-button',
      args: {
      }
    })
  });
  graph.value.on('node:mouseleave', ({ node }) => {
    node.removeTool('answer-button');
  });
}

const handleShapeDragStart = (event: DragEvent, item: FlowShape) => {
  const node = graph.value!.createNode({
    shape: item.shape,
    ports: {
      items: [{
        group: 'port_g',
        id: 'p_top'
      },
      {
        group: 'port_g',
        id: 'p_right'
      }],
      groups: {
        port_g: {
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              strokeWidth: 1,
              fill: '#fff'
            }
          },
          position: 'ellipseSpread'
        }
      }
    },
    tools: [
      {
        name: 'cus-button-remove',
        args: {
          x: 130,
          y: 4,
          offset: {
            x: -5,
            y: -5
          }
        }
      },
      // {
      //   name: 'node-editor',
      //   args: {
      //     getText: ({ cell }: any) => {
      //       return cell.data.name || ''
      //     },
      //     setText: ({ cell, value }: any) => {
      //       if (value === null) {
      //         return
      //       }
      //       const availableVal = value.replace(/\s/g, '');
      //       cell.setAttrs({
      //         title: { text: availableVal },
      //         label: { text: availableVal }
      //       });
      //       cell.setData({
      //         name: availableVal
      //       })
      //     }
      //   },
      // }
    ],
  })
  node.attrs!.label.text = item.name;
  node.attrs!.title.text = item.name;
  node.setData({
    name: item.name
  })
  dnd.value?.start(node, event)
}


const handleRemoveOption = (id: string) => {
  const node = graph.value?.getCellById(id)
  if (node) {
    node.setAttrs({
      title: { text: node.data.name },
      label: { text: node.data.name },
    });
    node.setData({
      answer: false
    })
    optionsMap.value.delete(id)
  }
}

// 处理编辑确认
const handleEditConfirm = () => {
  if (!currentEditNode.value) return
  const newName = editForm.value.name.trim()
  if (!newName) {
    ElMessage.warning('节点名称不能为空')
    return
  }

  currentEditNode.value.setAttrs({
    title: { text: newName },
    label: { text: newName }
  })
  currentEditNode.value.setData({
    ...currentEditNode.value.data,
    name: newName
  })

  editDialogVisible.value = false
}

// 处理弹窗关闭
const handleEditDialogClose = () => {
  editForm.value.name = ''
  currentEditNode.value = null
}

onMounted(() => {
  registerNode(Graph);
  registerNodeTools(Graph);
  initGraph()
  if (props.taskOptions) {
    const data = JSON.parse(props.taskOptions)
    data.forEach((item: OptionItem) => {
      optionsMap.value.set(item.id, item)
    })
  }
})

onBeforeUnmount(() => {
  unregisterNode(Graph)
  unregisterNodeTools(Graph)
  // 清理tooltip
  const tooltip = document.querySelector('.node-tooltip')
  if (tooltip) {
    document.body.removeChild(tooltip)
  }
})

const validate = () => {
  return new Promise((resolve, reject) => {
    const data = graph.value?.toJSON();
    if (!data?.cells?.length) {
      ElMessage.error('流程设计不能为空')
      reject()
    }
    resolve({
      initContent: JSON.stringify(data?.cells),
      taskOptions: JSON.stringify(options.value),
    })
  })
}


defineExpose({
  validate
})
</script>

<style scoped lang="scss">
.business-flow {
  @include flex(row, flex-start, flex-start, nowrap);
  gap: 20px;
  position: relative;
  min-height: 600px;

  .business-flow-left {
    width: 180px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    padding: 14px;
  }

  .sub-title {
    font-size: 14px;

  }

  .business-flow-main {
    height: 600px;
    flex: 1;
    min-width: 0;
    @include flex(column, flex-start, stretch, nowrap);
    gap: 16px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 14px;
    border-radius: 6px;
  }

  .graph-wrapper {
    min-height: 0;
    flex: 1;

    .graph {
      height: 100%;
    }
  }
}

.business-flow-right {
  width: 200px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  padding: 14px;
  height: 600px;
  overflow-y: auto;

  .option-list {
    margin-top: 12px;
    @include flex(column, flex-start, stretch, nowrap);
    gap: 8px;
  }

  .option-item {
    padding: 8px;
    background: #f5f7fa;
    border-radius: 4px;
    @include flex(row, space-between, center, nowrap);

    .option-name {
      font-size: 14px;
      color: #333;
      flex: 1;
      min-width: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.flow-shapes {
  @include flex(column, flex-start, stretch, nowrap);
  gap: 16px;
  margin-top: 12px;

  .shape-item {
    cursor: move;
    color: #001E22;
    font-weight: 700;

    .shape {
      height: 40px;
      background: linear-gradient(180deg, #E7F7FF 0%, #6F92AA 100%);
      @include flex(row, center, center, nowrap);
      font-size: 14px;
      color: #333;
      cursor: pointer;

      &.start,
      &.end {
        border-radius: 20px;
      }

      &.process {
        border-radius: 2px;
      }

      &.decision {
        background: linear-gradient(180deg, #E7F7FF 0%, #6F92AA 100%);
        clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);

        .text {
          transform: rotate(-45deg);
        }
      }

      &.data {
        transform: skew(-20deg);

        .text {
          transform: skew(20deg);
        }
      }
    }
  }
}

:deep(.el-dialog__body) {
  padding-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
