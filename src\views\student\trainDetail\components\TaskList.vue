<template>
  <div class="task-list">
    <div class="section-title">
      <div class="title-label">任务列表</div>
    </div>
    <div class="list-content">
      <el-scrollbar height="100%">
        <div class="list-inner">
          <el-collapse>
            <el-collapse-item :class="['icon-' + item.categoryIcon]" v-for="item in taskList" :title="item.categoryName"
              :name="item.categoryName" :icon="ArrowRightBold" :key="item.categoryName">
              <template #title>
                <div class="collapse-title" :title="item.categoryName">
                  {{ item.categoryName }}
                </div>
              </template>
              <div class="task-item"
                :class="{ active: currentTask?.questionId === task.questionId, checked: task.ifFinish }"
                v-for="task in item.questionList" :key="task.questionId" :title="task.questionName"
                @click="handleClick(task)">
                <span class="task-name">{{ task.questionName }}</span>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowRightBold } from '@element-plus/icons-vue'
import type { TaskItem, TaskListItem } from '../types'

interface Props {
  taskList: TaskListItem[]
  currentTask: TaskItem | null
}

withDefaults(defineProps<Props>(), {
  taskList: () => []
})

const emit = defineEmits<{
  'update:currentTask': [task: TaskItem]
  'change': [task: TaskItem]
}>()


const handleClick = (task: TaskItem) => {
  emit('update:currentTask', task)
  emit('change', task)
}
</script>

<style scoped lang="scss">
.task-list {
  width: 100%;
  height: 100%;
  @include flex(column, flex-start, flex-start, nowrap);
  gap: 20px;

  .list-content {
    width: 100%;
    flex: 1;
    min-height: 0;
    border: 1px solid;
    border-image: linear-gradient(180deg, #31D2FF, #002752) 1;
    background: rgba(255, 255, 255, 0.1);

    .list-inner {
      padding: 20px 8px 20px 20px;
    }

    .task-item {
      width: 100%;
      height: 44px;
      color: #fff;
      padding-left: 56px;
      @include flex(row, flex-start, center, nowrap);
      position: relative;
      cursor: pointer;
      padding-right: 2px;

      .task-name {
        flex: 1;
        min-width: 0;
        @include text-ellipsis();
      }

      &:hover {
        color: #27D4F2;
      }

      &.active {
        background: url('@/assets/images/train/bg-task-item.png') no-repeat;
        background-size: 100% 100%;
        color: #fff;
      }

      &.checked::before {
        content: '';
        position: absolute;
        left: 16px;
        top: 50%;
        width: 20px;
        height: 20px;
        background: url('@/assets/images/icon/icon-check.png') no-repeat;
        background-size: 100% 100%;
        transform: translateY(-50%);
      }
    }

    .collapse-title {
      @include text-ellipsis();
    }

    :deep(.el-collapse) {
      border: 0;

      .el-collapse-item {
        &.is-active {
          .el-collapse-item__header::after {
            transform: scale(1);
          }

          .el-collapse-item__arrow {
            transform: rotate(-90deg);
          }
        }

        @for $i from 1 through 12 {
          &.icon-#{$i} {
            .el-collapse-item__header {
              background: 10px center url('@/assets/images/taskIcon/icon-task-#{$i}.png') no-repeat;
              background-size: 32px 32px;
            }

          }
        }
      }

      .el-collapse-item__header {
        height: 48px;
        background: transparent;
        color: #fff;
        border: 0;
        font-size: 20px;
        font-style: normal;
        font-weight: 700;
        padding-left: 56px;
        font-family: "Microsoft YaHei UI";
        position: relative;
        @include text-ellipsis();

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 2px;
          background: linear-gradient(to right,
              transparent,
              rgba(255, 255, 255, 0.75) 50%,
              transparent 100%);
          clip-path: ellipse(50% 100% at 50% 100%);
          transform: scale(0);
          transition: all 0.35s;
        }

        .el-collapse-item__arrow {
          transform: rotate(90deg);
          font-size: 22px;
          margin-right: 14px;
        }
      }

      .el-collapse-item__wrap {
        background: transparent;
        border: 0;
        color: #fff;
      }

      .el-collapse-item__content {
        padding-top: 6px;
        padding-bottom: 0;
      }

      @media screen and (max-width: 1440px) {
        .el-collapse-item__header {
          font-size: 14px;
          padding-left: 48px;
          background-size: 26px 26px !important;
        }
      }
    }

    @media screen and (max-width: 1440px) {
      .list-inner {
        padding: 20px 8px;
      }

      .task-item {
        height: 36px;

      }
    }
  }


}
</style>
