const degeLabel = {
  inherit: 'edge',
  defaultLabel: {
    markup: [
      {
        tagName: 'rect',
        selector: 'body',
      },
      {
        tagName: 'text',
        selector: 'label',
      },
    ],
    attrs: {
      label: {
        fill: '#000',
        fontSize: 14,
        textAnchor: 'middle',
        textVerticalAnchor: 'middle',
        pointerEvents: 'none',
      },
      body: {
        ref: 'label',
        fill: '#ffffff',
        stroke: '#507af1',
        strokeWidth: 2,
        rx: 4,
        ry: 4,
        refWidth: '120%',
        refHeight: '120%',
        refX: '-10%',
        refY: '-10%',
      },
    },
    position: {
      distance: 0.5
    }
  },
}

export default degeLabel
