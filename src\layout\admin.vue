<template>
  <div class="admin-layout">
    <div class="admin-menu">
      <div class="logo">
        <img src="@/assets/images/logo-colored.png" alt="logo" />
      </div>
      <el-menu :default-active="route.path" @select="handleMenuSelect">
        <el-menu-item v-for="item in routeList" :key="item.path" :index="item.path">
          <el-icon>
            <component :is="item.icon" />
          </el-icon>
          <span>{{ item.name }}</span>
        </el-menu-item>
      </el-menu>
    </div>
    <div class="admin-content">
      <div class="admin-content-header">
        <div class="user-info">
          <el-dropdown trigger="click">
            <span class="el-dropdown-link">
              <el-avatar :size="30" :src="avatar" />
              <el-icon class="el-icon--right">
                <arrow-down />
              </el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>
                  <div @click="handleQuit">
                    <el-icon>
                      <SwitchButton />
                    </el-icon>
                    退出
                  </div>

                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
      <div class="main-content">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import avatar from '@/assets/images/avatar.png'
import {
  Menu as IconMenu,
  ArrowDown,
  SwitchButton,
  Memo,
  DataBoard,
  Guide
} from '@element-plus/icons-vue'
import { useRoute, useRouter } from 'vue-router'
import { useStore } from 'vuex';

const route = useRoute()
const router = useRouter()
const store = useStore()

const routeList = [
  {
    path: '/admin/questionList',
    name: '题目管理',
    icon: IconMenu
  },
  {
    path: '/teacher/trainingList',
    name: '实训管理',
    icon: DataBoard
  },
  {
    path: '/admin/questionCategory',
    name: '题目分类',
    icon: Guide
  },
  {
    path: '/admin/enterpriseList',
    name: '企业管理',
    icon: Memo
  },
]

const handleQuit = () => {
  store.dispatch('userLogout')
  router.replace('/adminLogin')
}
const handleMenuSelect = (key: string) => {
  router.push({
    path: key,
    query: route.query
  })
}
</script>
<style lang="scss" scoped>
.admin-layout {
  width: 100vw;
  height: 100vh;
  background: #f0f2f5;
  display: flex;
  overflow: hidden;

  .admin-menu {
    width: 200px;
    height: 100%;
    background: #fff;
    flex-shrink: 0;
  }

  .logo {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-right: 1px solid var(--el-menu-border-color);

    img {
      width: 140px;
    }
  }

  .el-menu {
    height: 100%;
  }

  .admin-content {
    flex: 1;
    height: 100%;
    @include flex(column, flex-start, flex-start, nowrap);
    overflow: hidden;
  }

  .admin-content-header {
    width: 100%;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 20px;
    border-bottom: 1px solid var(--el-menu-border-color);
    background: #fff;

    .el-dropdown-link {
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }

  .user-info {
    cursor: pointer;
    vertical-align: middle;
  }

  .main-content {
    width: 100%;
    flex: 1;
    overflow: auto;
  }
}
</style>
