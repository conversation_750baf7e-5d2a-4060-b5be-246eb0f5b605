interface IBaseType {
  series?: string;
  platformCode?: string;
  title?: string,
  webAPI?: string,
  webCDN?: string,
  copyRight?: string,
}
const baseObject = process.env.NODE_ENV === 'production' ? window.global as IBaseType : {}
const appSettings = {
  series: baseObject.series || 'FINANCE', //系列名称
  platformCode: baseObject.platformCode || 'ninBusinessCanvas', //平台编码 后台打包必须为空
  title: baseObject.title || '商业画布', //名称
  webAPI: baseObject.webAPI || 'http://172.30.6.28:8084/nin_business_canvas', //当前产品接口
  webCDN: baseObject.webCDN || 'http://172.30.6.28:8084/nin_business_canvas', //当前产品接口
  copyRight: baseObject.copyRight || '',
}
export default appSettings
