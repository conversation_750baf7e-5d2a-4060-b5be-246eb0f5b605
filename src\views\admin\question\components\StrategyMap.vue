<template>
  <div class="key-point-control">
    <OptionList v-model:options="options" :used-option-ids="usedOptionIds" />
    <div class="control-right">
      <div class="right-header">
        <span>战略地图</span>
        <span class="tip-text">请先设置选项后再拖拽到战略地图中</span>
      </div>
      <div class="map-wrapper">
        <div class="map-item" v-for="(item, rowIndex) in tableList" :key="rowIndex">
          <el-button type="danger" size="small" class="delete-map" :icon="Delete" @click="handleDeleteRow(rowIndex)">
          </el-button>
          <div class="map-name">
            {{ item.name }}
          </div>
          <div class="map-content" @drop="handleDrop($event, rowIndex)" @dragover.prevent
            @dragenter="handleDragEnter($event)" @dragleave="handleDragLeave($event)">
            <div v-if="item.children.length === 0" class="map-desc">
              请将左侧选项拖动到此区域
            </div>
            <div v-else class="dropped-items">
              <div class="items-wrapper">
                <div v-for="(optionName, index) in item.children" :key="index" :title="optionName" class="option-item"
                  draggable="true" @dragstart="handleDragStart($event, optionName)">
                  {{ optionName }}
                  <div class="delete-icon" @click.stop="handleDelete(rowIndex, index)">
                    <el-icon>
                      <Close />
                    </el-icon>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="map-item">
          <el-button type="primary" size="small" class="delete-map" :icon="Plus" @click="handleAddRow()">
          </el-button>
          <div class="map-name-input">
            <el-input v-model="name" placeholder="请输入名称" maxlength="20" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Delete, Plus, Close } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import OptionList from './OptionList.vue'
import type { OptionItem } from '../../types'

const props = defineProps({
  initContent: {
    type: String,
    default: ''
  },
  taskOptions: {
    type: String,
    default: ''
  }
})

interface TableItem {
  name: string
  children: string[]  // 改为存储选项名称
}

const options = ref<OptionItem[]>([])


const optionsMap = computed(() => {
  const map = new Map<string, string>()
  options.value.forEach(item => {
    map.set(item.name, item.id)
  })
  return map
})

// 计算已使用的选项名称集合
const usedOptionIds = computed(() => {
  const names = new Set<string>()
  tableList.value.forEach(item => {
    item.children.forEach(name => {
      if (name) {
        const id = optionsMap.value.get(name)
        if (id) {
          names.add(id)
        }
      }
    })
  })
  return names
})

const tableList = ref<TableItem[]>([])

const name = ref('')
// 新增行
const handleAddRow = () => {
  if (name.value === '') return ElMessage.warning('请输入名称')
  tableList.value.push({
    name: name.value,
    children: []
  })
  name.value = ''
}

// 处理拖拽进入
const handleDragEnter = (event: DragEvent) => {
  const cell = event.currentTarget as HTMLElement
  cell.classList.add('highlight-drop')
}

// 处理拖拽离开
const handleDragLeave = (event: DragEvent) => {
  const cell = event.currentTarget as HTMLElement
  cell.classList.remove('highlight-drop')
}

// 处理放置
const handleDrop = (event: DragEvent, rowIndex: number) => {
  const data = event.dataTransfer?.getData('text/plain')
  if (!data) return

  const option = JSON.parse(data) as OptionItem
  const currentRow = tableList.value[rowIndex]

  // 如果该选项已经在其他位置，先移除
  tableList.value.forEach(item => {
    item.children = item.children.filter(name => name !== option.name)
  })

  // 添加到当前位置
  if (!currentRow.children.includes(option.name)) {
    currentRow.children.push(option.name)
  }

  // 移除高亮效果
  const cell = event.currentTarget as HTMLElement
  cell.classList.remove('highlight-drop')
}

// 处理选项拖拽开始
const handleDragStart = (event: DragEvent, optionName: string) => {
  const option = options.value.find(opt => opt.name === optionName)
  if (option) {
    event.dataTransfer?.setData('text/plain', JSON.stringify(option))
    event.dataTransfer!.effectAllowed = 'move'

    // 创建拖拽图像
    const dragImage = document.createElement('div')
    dragImage.textContent = optionName
    dragImage.style.cssText = `
      position: absolute;
      left: -9999px;
      background: rgb(101, 151, 247);
      color: #fff;
      padding: 8px 16px;
      border-radius: 4px;
      font-size: var(--font-size-small);
      white-space: nowrap;
      pointer-events: none;
      z-index: 9999;
      width: 200px;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
    `
    document.body.appendChild(dragImage)
    event.dataTransfer!.setDragImage(dragImage, 0, 0)

    // 在下一帧移除临时元素
    setTimeout(() => {
      document.body.removeChild(dragImage)
    }, 0)
  }
}

// 添加删除处理函数
const handleDelete = (rowIndex: number, index: number) => {
  const currentRow = tableList.value[rowIndex]
  if (currentRow) {
    currentRow.children.splice(index, 1)
  }
}

const handleDeleteRow = (rowIndex: number) => {
  tableList.value.splice(rowIndex, 1)
}


const validate = () => {
  return new Promise((resolve, reject) => {
    if (tableList.value.length === 0 || options.value.length === 0) {
      ElMessage.error('请添加选项和战略地图')
      reject(new Error('请添加选项'))
    }
    resolve({
      initContent: JSON.stringify(tableList.value),
      taskOptions: JSON.stringify(options.value)
    })
  })
}

onMounted(() => {
  tableList.value = props.initContent ? JSON.parse(props.initContent) : []
  options.value = props.taskOptions ? JSON.parse(props.taskOptions) : []
})

defineExpose({
  validate
})
</script>

<style scoped lang="scss">
.key-point-control {
  @include flex(row, flex-start, flex-start, nowrap);
  gap: 20px;
  position: relative;

  .control-right {
    flex: 1;
    min-width: 0;
    @include flex(column, flex-start, stretch, nowrap);
    gap: 16px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 14px;
    border-radius: 6px;

    .right-header {
      @include flex(row, flex-start, flex-start, nowrap);
      gap: 14px;

      span {
        font-weight: bold;
        color: var(--el-text-color-primary);
      }

      .tip-text {
        color: var(--el-text-color-secondary);
        font-size: 14px;
        font-weight: normal;
      }
    }
  }

  .map-wrapper {
    @include flex(column, flex-start, stretch, nowrap);
    gap: 16px;

    .map-item {
      @include flex(column, flex-start, stretch, nowrap);
      gap: 8px;
      padding-right: 40px;
      position: relative;
    }

    .delete-map {
      position: absolute;
      top: 5px;
      right: -4px;
    }

    .map-name {
      height: 38px;
      background: linear-gradient(180deg, #4BC3FF 0%, #254CCE 100%);
      text-align: center;
      font-weight: 700;
      color: #fff;
      line-height: 38px;
    }

    .map-content {
      padding: 8px;
      border: 1px dashed #2BA0AD;
      min-height: 100px;
      transition: all 0.2s ease;
      position: relative;

      &.highlight-drop {
        border-color: #409EFF;
        background: rgba(64, 158, 255, 0.05);
        box-shadow: 0 0 0 1px #409EFF;
        z-index: 1;

        &::after {
          content: '';
          position: absolute;
          inset: -2px;
          border: 2px dashed #409EFF;
          pointer-events: none;
          animation: borderDash 20s linear infinite;
        }
      }

      .map-desc {
        height: 84px;
        @include flex(row, center, center, nowrap);
        color: var(--el-text-color-secondary);
        font-size: 14px;
      }

      .dropped-items {
        .items-wrapper {
          @include flex(row, flex-start, flex-start, wrap);
          gap: 8px;
          width: 100%;
        }

        .option-item {
          width: calc((100% - 16px) / 3);
          height: 38px;
          background-color: rgb(52, 114, 233, 70%);
          color: #fff;
          padding: 4px 12px;
          border-radius: 4px;
          cursor: move;
          position: relative;
          @include text-ellipsis();

          &:hover {
            .delete-icon {
              opacity: 1;
            }
          }

          .delete-icon {
            position: absolute;
            top: 50%;
            right: 10px;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            cursor: pointer;
            opacity: 0;
            transition: all 0.2s ease;
            color: #fff;
            transform: translateY(-50%);
            @include flex(row, center, center, nowrap);
            background-color: rgba(0, 0, 0, 0.2);

            &:hover {
              transform: translateY(-50%) scale(1.2);
              background-color: rgba(0, 0, 0, 0.4);
            }

            .el-icon {
              font-size: var(--font-size-small);
              font-weight: bold;
            }
          }
        }
      }
    }

    .add-map {
      width: fit-content;
    }
  }
}

@keyframes borderDash {
  to {
    background-position: 100% 100%;
  }
}
</style>
