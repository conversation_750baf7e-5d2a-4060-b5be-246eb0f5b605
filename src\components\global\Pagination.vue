<template>
  <div class="pagination clearfix">
    <template v-if="isSmall">
      <el-pagination :hide-on-single-page="true" small :page-size="pSizes" background layout="prev, pager, next, jumper"
        :total="total" :current-page="curPage" @current-change="handleCurrentChange" @size-change="handleSizeChange"
        v-if="parseInt(`${total}`) > 0" />
    </template>
    <template v-else>
      <el-pagination :page-sizes="pageSizes" :page-size="pSizes" background :layout="layout" :total="total"
        :current-page="curPage" :popper-class="popperClass" @current-change="handleCurrentChange"
        @size-change="handleSizeChange" v-if="parseInt(`${total}`) > 0"></el-pagination>
    </template>
  </div>
</template>

<script setup lang="ts">
import { toRefs } from "vue";
import type { PropType } from "vue";
const props = defineProps({
  popperClass: {
    type: String,
    default: "",
  },
  pageSizes: {
    type: Array as PropType<number[]>,
    default: () => {
      return [10, 20, 30, 40];
    },
  },
  pageSize: {
    type: Number,
    default: 10,
  },
  total: {
    type: Number,
    default: 50,
  },
  currentPage: {
    type: Number,
    default: 1,
  },
  isSmall: {
    type: Boolean,
    default: false,
  },
  layout: {
    type: String,
    default: "total, sizes, prev, pager, next, jumper",
  }
});
const emit = defineEmits(["currentChange", "sizeChange"]);
const { currentPage: curPage } = toRefs(props);
const { pageSize: pSizes } = toRefs(props);

const handleCurrentChange = (current: number) => {
  emit("currentChange", current);
};
const handleSizeChange = (size: number) => {
  emit("sizeChange", size);
};
</script>

<style lang="scss" scoped>
.pagination {
  padding: 10px 0 20px 0;

  :deep(.el-pagination) {
    float: right;
  }
}
</style>
