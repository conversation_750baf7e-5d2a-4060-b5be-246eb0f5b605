<template>
  <div class="option-list-wrapper" :class="{ collapsed }">
    <el-scrollbar>
      <div class="left-content">
        <div class="list-header">
          <div class="header-left">
            <span>选项</span>
            <span class="option-count">{{ options.length }}</span>
          </div>
          <div class="collapse-trigger" @click="handleCollapse">
            <el-icon>
              <CaretLeft />
            </el-icon>
          </div>
        </div>
        <el-input v-model.trim="optionName" placeholder="请输入名称" maxlength="50">
          <template #append>
            <el-button @click="handleAddOption">添加</el-button>
          </template>
        </el-input>
        <div class="option-list">
          <template v-if="options.length">
            <div v-for="(item, index) in options" :key="item.id" class="option-item"
              :class="{ used: usedOptionIds.has(item.id) }"
              :draggable="editingIndex !== index && !usedOptionIds.has(item.id)"
              @dragstart="handleOptionDragStart($event, item)">
              <template v-if="editingIndex === index">
                <el-input class="option-name" v-model.trim="editingName" size="small" @blur="handleEditBlur"
                  maxlength="50" @keyup.enter="handleEditConfirm" ref="editInputRef" @mousedown.stop />
              </template>
              <div v-else class="option-name" :title="item.name" @dblclick="handleDblClick(index, item)">
                {{ item.name }}
              </div>
              <el-icon v-if="!usedOptionIds.has(item.id)" class="delete-icon" @click="handleDeleteOption(index)">
                <Delete />
              </el-icon>
            </div>
          </template>
          <div v-else class="empty-tip">
            <span>暂无选项</span>
          </div>
        </div>
      </div>
    </el-scrollbar>
    <div class="expand-trigger" v-if="collapsed" @click="handleCollapse">
      <el-icon>
        <CaretRight />
      </el-icon>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'
import { Delete, CaretLeft, CaretRight } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import type { OptionItem } from '../../types'
import type { ElInput } from 'element-plus'

const props = defineProps<{
  options: OptionItem[]
  usedOptionIds: Set<string>
}>()

const emit = defineEmits<{
  'update:options': [options: OptionItem[]]
  'toggle-collapse': []
  'dragStart': [event: DragEvent, item: OptionItem]
}>()

const optionName = ref('')
const collapsed = ref(false)
const editingIndex = ref(-1)
const editingName = ref('')
const editInputRef = ref<typeof ElInput>()

const handleAddOption = () => {
  if (!optionName.value.trim()) {
    ElMessage.warning('请输入选项名称')
    return
  }

  if (props.options.some(option => option.name === optionName.value.trim())) {
    ElMessage.warning('选项名称不能重复')
    return
  }

  const newOptions = [...props.options, {
    id: `option_${Date.now()}`,
    name: optionName.value.trim()
  }]

  emit('update:options', newOptions)
  optionName.value = ''
}

const handleDeleteOption = (index: number) => {
  const option = props.options[index]
  if (props.usedOptionIds.has(option.id)) {
    ElMessage.warning('该选项已被使用，不能删除')
    return
  }
  const newOptions = [...props.options]
  newOptions.splice(index, 1)
  emit('update:options', newOptions)
}

const handleOptionDragStart = (event: DragEvent, item: OptionItem) => {
  if (props.usedOptionIds.has(item.id)) {
    event.preventDefault()
    return
  }
  event.dataTransfer?.setData('text/plain', JSON.stringify(item))

  emit('dragStart', event, item)
}

const handleCollapse = () => {
  collapsed.value = !collapsed.value
  emit('toggle-collapse')
}

const handleDblClick = (index: number, item: OptionItem) => {
  if (props.usedOptionIds.has(item.id)) return
  editingIndex.value = index
  editingName.value = item.name
  nextTick(() => {
    editInputRef.value?.[0]?.focus()
  })
}

const handleEditBlur = () => {
  handleEditConfirm()
}

const handleEditConfirm = () => {
  if (editingIndex.value === -1) return

  const trimmedName = editingName.value.trim()
  if (!trimmedName) {
    ElMessage.warning('选项名称不能为空')
    editInputRef.value?.[0]?.focus()
    return
  }

  const duplicateIndex = props.options.findIndex((option, index) =>
    option.name === trimmedName && index !== editingIndex.value
  )

  if (duplicateIndex !== -1) {
    ElMessage.warning('选项名称不能重复')
    editInputRef.value?.[0]?.focus()
    return
  }

  const newOptions = [...props.options]
  newOptions[editingIndex.value] = {
    ...newOptions[editingIndex.value],
    name: trimmedName
  }

  emit('update:options', newOptions)
  editingIndex.value = -1
  editingName.value = ''
}
</script>

<style scoped lang="scss">
.option-list-wrapper {
  height: 85vh;
  @include flex(row, flex-start, stretch, nowrap);
  position: relative;
  transition: all 0.3s ease;
  width: 300px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  padding: 14px;
  border-radius: 6px;

  &.collapsed {
    width: 0;
    margin-right: 0;
    padding-right: 10px;
    box-shadow: unset;

    .left-content {
      opacity: 0;
      transform: translateX(-100%);
    }
  }

  .expand-trigger {
    position: absolute;
    width: 24px;
    height: 24px;
    background-color: var(--el-color-primary);
    border-radius: 4px;
    cursor: pointer;
    @include flex(row, center, center, nowrap);
    color: white;
    margin-right: -4px;

    &:hover {
      background-color: var(--el-color-primary-light-3);
    }
  }

  .left-content {
    flex: 1;
    @include flex(column, flex-start, stretch, nowrap);
    gap: 12px;
    transition: all 0.3s ease;
    opacity: 1;
    transform: translateX(0);
    background-color: var(--el-bg-color);
    z-index: 1;

    .list-header {
      @include flex(row, space-between, center, nowrap);
      padding-right: 24px;
      position: relative;

      .header-left {
        @include flex(row, flex-start, center, nowrap);
        gap: 8px;

        >span {
          font-weight: bold;
          color: var(--el-text-color-primary);

          &.option-count {
            font-weight: normal;
            color: var(--el-text-color-secondary);
            font-size: 14px;
          }
        }
      }

      .collapse-trigger {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        width: 24px;
        height: 24px;
        background-color: var(--el-color-primary);
        border-radius: 4px;
        cursor: pointer;
        @include flex(row, center, center, nowrap);
        color: white;
        z-index: 1;

        &:hover {
          background-color: var(--el-color-primary-light-3);
        }
      }
    }

    .option-list {
      @include flex(column, flex-start, stretch, nowrap);
      gap: 12px;
      padding: 12px;
      background-color: var(--el-fill-color-light);
      border: 1px solid var(--el-border-color);
      border-radius: 4px;
      min-height: 100px;

      .empty-tip {
        height: 100px;
        @include flex(column, center, center, nowrap);
        gap: 8px;
        color: var(--el-text-color-secondary);

        .el-icon {
          font-size: 24px;
        }

        span {
          font-size: 14px;
        }
      }

      .option-item {
        @include flex(row, space-between, center, nowrap);
        padding: 4px 12px;
        background-color: var(--el-bg-color);
        border: 1px solid var(--el-border-color-lighter);
        border-radius: 4px;
        cursor: move;

        .option-name {
          flex: 1;
        }

        &:hover {
          background-color: var(--el-fill-color-light);
        }

        &.used {
          opacity: 0.6;
          cursor: not-allowed;
          background-color: var(--el-fill-color);

          &:hover {
            background-color: var(--el-fill-color);
          }
        }

        div {
          line-height: 1.4;
          max-height: 2.8em;
          overflow: hidden;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          line-clamp: 2;
          word-break: break-all;
        }

        .delete-icon {
          cursor: pointer;
          color: var(--el-text-color-secondary);
          font-size: 16px;
          flex-shrink: 0;
          margin-left: 8px;

          &:hover {
            color: var(--el-color-danger);
          }
        }

        .el-input {
          flex: 1;
          margin-right: 8px;
        }
      }
    }
  }
}
</style>
