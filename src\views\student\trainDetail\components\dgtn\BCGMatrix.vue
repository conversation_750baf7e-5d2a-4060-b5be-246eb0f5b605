<template>
  <div class="point-control">
    <div class="options-wrapper">
      <OptionList :options="options" :used-option-ids="usedOptionIds" :disabled="disabled"
        @drag-start="handleOptionDragStart" />
    </div>
    <div class="base-content content">
      <div class="section-title-light">
        <div class="title-label">{{ taskDetail.questionName }}</div>
      </div>
      <div class="content-wrapper">
        <div class="matrix-container">
          <div class="axis x-axis"></div>
          <div class="axis y-axis"></div>
          <div class="quadrant">
            <div class="quadrant-title">{{ matrixData[0].name }}</div>
            <div class="drop-area" @dragenter="handleDragEnter" @dragleave="handleDragLeave" @dragover.prevent
              @drop="(e) => handleCellDrop(e, 0)">
              <el-scrollbar>
                <div class="drop-area-content">
                  <OptionItem width="100%" v-for="(item, index) in matrixData[0].contents" :key="index" :name="item"
                    :show-delete="!disabled" @delete="() => handleDeleteItem(0, index, item)"
                    @dragstart="handleItemDragStart($event, item, 0)" />
                </div>
              </el-scrollbar>
            </div>
          </div>
          <div class="quadrant">
            <div class="quadrant-title">{{ matrixData[1].name }}</div>
            <div class="drop-area" @dragenter="handleDragEnter" @dragleave="handleDragLeave" @dragover.prevent
              @drop="(e) => handleCellDrop(e, 1)">
              <el-scrollbar>
                <div class="drop-area-content">
                  <OptionItem width="100%" v-for="(item, index) in matrixData[1].contents" :key="index" :name="item"
                    :show-delete="!disabled" @delete="() => handleDeleteItem(1, index, item)"
                    @dragstart="handleItemDragStart($event, item, 1)" />
                </div>
              </el-scrollbar>
            </div>
          </div>
          <div class="quadrant">
            <div class="quadrant-title">{{ matrixData[2].name }}</div>
            <div class="drop-area" @dragenter="handleDragEnter" @dragleave="handleDragLeave" @dragover.prevent
              @drop="(e) => handleCellDrop(e, 2)">
              <el-scrollbar>
                <div class="drop-area-content">
                  <OptionItem width="100%" v-for="(item, index) in matrixData[2].contents" :key="index" :name="item"
                    :show-delete="!disabled" @delete="() => handleDeleteItem(2, index, item)"
                    @dragstart="handleItemDragStart($event, item, 2)" />
                </div>
              </el-scrollbar>
            </div>
          </div>
          <div class="quadrant">
            <div class="quadrant-title">{{ matrixData[3].name }}</div>
            <div class="drop-area" @dragenter="handleDragEnter" @dragleave="handleDragLeave" @dragover.prevent
              @drop="(e) => handleCellDrop(e, 3)">
              <el-scrollbar>
                <div class="drop-area-content">
                  <OptionItem width="100%" v-for="(item, index) in matrixData[3].contents" :key="index" :name="item"
                    :show-delete="!disabled" @delete="() => handleDeleteItem(3, index, item)"
                    @dragstart="handleItemDragStart($event, item, 3)" />
                </div>
              </el-scrollbar>
            </div>
          </div>

        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import OptionList from './OptionList.vue'
import OptionItem from './OptionItem.vue'
import type { OptionItem as OptionItemType } from '@/views/admin/types'
import type { TaskDetailItem } from '../../types'
import * as api from '@/api/student'

interface IProps {
  taskDetail: TaskDetailItem,
  disabled?: boolean
  apiExtraParams?: Record<string, string>
  mode?: 'viewAnswer' | 'answer'
}

interface MatrixQuadrant {
  name: string
  contents: string[]
}

const props = withDefaults(defineProps<IProps>(), {
  disabled: false,
  apiExtraParams: () => ({}),
  mode: 'answer'
})

const isDragging = ref(false)
const options = ref<OptionItemType[]>([])
// 合并配置和数据到单一数组
const matrixData = ref<MatrixQuadrant[]>([
  { name: '', contents: [] },
  { name: '', contents: [] },
  { name: '', contents: [] },
  { name: '', contents: [] }
])
const currentIndex = ref(-1)

const handleOptionDragStart = (option: OptionItemType, event: DragEvent) => {
  if (props.disabled) return
  event.dataTransfer?.setData('text/plain', JSON.stringify(option))
  isDragging.value = true
  currentIndex.value = -1
}

const handleItemDragStart = (event: DragEvent, optionName: string, index: number) => {
  if (props.disabled) return
  const option = options.value.find(opt => opt.name === optionName)
  if (option) {
    event.dataTransfer?.setData('text/plain', JSON.stringify(option))
    isDragging.value = true
    currentIndex.value = index
  }
}

const handleDragEnter = (event: DragEvent) => {
  if (props.disabled) return
  const target = event.currentTarget as HTMLElement
  target.classList.add('highlight-drop')
}

const handleDragLeave = (event: DragEvent) => {
  const target = event.currentTarget as HTMLElement
  target.classList.remove('highlight-drop')
}

const removeOptionFromMatrix = async (optionName: string) => {
  return new Promise(async (resolve, reject) => {
    try {
      let rowIndex = -1
      let quadrantIndex = -1
      matrixData.value.forEach((quadrant, index) => {
        const findIndex = quadrant.contents.indexOf(optionName)
        if (findIndex > -1) {
          rowIndex = index
          quadrantIndex = findIndex
        }
      })
      if (rowIndex > -1) {
        await handleSaveStep({
          content: optionName,
          index: rowIndex,
          operationType: 1
        })
        matrixData.value[rowIndex].contents.splice(quadrantIndex, 1)
      }
      resolve(true)
    } catch (error) {
      console.error(error)
      reject(error)
    }
  })
}

const handleSaveStep = async (answer: {
  content: string,
  index: number,
  operationType: 0 | 1,
}) => {
  return new Promise(async (resolve, reject) => {
    const params = {
      questionId: props.taskDetail.questionId,
      ...props.apiExtraParams,
      ...answer
    }
    try {
      const { data: { code, msg, data } } = await api.analysisRealTime(params)
      if (code === 200) {
        if (data === 1) {
          ElMessage.success('回答正确')
        } else if (data === 0) {
          ElMessage.error('回答错误')
        }
        resolve(true)
      } else {
        ElMessage.error(msg)
        reject(msg)
      }
    } catch (error) {
      console.error(error)
      reject(error)
    }
  })
}

const handleCellDrop = async (event: DragEvent, quadrant: number) => {
  if (props.disabled || !isDragging.value || currentIndex.value === quadrant) return
  const target = event.currentTarget as HTMLElement
  target.classList.remove('highlight-drop')
  const data = event.dataTransfer?.getData('text/plain')
  if (!data) return

  try {
    if (isDragging.value) {
      const option = JSON.parse(data) as OptionItemType
      if (!option || !option.name || !option.id) return
      await removeOptionFromMatrix(option.name)
      await handleSaveStep({
        content: option.name,
        index: quadrant,
        operationType: 0
      })
      matrixData.value[quadrant].contents.push(option.name)
      isDragging.value = false
    }
  } catch (e) {
    isDragging.value = false
    console.error('Invalid drag data format:', e)
  }
}

const handleDeleteItem = (quadrant: number, index: number, item: string) => {
  if (props.disabled) return
  matrixData.value[quadrant].contents.splice(index, 1)
  handleSaveStep({
    content: item,
    index: quadrant,
    operationType: 1
  })
}

const optionsMap = computed(() => {
  const map = new Map<string, string>()
  options.value.forEach(item => {
    map.set(item.name, item.id)
  })
  return map
})


const usedOptionIds = computed(() => {
  const ids = new Set<string>()
  matrixData.value?.forEach(quadrant => {
    quadrant.contents?.forEach(name => {
      const id = optionsMap.value.get(name)
      if (id) ids.add(id)
    })
  })
  return ids
})

const handleDragEnd = () => {
  isDragging.value = false
}

onMounted(() => {
  options.value = props.taskDetail.taskOptions ? JSON.parse(props.taskDetail.taskOptions) : []

  if (props.taskDetail.stuAnswer) {
    matrixData.value = JSON.parse(props.taskDetail.stuAnswer)
  } else {
    const initContent = props.taskDetail.initContent ? JSON.parse(props.taskDetail.initContent) : []
    if (initContent?.length) {
      matrixData.value = initContent.map((item: MatrixQuadrant) => ({
        name: item.name,
        contents: []
      }))
    }
  }

  document.addEventListener('dragend', handleDragEnd)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('dragend', handleDragEnd)
})

const getStuAnswer = () => {
  return Promise.resolve(JSON.stringify(matrixData.value))
}

defineExpose({
  getStuAnswer
})

watch(() => props.taskDetail, (newVal) => {
  if (newVal.stuAnswer) {
    matrixData.value = JSON.parse(newVal.stuAnswer)
  }
})
</script>

<style scoped lang="scss">
.point-control {
  @include flex(row, flex-start, flex-start, nowrap);
  height: 100%;
  gap: 16px;

  .base-content {
    height: 100%;
    gap: 20px;
    @include flex(column, flex-start, flex-start, nowrap);
    position: relative;

    .content-wrapper {
      width: 100%;
      flex: 1;
      min-height: 0;
      padding: 30px 50px;
      background: url('@/assets/images/train/bg-light-task.png') no-repeat bottom center;
      background-size: 100% auto;
    }
  }

  .options-wrapper {
    width: 240px;
    height: 100%;
  }

  .content {
    min-width: 0;
    flex: 1;
    background-image: linear-gradient(to bottom, #fff 0%, rgba(255, 255, 255, 0.5) 100%);
    box-shadow: 0px 2px 4px 0px rgba(120, 147, 187, 0.15);
    border-radius: 6px;
    border: solid 1px #ffffff;
    overflow: hidden;
  }

  .matrix-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-template-rows: 1fr 1fr;
    gap: 50px;
    width: 100%;
    height: 100%;
    position: relative;

    .axis {
      position: absolute;
      background-color: #8a9de1;
      z-index: 1;
    }

    // Vertical axis
    .x-axis {
      top: -10px;
      bottom: 0;
      left: 50%;
      width: 2px;
      transform: translateX(-50%);
    }

    // Horizontal axis
    .y-axis {
      left: 0;
      right: -10px;
      top: 50%;
      height: 2px;
      transform: translateY(-50%);
    }

    // Arrow for vertical axis
    .x-axis::after {
      content: '';
      position: absolute;
      top: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      border-bottom: 10px solid #8a9de1;
    }

    // Arrow for horizontal axis
    .y-axis::after {
      content: '';
      position: absolute;
      right: -10px;
      top: 50%;
      transform: translateY(-50%);
      width: 0;
      height: 0;
      border-top: 6px solid transparent;
      border-bottom: 6px solid transparent;
      border-left: 10px solid #8a9de1;
    }
  }

  .quadrant {
    min-width: 0;
    min-height: 0;
    padding: 40px;
    z-index: 2;
    background: url('@/assets/images/train/bg-matrix.png') no-repeat center center;
    background-size: 100% 100%;
    gap: 32px;
    overflow: hidden;
    @include flex(column, center, flex-start, nowrap);
  }

  .quadrant-title {
    padding: 0 20px;
    height: 34px;
    width: 80%;
    text-align: center;
    line-height: 34px;
    font-size: var(--font-size);
    color: #fff;
    background-image: linear-gradient(90deg,
        transparent 2%,
        #6c74ff 50%,
        transparent 98%);
    background-blend-mode: normal,
      normal;
    box-shadow: 0px -1px 0px 0px rgba(239, 240, 255, 0.45),
      0px 1px 0px 0px rgba(60, 68, 206, 0.08);
    margin: 0 auto;
    flex-shrink: 0;
    @include text-ellipsis();
  }

  .drop-area {
    width: 100%;
    flex: 1;
    min-height: 0;
    background-color: rgba($color: #fff, $alpha: 0.45);
    border: dashed 1px #6d75ff;


    :deep(.option-item) {
      width: calc(50% - 8px) !important;
    }

    &.highlight-drop {
      background-color: rgba(137, 125, 255, 0.2);
      border-color: #897dff;
    }
  }

  .drop-area-content {
    width: 100%;
    height: 100%;
    @include flex(row, flex-start, flex-start, wrap);
    align-content: flex-start;
    gap: 16px;
    overflow: hidden;
    padding: 20px 16px;
  }

  @media screen and (max-width: 1440px) {
    .options-wrapper {
      width: 200px;
    }

    .quadrant {
      padding: 14px;
      gap: 12px;
    }

    .quadrant-title {
      height: 28px;
      line-height: 28px;
    }

    .drop-area-content {
      padding: 10px;
      gap: 10px;
    }
  }
}
</style>
