<template>
  <el-dialog :class="theme === 'light' ? 'cus-dialog-dgth' : 'cus-dialog'" :model-value='visible' title='参考答案'
    fullscreen :before-close='handleClose' append-to-body>
    <template #header>
      <div :class="theme === 'light' ? 'section-title-light' : 'section-title'">
        <div class="title-label">参考答案</div>
      </div>
    </template>
    <div class="answer-container">
      <component v-if="visible" :key="taskDetail?.questionId + 'answer'" mode="viewAnswer" :is="currentComponent"
        ref="componentRef" :enterpriseId="taskDetail?.enterpriseId" :taskDetail="{ ...taskDetail, stuAnswer: answer }"
        disabled />
    </div>
  </el-dialog>
</template>
<script setup lang='ts'>
import Empty from './Empty.vue'
import type { TaskDetailItem } from '../types'
import { computed, defineAsyncComponent } from 'vue'
import { useStore } from 'vuex';

const Organizational = defineAsyncComponent(() => import('./Organizational.vue'))
const StrategyMap = defineAsyncComponent(() => import('./StrategyMap.vue'))
const BusinessFlow = defineAsyncComponent(() => import('./BusinessFlow.vue'))
const PointControl = defineAsyncComponent(() => import('./PointControl.vue'))
const System = defineAsyncComponent(() => import('./System.vue'))
const RiskCoordinate = defineAsyncComponent(() => import('./RiskCoordinate.vue'))
const FlowDesign = defineAsyncComponent(() => import('./dgtn/FlowDesign.vue'))
const StrategyMapFlow = defineAsyncComponent(() => import('./dgtn/StrategyMapFlow.vue'))
const BCGMatrix = defineAsyncComponent(() => import('./dgtn/BCGMatrix.vue'))
const RadarChart = defineAsyncComponent(() => import('./dgtn/RadarChart.vue'))
const TimeAxis = defineAsyncComponent(() => import('./dgtn/TimeAxis.vue'))
const MindMap = defineAsyncComponent(() => import('./dgtn/MindMap.vue'))


interface Props {
  taskDetail: TaskDetailItem | null
  visible?: boolean
}
const props = withDefaults(defineProps<Props>(), {
  visible: false
})

const store = useStore()
const theme = computed(() => {
  return store.getters.theme
})

const emits = defineEmits(['update:visible'])
const handleClose = () => {
  emits('update:visible')
}

const currentComponent = computed(() => {
  if (props.taskDetail?.questionType === 8) {
    return [BCGMatrix, TimeAxis, RadarChart, MindMap][props.taskDetail?.questionSecondType - 1]
  }
  const map = [Organizational, StrategyMap, BusinessFlow, PointControl, System, RiskCoordinate, FlowDesign, StrategyMapFlow]
  return props.taskDetail ? map[props.taskDetail?.questionType] : Empty
})

const answer = computed(() => {
  if (props.taskDetail?.questionType === 2) {
    const flowData = JSON.parse(props.taskDetail?.initContent || '[]')
    flowData.forEach((item: any) => {
      if (item.shape !== "edge" && item.data?.answer) {
        item.attrs.title.text = item.data?.name
        item.attrs.label.text = item.data?.name
      }
    })
    return JSON.stringify(flowData)
  }
  return props.taskDetail?.initContent
})
</script>

<style scoped lang='scss'>
.answer-container {
  height: calc(100vh - 106px);

  :deep {

    .left-wrapper,
    .options-wrapper {
      display: none !important;
    }

    .right-wrapper,
    .base-content {
      flex: 1;
      width: 100%;
      box-shadow: none;

      .section-title,
      .section-title-light {
        display: none;
      }
    }
  }
}
</style>
