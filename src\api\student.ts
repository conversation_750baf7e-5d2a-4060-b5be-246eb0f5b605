import $http from "../http"
import appSettings from "../settings"
const { webAPI } = appSettings

interface Params {
  [key: string]: any
}

//实训详情-左侧菜单
export const getStuTrainContent = (params: Params) => {
  return $http({
    url: webAPI + '/ninBcStuTrain/getStuTrainContent',
    method: 'get',
    params
  })
}
//实训题目详情
export const getStuTrainDetail = (params: Params) => {
  return $http({
    url: webAPI + '/ninBcStuTrain/getStuTrainDetail',
    method: 'get',
    params
  })
}
//实训列表
export const getStuTrainList = (params: Params) => {
  return $http({
    url: webAPI + '/ninBcTrain/getStuTrainList',
    method: 'get',
    params
  })
}

//实训任务保存
export const saveStuAnswer = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcStuTrain/saveStuAnswer',
    method: 'post',
    data
  })
}

//实训任务提交
export const submitStuAnswer = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcStuTrain/submitStuAnswer',
    method: 'post',
    data
  })
}

//前景提要【不再提示】
export const stuQuestionHint = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcStuTrain/stuQuestionHint',
    method: 'post',
    data
  })
}

//战略地图即时反馈【新增和删除】
export const realTimeFeedback = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcStuTrain/realTimeFeedback',
    method: 'post',
    data
  })
}

//战略分析【波士顿矩阵、时间轴、雷达图】即时反馈【新增和删除】
export const analysisRealTime = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcStuTrain/analysisRealTime',
    method: 'post',
    data
  })
}
//思维导图实时反馈
export const mindMappFeedback = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcStuTrain/mindMappFeedback',
    method: 'post',
    data
  })
}

//重置任务
export const resetStuAnswer = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcStuTrain/resetStuAnswer',
    method: 'post',
    data
  })
}
