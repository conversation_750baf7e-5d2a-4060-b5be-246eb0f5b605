<template>
  <div class="risk-coordinate">
    <div class="coordinate-content">
      <div class="sub-title">风险坐标
        <span class="sub-title-desc">请设置风险坐标，点击可以切换颜色。</span>
      </div>
      <div class="coordinate">
        <div class="coordinate-wrapper">
          <div class="coordinate-effect-wrapper">
            <div class="coordinate-effect-title">影响</div>
            <div class="coordinate-effect" v-for="(item, i) in yEffectList" :key="i">
              {{ item }}
            </div>
          </div>
          <div class="coordinate-inner">
            <div class="coordinate-row" v-for="(item, i) in list" :key="i">
              <div class="coordinate-cell" v-for="(child, j) in item" :key="j"
                :class="{ middle: child === 1, high: child === 2 }" @click="handleToggle(i, j)"></div>
            </div>
          </div>
        </div>
        <div class="coordinate-effect-wrapper-x">
          <div class="coordinate-effect-x" v-for="(item, i) in xEffectList" :key="i">
            {{ item }}
          </div>
          <div class="coordinate-effect-title-x">可能性</div>
        </div>
      </div>
      <div class="respond">
        <div class="sub-title">应对：</div>
        <el-input v-model="respond" :validate-event="false" type="textarea" placeholder="" maxlength="500"
          show-word-limit />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { ref, onMounted } from 'vue'

interface IProps {
  taskOptions: string
  initContent: string
}

const props = withDefaults(defineProps<IProps>(), {
  taskOptions: '',
  initContent: ''
})


const respond = ref('')
const xEffectList = ['极低', '低', '中等', '高', '极高']
const yEffectList = ['极高', '高', '中等', '低', '极低']
const list = ref(Array.from(Array(5), () => Array.from(Array(5), () => 0)))
const handleToggle = (i: number, j: number) => {
  const value = list.value[i][j] + 1;
  list.value[i][j] = value > 3 ? 0 : value
}

onMounted(() => {
  if (props.initContent) {
    list.value = JSON.parse(props.initContent)
  }
  if (props.taskOptions) {
    respond.value = props.taskOptions
  }
})

const validate = () => {
  return new Promise((resolve, reject) => {
    if (!respond.value) {
      ElMessage.error('应对不能为空')
      reject(false)
    }
    resolve({
      initContent: JSON.stringify(list.value),
      taskOptions: respond.value
    })
  })
}

defineExpose({
  validate
})
</script>

<style scoped lang="scss">
.risk-coordinate {

  .sub-title {
    font-weight: 700;
    margin-bottom: 10px;
  }

  .sub-title-desc {
    font-size: 12px;
    color: #666;
    line-height: 17px;
    margin-left: 5px;
    font-weight: 300;
  }

  .coordinate {
    display: inline-block;
    background-color: #343a3d;
    padding: 20px 20px 0;
    color: #dedede;
  }

  .coordinate-wrapper {
    @include flex(row, flex-start, flex-start, nowrap);
  }

  .coordinate-effect-wrapper {
    @include flex(column, flex-start, flex-start, nowrap);
    height: 100%;
    padding: 0 15px 10px 20px;
    user-select: none;
    position: relative;

    &::after {
      position: absolute;
      content: '';
      right: -6px;
      top: -2px;
      border: 6px solid transparent;
      border-bottom-color: #fff;
      font-size: 20px;
      transform: translateY(-50%);
    }
  }

  .coordinate-effect-wrapper-x {
    @include flex(row, flex-start, center, nowrap);
    display: inline-flex;
    padding: 16px 0 0 74px;
    position: relative;

    &::after {
      position: absolute;
      content: '';
      right: -19px;
      top: 0;
      border: 6px solid transparent;
      border-left-color: #fff;
      font-size: 20px;
      transform: translateY(-50%);
    }
  }

  .coordinate-effect-title {
    line-height: 28px;
    height: 28px;

  }

  .coordinate-effect {
    line-height: 52px;
    height: 52px;
    margin-bottom: 2px;
  }

  .coordinate-effect-x {
    width: 52px;
    text-align: center;
    margin-right: 2px;
  }

  .coordinate-inner {
    padding: 28px 50px 11px 11px;
    border-bottom: 1px solid #fff;
    border-left: 1px solid #fff;
  }

  .coordinate-row {
    @include flex(row, flex-start, flex-start, nowrap);
    gap: 2px;
    margin-bottom: 2px;

    .coordinate-cell {
      width: 52px;
      height: 52px;
      background: #07D66D;
      cursor: pointer;
      transition: all 0.35s;

      &:hover {
        transform: scale(1.05);
      }

      &.middle {
        background: #1BCBDE;
      }

      &.high {
        background: #254CCE;
      }
    }
  }

  .respond {
    margin-top: 40px;
  }
}
</style>
