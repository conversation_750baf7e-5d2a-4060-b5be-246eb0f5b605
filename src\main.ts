import { createApp } from 'vue'

import App from './App.vue'
import router from './router'
import store from './store'
import './http/interceptor'

import ElementPlus from './plugins/element'
import '@/styles/styles.scss'
import 'virtual:svg-icons-register'

import CKEditor from '@ckeditor/ckeditor5-vue'


const app = createApp(App)

ElementPlus(app)

app.use(store)
app.use(router)
app.use(CKEditor)

app.mount('#app')
