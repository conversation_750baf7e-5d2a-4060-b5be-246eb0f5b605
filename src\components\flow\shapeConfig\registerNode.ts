
import GeneralNode from "./GeneralNode"
import StartNode from "./startNode"
import GatewayNode from "./gatewayNode"
import DataNode from "./dataNode"
import RoundedRectanglNode from "./roundedRectangleNode"
import RectangleNode from "./rectangleNode"
import DecisionN<PERSON> from "./decisionNode"
import <PERSON><PERSON>abe<PERSON> from "./edgeLabel"
import { Graph } from "@antv/x6"

export const registerNode = (graph: typeof Graph) => {
  graph.registerNode('general-node', GeneralNode, true)
  graph.registerNode('start-node', StartNode, true)
  graph.registerNode('gateway-node', GatewayNode, true)
  graph.registerNode('data-node', DataNode, true)
  graph.registerNode('rounded-rectangle-node', RoundedRectanglNode, true)
  graph.registerNode('rectangle-node', RectangleNode, true)
  graph.registerNode('text-node', RectangleNode, true)
  graph.registerNode('decision-node', DecisionNode, true)
  graph.registerEdge('custom-edge', <PERSON><PERSON><PERSON>l, true)
}

export const unregisterNode = (graph: typeof Graph) => {
  graph.unregisterNode('general-node')
  graph.unregisterNode('start-node')
  graph.unregisterNode('gateway-node')
  graph.unregisterNode('data-node')
  graph.unregisterNode('rounded-rectangle-node')
  graph.unregisterNode('rectangle-node')
  graph.unregisterNode('text-node')
  graph.unregisterNode('decision-node')
  graph.unregisterEdge('custom-edge')
}
