<template>
  <div class="teacher-wrap">
    <div class="teacher-main">
      <BackTop>
      </BackTop>

      <div class="head-search">
        <el-form :inline="true" :model="ninSearchForm">
          <el-form-item label="姓名：">
            <el-input v-model="ninSearchForm.studentName" placeholder="请输入姓名" class="mini" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="handleCurrentChange(1, getTableList, true)">
              查询
            </el-button>
          </el-form-item>
        </el-form>
      </div>
      <div>
        <el-table :data="tableData" border>
          <el-table-column label="序号" type="index" width="55" />
          <el-table-column label="学生" prop="studentName" />
          <el-table-column label="总分" prop="totalQuestionScore" />
          <el-table-column label="得分" prop="totalStuScore" />
          <el-table-column label="操作" width="170">
            <template #default="{ row }">
              <el-button type="primary" link @click="handleDetail(row)">得分详情</el-button>
              <el-button type="primary" link @click="handleReset(row)">重置做题</el-button>
            </template>
          </el-table-column>
        </el-table>
        <Pagination :total="pageTotal" :pageSize="pageSize" :currentPage="pageNum"
          @currentChange="handleCurrentChange($event, getTableList)" @sizeChange="handleSizeChange($event, getTableList)">
        </Pagination>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessageBox, ElMessage } from 'element-plus'
import BackTop from './components/BackTop.vue';
import * as api from "@/api/teacher";
import { Search } from '@element-plus/icons-vue';
import Pagination from "@/components/global/Pagination.vue";
import { TableOptions } from "@/utils/tableOptions";
import { ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const t2 = new TableOptions();
const { loading, pageNum, pageSize, pageTotal, tableData, ninSearchForm, tempSearchForm } = t2;
const { handleCurrentChange, handleSizeChange } = t2;

const route = useRoute(),
  router = useRouter();

const trainId = ref(route.query.trainId as string)

const getTableList = async () => {
  try {
    loading.value = true;
    const params: Record<string, string | number> = {
      page: pageNum.value,
      size: pageSize.value,
      trainId: trainId.value,
      ...tempSearchForm.value
    };
    for (const key in tempSearchForm.value) {
      if (!tempSearchForm.value[key]) {
        delete params[key];
      }
    }
    const { data: { code, msg, data } } = await api.getStudentTrainList(params);
    if (code === 200) {
      tableData.value = data.list;
      pageTotal.value = parseInt(data.total);
    } else {
      ElMessage.error(msg)
    }
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

const handleDetail = (row: any) => {
  const { href } = router.resolve({ path: '/student/trainDetail', query: { trainId: trainId.value, studentId: row.studentId } })
  window.open(href, '_blank')
}

const handleReset = async (row: any) => {
  ElMessageBox.confirm(
    '是否确认重置该学生的做题记录？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      const { data: { code, msg } } = await api.updateStuAnswer({ studentId: row.studentId, trainId: trainId.value });
      if (code === 200) {
        ElMessage.success(msg)
        getTableList()
      } else {
        ElMessage.error(msg)
      }
    } catch (error) {
      console.log(error)
    }
  }).catch(() => { })

}

watch(() => route.query, () => {
  // getClassList()
  getTableList()
}, { deep: true, immediate: true })
</script>

<style lang="scss" scoped>
@use "@/styles/teacher";
</style>
