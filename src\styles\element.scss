.cus-dialog.el-dialog {
  border-radius: 0;
  background-image: linear-gradient(180deg, #002752c2, #002752f2);
  border: 1px solid;
  border-image: linear-gradient(180deg, #31D2FF, #002752) 1;
  padding: 0;

  .el-dialog__header {
    padding: 10px 20px;
  }

  .el-dialog__headerbtn {
    top: 6px;

    .el-dialog__close {
      font-size: 24px;
      color: #fff;
    }
  }

  .el-dialog__body {
    padding: 20px;
    color: #fff;
  }
}

.cus-dialog-dgth.el-dialog {
  border-radius: 12px;
  padding: 0;

  // &::before {
  //   display: inline-block;
  //   content: '';
  //   position: absolute;
  //   top: 0;
  //   left: 0;
  //   width: 100%;
  //   height: 48px;
  //   border-radius: 12px 12px 0 0;
  //   background-image: linear-gradient(to bottom,
  //       #d7e1fa 0%,
  //       transparent 100%);
  // }

  .el-dialog__header {
    height: 48px;
    position: relative;
    border-radius: 12px 12px 0 0;
    // border-bottom: 1px solid #c8ddfb;
    // line-height: 48px;
    overflow: hidden;

    // &::after {
    //   display: inline-block;
    //   content: '';
    //   position: absolute;
    //   bottom: 0;
    //   left: 0;
    //   width: 24px;
    //   height: 3px;
    //   background-color: #507af1;
    // }

    &.show-close {
      padding: 0;
    }
  }

  .el-dialog__title {
    --el-text-color-primary: #313052;
    font-size: 16px;
  }

  .el-dialog__headerbtn {
    top: 4px;
    z-index: 1;

    &:hover {
      .el-dialog__close {
        color: #507af1;
      }
    }

    .el-dialog__close {
      font-size: 18px;
      color: #313052;
    }
  }

  .el-dialog__body {
    color: #313052;
    padding: 20px;
  }

  .el-dialog__footer {
    padding-right: 20px;
    padding-left: 20px;
  }
}
