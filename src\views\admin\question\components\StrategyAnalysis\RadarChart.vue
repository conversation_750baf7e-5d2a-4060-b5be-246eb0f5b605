<template>
  <div class="key-point-control">
    <OptionList v-model:options="options" :used-option-ids="usedOptionIds" />
    <div class="control-right">
      <div class="right-header">
        <span>波特五力雷达图分析</span>
        <span class="tip-text">拖拽选项到对应的力量区域</span>
      </div>

      <div class="radar-wrapper">
        <div class="radar-title">
          <h3>波特五力模型雷达图</h3>
        </div>

        <div class="radar-container">
          <div class="radar-chart">
            <svg class="radar-data" viewBox="0 0 400 400">
              <polygon :points="radarPoints" fill="rgba(64, 158, 255, 0.3)" stroke="#409EFF" stroke-width="2" />
              <circle v-for="(point, index) in radarPointsArray" :key="index" :cx="point.x" :cy="point.y" r="4"
                fill="#409EFF" />
            </svg>
            <div class="force-labels">
              <div v-for="(force, index) in forces" :key="index" class="force-label" :class="getLabelPosition(index)">
                <div class="force-header">
                  <el-input v-model="force.name" placeholder="请输入名称" class="force-name-input" size="small"
                    :maxlength="20" show-word-limit />
                </div>
                <div class="drop-area" @dragenter.prevent="handleDragEnter" @dragover.prevent
                  @dragleave="handleDragLeave" @drop.prevent="handleForceDrop($event, index)">
                  <div v-if="force.contents.length === 0" class="content-placeholder">
                    拖拽选项到此处
                  </div>
                  <el-scrollbar v-else height="100%">
                    <div class="content-items">
                      <div v-for="(option, optionIndex) in force.contents" :key="optionIndex" class="content-item"
                        draggable="true" @dragstart="handleItemDragStart($event, option)" :title="option">
                        {{ option }}
                        <div class="delete-icon" @click.stop="handleDeleteItem(index, optionIndex)">
                          <el-icon>
                            <Close />
                          </el-icon>
                        </div>
                      </div>
                    </div>
                  </el-scrollbar>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Close } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import OptionList from '../OptionList.vue'
import type { OptionItem } from '../../../types'

const props = defineProps({
  initContent: {
    type: String,
    default: ''
  },
  taskOptions: {
    type: String,
    default: ''
  }
})

interface ForceData {
  name: string
  contents: string[]
}

const options = ref<OptionItem[]>([])
const forces = ref<ForceData[]>([
  { name: '', contents: [] },
  { name: '', contents: [] },
  { name: '', contents: [] },
  { name: '', contents: [] },
  { name: '', contents: [] }
])

const optionsMap = computed(() => {
  const map = new Map<string, string>()
  options.value.forEach(item => {
    map.set(item.name, item.id)
  })
  return map
})

// 计算已使用的选项ID集合
const usedOptionIds = computed(() => {
  const ids = new Set<string>()
  forces.value.forEach(force => {
    force.contents.forEach(option => {
      const id = optionsMap.value.get(option)
      if (id) ids.add(id)
    })
  })
  return ids
})

// 计算雷达图数据点
const radarPointsArray = computed(() => {
  const centerX = 200
  const centerY = 200
  const maxRadius = 160

  return forces.value.map((_, index) => {
    const angle = (index * 72 - 90) * Math.PI / 180
    const radius = (3 / 5) * maxRadius
    return {
      x: centerX + radius * Math.cos(angle),
      y: centerY + radius * Math.sin(angle)
    }
  })
})

// 计算雷达图多边形点坐标字符串
const radarPoints = computed(() => {
  return radarPointsArray.value.map(point => `${point.x},${point.y}`).join(' ')
})

// 获取标签位置类名
const getLabelPosition = (index: number) => {
  const positions = ['top', 'right-top', 'right-bottom', 'left-bottom', 'left-top']
  return positions[index]
}

// 处理拖拽进入
const handleDragEnter = (event: DragEvent) => {
  const element = event.currentTarget as HTMLElement
  element.classList.add('highlight-drop')
}

// 处理拖拽离开
const handleDragLeave = (event: DragEvent) => {
  const element = event.currentTarget as HTMLElement
  element.classList.remove('highlight-drop')
}

// 处理力量区域拖拽放置
const handleForceDrop = (event: DragEvent, forceIndex: number) => {
  const data = event.dataTransfer?.getData('text/plain')
  if (!data) return

  const option = JSON.parse(data) as OptionItem
  removeOptionFromOtherPlaces(option.name)
  forces.value[forceIndex].contents.push(option.name)

  const element = event.currentTarget as HTMLElement
  element.classList.remove('highlight-drop')
}

// 移除选项从其他位置
const removeOptionFromOtherPlaces = (optionName: string) => {
  forces.value.forEach(force => {
    const index = force.contents.indexOf(optionName)
    if (index !== -1) {
      force.contents.splice(index, 1)
    }
  })
}

// 拖拽开始处理
const handleItemDragStart = (event: DragEvent, itemName: string) => {
  const option = options.value.find(opt => opt.name === itemName)
  if (option) {
    event.dataTransfer?.setData('text/plain', JSON.stringify(option))
    createDragImage(event, itemName)
  }
}

// 创建拖拽图像
const createDragImage = (event: DragEvent, text: string) => {
  const dragImage = document.createElement('div')
  dragImage.textContent = text
  dragImage.style.cssText = `
    position: absolute;
    left: -9999px;
    background: rgb(101, 151, 247);
    color: #fff;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: var(--font-size-small);
    white-space: nowrap;
    pointer-events: none;
    z-index: 9999;
    width: 200px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
  `
  document.body.appendChild(dragImage)
  event.dataTransfer!.setDragImage(dragImage, 0, 0)

  setTimeout(() => {
    document.body.removeChild(dragImage)
  }, 0)
}

// 删除项目
const handleDeleteItem = (forceIndex: number, optionIndex: number) => {
  forces.value[forceIndex].contents.splice(optionIndex, 1)
}

// 验证函数
const validate = () => {
  return new Promise((resolve, reject) => {
    if (options.value.length === 0) {
      ElMessage.error('请添加选项')
      reject(new Error('请添加选项'))
      return
    }

    const hasItems = forces.value.some(force => force.contents.length > 0)
    if (!hasItems) {
      ElMessage.error('请在波特五力模型中添加至少一个选项')
      reject(new Error('请在波特五力模型中添加选项'))
      return
    }

    resolve({
      initContent: JSON.stringify(forces.value),
      taskOptions: JSON.stringify(options.value)
    })
  })
}

onMounted(() => {
  if (props.initContent) {
    try {
      const data = JSON.parse(props.initContent) as ForceData[]
      if (Array.isArray(data) && data.length === 5) {
        forces.value = data.map(force => ({
          name: force.name,
          contents: Array.isArray(force.contents) ? [...force.contents] : []
        }))
      }
    } catch (error) {
      console.error('Failed to parse initContent:', error)
    }
  }
  options.value = props.taskOptions ? JSON.parse(props.taskOptions) : []
})

defineExpose({
  validate
})
</script>

<style scoped lang="scss">
.key-point-control {
  @include flex(row, flex-start, flex-start, nowrap);
  gap: 20px;
  position: relative;
  width: 100%;

  .control-right {
    flex: 1;
    min-width: 0;
    @include flex(column, flex-start, stretch, nowrap);
    gap: 16px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 14px;
    border-radius: 6px;
    overflow: hidden;

    .right-header {
      @include flex(row, flex-start, flex-start, nowrap);
      gap: 14px;

      span {
        font-weight: bold;
        color: var(--el-text-color-primary);
      }

      .tip-text {
        color: var(--el-text-color-secondary);
        font-size: 14px;
        font-weight: normal;
      }
    }
  }

  .radar-wrapper {
    @include flex(column, flex-start, stretch, nowrap);
    gap: 30px;
    overflow-y: auto;

    .radar-title {
      text-align: center;

      h3 {
        margin: 0;
        color: var(--el-text-color-primary);
        font-size: 20px;
        font-weight: bold;
      }
    }

    .radar-container {
      @include flex(column, flex-start, center, nowrap);
      gap: 30px;
      padding: 20px;

      .radar-chart {
        position: relative;
        width: 400px;
        height: 400px;
        margin: 0 auto;

        .radar-data {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          pointer-events: none;
        }

        .force-labels {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;

          .force-label {
            position: absolute;
            font-size: 12px;
            color: #333;
            font-weight: bold;
            text-align: center;
            width: 180px;
            line-height: 1.2;

            &.top {
              top: -10px;
              left: 50%;
              transform: translateX(-50%);
            }

            &.right-top {
              top: 25%;
              right: -120px;
            }

            &.right-bottom {
              bottom: 10%;
              right: -80px;
            }

            &.left-bottom {
              bottom: 10%;
              left: -80px;
            }

            &.left-top {
              top: 25%;
              left: -120px;
            }
          }
        }

        .drop-area {
          height: 90px;
          border: 2px dashed #d9d9d9;
          border-radius: 8px;
          transition: all 0.2s ease;
          margin-top: 10px;
          overflow-y: auto;
          background-color: rgba(255, 255, 255, 0.7);
          backdrop-filter: blur(2px);

          &.highlight-drop {
            border-color: #67C23A;
            background: rgba(103, 194, 58, 0.1);
            box-shadow: 0 0 8px rgba(103, 194, 58, 0.3);
          }

          .content-placeholder {
            height: 100%;
            @include flex(row, center, center, nowrap);
            color: var(--el-text-color-placeholder);
            font-size: 13px;
            text-align: center;
          }

          .content-items {
            @include flex(column, flex-start, stretch, nowrap);
            gap: 8px;
            padding: 8px;
          }
        }

        .content-item {
          width: 100%;
          padding: 8px 10px;
          border-radius: 6px;
          cursor: move;
          position: relative;
          font-size: 12px;
          line-height: 1.4;
          transition: all 0.3s ease;
          background: linear-gradient(135deg, #409EFF, #5DADE2);
          color: #fff;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          @include text-ellipsis;

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);

            .delete-icon {
              opacity: 1;
            }
          }
        }
      }
    }
  }

  // 删除图标样式
  .delete-icon {
    position: absolute;
    top: 50%;
    right: 4px;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    transition: all 0.2s ease;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.4);
    transform: translateY(-50%);
    @include flex(row, center, center, nowrap);

    &:hover {
      transform: scale(1.2) translateY(-50%);
      background-color: rgba(0, 0, 0, 0.6);
    }

    .el-icon {
      font-size: 10px;
      font-weight: bold;
    }
  }
}
</style>
