<template>
  <el-dialog :model-value='visible' :title='dialogType === "add" ? "新增分类" : "编辑分类"' width='600px'
    :before-close='handleClose'>
    <el-form :model="form" ref="formRef" :rules="rules" label-width="100px">
      <el-form-item label="分类名称" prop="categoryName">
        <el-input v-model.trim="form.categoryName" placeholder="请输入分类名称，20字内" maxlength="20" show-word-limit />
      </el-form-item>
      <el-form-item label="分类图标" prop="categoryIcon">
        <div class="icon-list">
          <el-popover title="" content="" trigger="click" placement="bottom-start">
            <div class="box-item" v-for="item in iconList" :key="item.value">
              <el-image :src="item.image" style="width: 20px; height: 20px;" @click="form.categoryIcon = item.value" />
            </div>
            <template #reference>
              <el-image :src="icon" style="width: 32px; height: 32px;"></el-image>
            </template>
          </el-popover>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submitForm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang='ts'>
import { ref, reactive, watch, computed, type PropType } from 'vue'
import { addEditCategory } from '@/api/admin'
import { ElMessage } from 'element-plus'
import type { CategoryItem, ImageModule } from '@/views/admin/types'

const imagesList: Record<string, ImageModule> = import.meta.glob('../../../../assets/images/taskIcon/*.png', { eager: true })
const iconList = Object.keys(imagesList).map((key: string) => {
  return {
    value: key.split('/').pop()?.split('.')[0].split('-').pop() || '',
    image: imagesList[key].default
  }
}).sort((a, b) => +a.value - +b.value)

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dialogType: {
    type: String,
    default: 'add'
  },
  editData: {
    type: Object as PropType<CategoryItem | null>,
    default: () => ({})
  }
})

const emits = defineEmits(['update:visible', 'success'])
const formRef = ref()
const loading = ref(false)
const form = reactive({
  categoryIcon: '1',
  categoryName: '',
})

const icon = computed(() => {
  return iconList.find(item => item.value === form.categoryIcon)?.image
})

watch(() => props.visible, (newVal) => {
  if (newVal) {
    if (props.dialogType === 'edit' && props.editData) {
      form.categoryName = props.editData.categoryName
      form.categoryIcon = props.editData.categoryIcon
    }
    formRef.value?.clearValidate()
  }
})


const rules = {
  categoryName: [
    { required: true, message: '请输入分类名称', trigger: 'blur' },
  ],
  categoryIcon: [
    { required: true, message: '请选择分类图标', trigger: 'change' }
  ]
}

const handleClose = () => {
  formRef.value?.resetFields()
  // 重置表单数据
  Object.assign(form, {
    categoryIcon: '1',
    categoryName: '',
  })
  emits('update:visible', false)
}

const submitForm = async () => {
  if (!formRef.value) return
  try {
    await formRef.value.validate()
    loading.value = true

    const { data: { code, msg } } = await addEditCategory({ ...form, categoryId: props.editData?.categoryId });
    if (code === 200) {
      ElMessage.success('保存成功')
      emits('success', form)
      handleClose()
    } else {
      ElMessage.error(msg || '保存失败')
    }
  } catch (error) {
    console.log('表单验证失败:', error)
  } finally {
    loading.value = false // 关闭加载状态
  }
}
</script>

<style scoped lang='scss'>
.w-100 {
  width: 100%;
}

.icon-list {
  cursor: pointer;
}

.box-item {
  cursor: pointer;
  display: inline-block;
  padding: 2px;

  &:hover {
    background-color: #eee;
    border-radius: 4px;
    transition: all 0.2s ease;
  }
}
</style>
