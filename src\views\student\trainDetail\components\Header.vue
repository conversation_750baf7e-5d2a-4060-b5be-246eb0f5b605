<template>
  <div class="header">
    <div class="left flex items-center">
      <img src="@/assets/images/icon/icon-train.png" alt="" class="train-logo">
      <span>{{ trainName }}</span>
    </div>
    <div class="center">
      <div class="task-num line">
        <div class="num">共计 <span>{{ taskNum }}</span> 个任务</div>
        <div class="num">完成 <span>{{ finishNum }}</span> 个任务</div>
      </div>
      <template v-if="taskDetail?.questionId">
        <div class="line">
          <!-- <el-button class="task-btn">任务</el-button> -->
        </div>
        <div class="task-num ">
          <div class="num">
            <div class="task-name" :title="taskDetail.questionName">{{ taskDetail.questionName }}</div>
            (<template v-if="taskDetail.ifFinish">
              {{ taskDetail.stuScore }}分/
            </template>
            {{ taskDetail.questionScore }} 分）
          </div>
        </div>
      </template>
    </div>
    <div class="right">
      <template v-if="taskDetail?.questionId">
        <div class="line"></div>
        <el-button class="primary-btn" type="primary" @click="handleShowDesc(0)">集团背景</el-button>
        <el-button class="primary-btn" type="primary" @click="handleShowDesc(1)">前景提要</el-button>
        <el-button class="primary-btn" type="primary" @click="visibleAttach = true">附件资料</el-button>
        <el-button v-if="!disabled" class="yellow-btn" type="primary" @click="handleSubmit"
          :disabled="disabled">保存</el-button>
        <el-button v-if="taskDetail?.showAnswer" class="primary-btn" type="primary"
          @click="visibleAnswer = true">查看答案</el-button>
      </template>
      <el-button v-if="platformCode === 'ninBusinessCanvas'" class="" type="primary" :icon="SwitchButton"
        @click="handleQuit">退出</el-button>
    </div>
    <Answer v-model:visible="visibleAnswer" :title="descTitle" :taskDetail="taskDetail"></Answer>
    <TaskDesc v-model:visible="visibleDesc" :title="descTitle" :taskDesc="taskDesc"></TaskDesc>
    <AttachmentList v-model:visible="visibleAttach" :attachments="taskDetail?.ninBcQuestionFileList"></AttachmentList>
  </div>
</template>

<script setup lang="ts">
import Answer from './Answer.vue'
import TaskDesc from './TaskDesc.vue'
import AttachmentList from './AttachmentList.vue'
import { SwitchButton } from '@element-plus/icons-vue'
import { computed, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import type { TaskDetailItem, TaskListItem } from '../types'
import { decodeName } from '@/utils/tools'
import { useStore } from 'vuex';

interface Props {
  taskList: TaskListItem[]
  taskDetail: TaskDetailItem | null
  disabled?: boolean,
}

const store = useStore()
const router = useRouter()
const route = useRoute()

const props = withDefaults(defineProps<Props>(), {
  taskList: () => [],
  disabled: false
})

const emit = defineEmits(['submit'])

const taskNum = computed(() => {
  return props.taskList.reduce((total, item) => {
    return total + item.questionList.length
  }, 0)
})

const trainName = computed(() => {
  return route.query.trainName ? decodeName(route.query.trainName as string) : '实训任务'
})

const platformCode = computed(() => {
  return route.query.platformCode as string
})

const finishNum = computed(() => {
  let total = 0
  props.taskList.forEach(item => {
    item.questionList.forEach(task => {
      if (task.ifFinish) total++
    })
  })
  return total
})

const visibleDesc = ref(false)
const visibleAttach = ref(false)
const taskDesc = ref('');
const descTitle = ref('前景提要');
const handleShowDesc = (flag: 0 | 1) => {
  descTitle.value = flag === 0 ? '集团背景' : '前景提要'
  taskDesc.value = (flag === 0 ? props.taskDetail?.taskNote : props.taskDetail?.taskDesc) || ''
  visibleDesc.value = true
}

const handleQuit = () => {
  store.dispatch('userLogout')
  router.replace('/login')
}

const handleSubmit = () => {
  emit('submit')
}

const visibleAnswer = ref(false)
</script>

<style scoped lang="scss">
.header {
  @include flex(row, space-between, center, nowrap);
  color: #fff;

  :deep {
    .el-button {
      height: 36px;
      flex-shrink: 0;
      background: url('@/assets/images/btn/bg-btn-4.png') no-repeat center;
      background-size: 100% 100%;
      border: 0;
      border-radius: 0;
      padding: 0;
      text-align: center;
      color: #001e22;


      &.yellow-btn {
        background: url('@/assets/images/btn/bg-btn-2.png') no-repeat center;
        background-size: 100% 100%;
        color: #875D0C;
      }

      &.primary-btn {
        background: url('@/assets/images/btn/bg-btn-5.png') no-repeat center;
        background-size: 100% 100%;
        color: #fff;

        &:hover:not(.is-disabled) {
          color: #001e22;
        }
      }

      span {
        font-size: var(--font-size-large);
        font-family: "YouSheBiaoTiHei";
      }

      &:hover:not(.is-disabled) {
        color: #fff;
      }

      &.is-disabled {
        filter: grayscale(100%);
      }
    }
  }

  .left {
    font-size: 24px;
    font-weight: 700;
  }

  .center {
    margin-left: 24px;
    @include flex(row, flex-start, center, nowrap);
    gap: 16px;
  }

  .train-logo {
    width: 60px;
    height: 64px;
  }

  .train-name {
    font-weight: 700;
  }

  .line {
    position: relative;
    padding-left: 30px;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 50%;
      width: 9px;
      height: 34px;
      background: rgba($color: #fff, $alpha: .15);
      transform: translateY(-50%) skewX(-17deg);
    }
  }

  .task-num {
    font-size: var(--font-size-small);
    line-height: 18px;

    .num {
      padding-left: 15px;
      background: left center url(@/assets/images/icon/icon-1.png) no-repeat;
      background-size: 11px 10px;
      @include flex(row, start center, nowrap);
    }

    span {
      color: #DBB642;
      padding: 0 5px;
    }
  }

  .task-btn {
    width: 138px;
  }

  .right {
    flex: 1;
    @include flex(row, flex-end, center, nowrap);
    position: relative;
    padding-left: 6px;
    padding-right: 74px;


    &::after {
      content: "";
      position: absolute;
      right: -30px;
      top: 50%;
      width: 100px;
      height: 34px;
      background: rgba($color: #fff, $alpha: .15);
      transform: translateY(-50%) skewX(-17deg);
    }

    .line {
      padding-left: 16px;
    }

    .el-button {
      flex: 1;
      max-width: 138px;
    }


    .el-button+.el-button {
      margin-left: 0;
    }

    .blue {
      background: url('@/assets/images/btn/bg-btn-4.png') no-repeat center;
      background-size: 100% 100%;
    }
  }

  @media screen and (max-width: 1440px) {
    .el-button {
      height: 28px;
    }

    .left {
      font-size: 20px;
    }

    .task-btn {
      width: 102px;
    }

    .line {
      padding-left: 20px;
    }

    .right {
      padding-right: 54px;

      .el-button {
        max-width: 102px;
      }

      &::after {
        transform: translateY(-50%) skewX(-10deg);
        width: 80px;
      }
    }
  }
}
</style>
