<template>
  <el-dialog :class='theme === "light" ? "cus-dialog-dgth" : "cus-dialog"' :model-value='visible' title='任务说明'
    width='700px' :modal="false" :before-close='handleClose'>
    <template #header>
      <div :class="theme === 'light' ? 'section-title-light' : 'section-title'">
        <div class="title-label">附件资料</div>
      </div>
    </template>
    <div class="attachment-list" :class="theme === 'light' ? 'light' : ''">
      <div v-if="attachments.length" class="list-content">
        <div v-for="item in attachments" :key="item.questionFileId" class="attachment-item"
          @click="handleDownload(item)">
          <div class="file-info">
            <el-icon class="file-icon">
              <component :is="getFileIcon(item.fileName)" />
            </el-icon>
            <span class="file-name">{{ item.fileName }}</span>
          </div>
          <el-icon class="download-icon">
            <Download />
          </el-icon>
        </div>
      </div>
      <el-empty v-else description="暂无附件" />
    </div>
  </el-dialog>
</template>

<script setup lang='ts'>
import { Document, Download, VideoCamera, Picture, Folder } from '@element-plus/icons-vue'
import type { NinFileItem } from '../types'
import settings from '@/settings'
import { computed } from 'vue'
import { useStore } from 'vuex';

defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  attachments: {
    type: Array as () => NinFileItem[],
    default: () => []
  }
})
const store = useStore()
const theme = computed(() => {
  return store.getters.theme
})

const emits = defineEmits(['update:visible'])

const handleClose = () => {
  emits('update:visible', false)
}

const handleDownload = (file: NinFileItem) => {
  window.open(settings.webAPI + '/' + file.filePath)
}

const getFileIcon = (fileName: string) => {
  const ext = fileName.split('.').pop()?.toLowerCase()
  switch (ext) {
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
      return Picture
    case 'mp4':
    case 'avi':
    case 'mov':
      return VideoCamera
    case 'zip':
    case 'rar':
    case '7z':
      return Folder
    default:
      return Document
  }
}
</script>

<style scoped lang='scss'>
.attachment-list {
  padding: 8px;

  &.light {
    .attachment-item {
      background: #F5F7FA;
      border: 1px solid #E4E7ED;
      border-image: none;

      &::before {
        background: #507af1;
        opacity: 1;
      }

      &:hover {
        background: #EBEEF5;
        border-color: #507af1;
        box-shadow: 0 0 15px rgba(64, 158, 255, 0.1);

        .download-icon {
          color: #507af1;
        }

        &::before {
          opacity: 1;
        }
      }

      .file-info {
        .file-icon {
          color: #507af1;
          filter: drop-shadow(0 0 3px rgba(64, 158, 255, 0.3));
        }

        .file-name {
          color: #303133;
        }
      }

      .download-icon {
        color: #909399;
      }
    }
  }

  .list-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }

  .attachment-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 14px;
    background: rgba(0, 21, 40, 0.35);
    border: 1px solid;
    border-image: linear-gradient(90deg, rgba(49, 210, 255, 0.3), transparent) 1;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.25s ease-in-out;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      background: #31D2FF;
      opacity: 0.3;
    }

    &:hover {
      background: rgba(0, 21, 40, 0.8);
      border-image: linear-gradient(90deg, rgba(49, 210, 255, 0.5), rgba(49, 210, 255, 0.1)) 1;
      box-shadow: 0 0 15px rgba(49, 210, 255, 0.1);

      .download-icon {
        color: #31D2FF;
      }

      &::before {
        opacity: 1;
      }
    }

    .file-info {
      display: flex;
      align-items: center;
      gap: 12px;
      flex: 1;
      min-width: 0;

      .file-icon {
        font-size: 24px;
        color: #31D2FF;
        filter: drop-shadow(0 0 3px rgba(49, 210, 255, 0.3));
      }

      .file-name {
        color: #fff;
        font-size: 14px;
        flex: 1;
        min-width: 0;
        @include text-ellipsis();
      }
    }

    .download-icon {
      font-size: 20px;
      color: rgba(255, 255, 255, 0.6);
      transition: all 0.25s ease-in-out;
      margin-left: 16px;
    }
  }
}

:deep(.el-dialog) {
  &.cus-dialog {
    background: #001528;
    border: 1px solid;
    border-image: linear-gradient(180deg, #31D2FF, #002752) 1;

    .el-dialog__header {
      padding: 0;
      margin: 0;
    }

    .el-dialog__body {
      padding: 0;
      color: #fff;
    }
  }

  &.cus-dialog-dgth {
    background: #FFFFFF;
    border: 1px solid #E4E7ED;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .el-dialog__header {
      padding: 0;
      margin: 0;
    }

    .el-dialog__body {
      padding: 0;
      color: #303133;
    }
  }
}
</style>
