<template>
  <div class="point-control">
    <div class="options-wrapper">
      <OptionList :options="options" :used-option-ids="usedOptionIds" :disabled="disabled"
        @drag-start="handleOptionDragStart" />
    </div>
    <div class="base-content content">
      <div class="section-title">
        <div class="title-label">{{ taskDetail.questionName }}</div>
      </div>
      <div class="content-wrapper ">
        <el-scrollbar height="100%">
          <div class="map-wrapper">
            <div class="map-item" v-for="(item, rowIndex) in tableList" :key="rowIndex">
              <div class="map-name">{{ item.name }}</div>
              <div class="map-content" @drop="handleDrop($event, rowIndex)" @dragover="handleDragOver"
                @dragenter="handleDragEnter($event)" @dragleave="handleDragLeave($event)">
                <div v-if="item.children.length === 0" class="map-desc">
                  请将左侧选项拖动到此区域
                </div>
                <div v-else class="dropped-items">
                  <!-- <TransitionGroup name="item" tag="div" class="items-wrapper"> -->
                  <div class="items-wrapper">
                    <div v-for="(optionName, index) in item.children" :key="index" class="option-item"
                      :draggable="!disabled" @dragstart="handleDragStart($event, optionName!)"
                      :title="String(optionName)">
                      {{ optionName }}
                      <div v-if="!disabled" class="delete-icon" @click.stop="handleDelete(rowIndex, index)">
                        <el-icon>
                          <Close />
                        </el-icon>
                      </div>
                    </div>
                  </div>
                  <!-- </TransitionGroup> -->
                </div>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Close } from '@element-plus/icons-vue'
import OptionList from './OptionList.vue'
import type { OptionItem } from '@/views/admin/types'
import type { TaskDetailItem } from '../types'

interface IProps {
  taskDetail: TaskDetailItem,
  disabled?: boolean
}

interface TableItem {
  name: string
  children: string[]  // 改为存储选项名称
}

const props = withDefaults(defineProps<IProps>(), {
  disabled: false
})

const options = ref<OptionItem[]>([])
const tableList = ref<TableItem[]>([])

// 处理选项拖拽开始
const handleDragStart = (event: DragEvent, optionName: string) => {
  const option = options.value.find(opt => opt.name === optionName)
  if (option) {
    event.dataTransfer?.setData('text/plain', JSON.stringify(option))
    event.dataTransfer!.effectAllowed = 'move'

    // 创建拖拽图像
    const dragImage = document.createElement('div')
    dragImage.textContent = optionName
    dragImage.style.cssText = `
      position: absolute;
      left: -9999px;
      background: rgb(101, 151, 247);
      color: #fff;
      padding: 8px 16px;
      border-radius: 4px;
      font-size: var(--font-size-small);
      white-space: nowrap;
      pointer-events: none;
      z-index: 9999;
      width: 200px;
      text-align: center;
      overflow: hidden;
      text-overflow: ellipsis;
    `
    document.body.appendChild(dragImage)
    event.dataTransfer!.setDragImage(dragImage, 0, 0)

    // 在下一帧移除临时元素
    setTimeout(() => {
      document.body.removeChild(dragImage)
    }, 0)
  }
}

// 处理放置
const handleDrop = (event: DragEvent, rowIndex: number) => {
  const data = event.dataTransfer?.getData('text/plain')
  if (!data) return

  const option = JSON.parse(data) as OptionItem
  const currentRow = tableList.value[rowIndex]

  // 如果该选项已经在其他位置，先移除
  tableList.value.forEach(item => {
    item.children = item.children.filter(name => name !== option.name)
  })

  // 添加到当前位置
  if (!currentRow.children.includes(option.name)) {
    currentRow.children.push(option.name)
  }

  // 移除高亮效果
  const cell = event.currentTarget as HTMLElement
  cell.classList.remove('highlight-drop')
}

const handleDragOver = (event: DragEvent) => {
  event.preventDefault()
  const target = event.currentTarget as HTMLElement
  target.classList.add('drag-over')
}


const optionsMap = computed(() => {
  const map = new Map<string, string>()
  options.value.forEach(item => {
    map.set(item.name, item.id)
  })
  return map
})
// 计算已使用的选项名称集合
const usedOptionIds = computed(() => {
  const names = new Set<string>()
  tableList.value.forEach(item => {
    item.children.forEach(name => {
      if (name) {
        const id = optionsMap.value.get(name)
        if (id) {
          names.add(id)
        }
      }
    })
  })
  return names
})

const handleOptionDragStart = (option: OptionItem, event: DragEvent) => {
  // handleDragStart(event, option.name)
  event.dataTransfer?.setData('text/plain', JSON.stringify(option))
}

const handleDragEnter = (event: DragEvent) => {
  event.preventDefault()
  const target = event.currentTarget as HTMLElement
  target.classList.add('drag-over')
}

const handleDragLeave = (event: DragEvent) => {
  event.preventDefault()
  const target = event.currentTarget as HTMLElement
  const rect = target.getBoundingClientRect()
  const x = event.clientX
  const y = event.clientY

  if (x <= rect.left || x >= rect.right || y <= rect.top || y >= rect.bottom) {
    target.classList.remove('drag-over')
  }
}

// 添加删除处理函数
const handleDelete = (rowIndex: number, index: number) => {
  const currentRow = tableList.value[rowIndex]
  if (currentRow) {
    currentRow.children.splice(index, 1)
  }
}

onMounted(() => {
  tableList.value = props.taskDetail.stuAnswer ? JSON.parse(props.taskDetail.stuAnswer) : []
  options.value = props.taskDetail.taskOptions ? JSON.parse(props.taskDetail.taskOptions) : []
})

const getStuAnswer = () => {
  return Promise.resolve(JSON.stringify(tableList.value))
}

defineExpose({
  getStuAnswer
})
</script>

<style scoped lang="scss">
.point-control {
  @include flex(row, flex-start, flex-start, nowrap);
  height: 100%;
  gap: 16px;

  .base-content {
    height: 100%;
    gap: 20px;
    @include flex(column, flex-start, flex-start, nowrap);

    .content-wrapper {
      width: 100%;
      flex: 1;
      min-height: 0;
      @include border-1px()
    }
  }

  .options-wrapper {
    width: 25%;
    height: 100%;
  }

  .content {
    min-width: 0;
    width: 75%;
  }

  .map-wrapper {
    padding: 20px;

    .map-name {
      height: 38px;
      box-shadow: 0 0 0 0.5px #fff;
      background: linear-gradient(180deg, #4BC3FF 0%, #254CCE 100%);
      text-align: center;
      line-height: 38px;
      font-weight: 700;
      color: #fff;
      user-select: none;
      font-size: var(--font-size-small);
    }

    .map-content {
      padding: 8px;
      border: 1px dashed #2BA0AD;
      margin: 14px 0;
      transition: all 0.2s ease;
      position: relative;
      z-index: 0;

      &.drag-over {
        border-color: #409EFF;
        background: rgba(64, 158, 255, 0.05);
        box-shadow: 0 0 0 1px #409EFF;
        z-index: 1;

        &::after {
          content: '';
          position: absolute;
          inset: -2px;
          border: 2px dashed #409EFF;
          pointer-events: none;
          animation: borderDash 20s linear infinite;
        }
      }
    }

    .map-desc {
      font-size: 14px;
      color: #fff;
      text-align: center;
      line-height: 38px;
    }
  }

  .dropped-items {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    overflow: hidden;

    .items-wrapper {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      overflow: hidden;
      width: 100%;
    }

    .option-item {
      width: calc((100% - 16px) / 3);
      height: 38px;
      padding: 0 15px;
      background: linear-gradient(180deg, #E7F7FF 0%, #6F92AA 100%);
      text-align: center;
      color: #001E22;
      line-height: 38px;
      font-size: var(--font-size-small);
      font-weight: 700;
      cursor: move;
      user-select: none;
      @include text-ellipsis();
      position: relative;

      &:hover {
        .delete-icon {
          opacity: 1;
        }
      }

      .delete-icon {
        position: absolute;
        top: 50%;
        right: 10px;
        width: 14px;
        height: 14px;
        border-radius: 50%;
        cursor: pointer;
        opacity: 0;
        transition: all 0.2s ease;
        color: #001E22;
        transform: translateY(-50%);
        @include flex(row, center, center, nowrap);

        &:hover {
          transform: translateY(-50%);
        }

        .el-icon {
          font-size: var(--font-size-small);
          font-weight: bold;
        }
      }
    }

  }

  @media screen and (max-width: 1440px) {
    .map-wrapper .map-desc {
      line-height: 32px;
    }

    .dropped-items .option-item {
      height: 32px;
      line-height: 32px;
    }
  }
}

@keyframes borderDash {
  to {
    background-position: 100% 100%;
  }
}
</style>
