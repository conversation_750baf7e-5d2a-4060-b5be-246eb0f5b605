<template>
  <el-dialog :model-value="visible" title="新增学生" width="650px" :before-close="handleClose" :show-close="false"
    draggable>
    <el-row :gutter="10">
      <el-col :span="13">
        <div class="stu-container">
          <div class="title-box">可选学生</div>
          <div class="main-box flex" v-loading="loading">
            <el-form :inline="true" :model="formInline" class="demo-form-inline">
              <el-form-item>
                <el-input v-model="formInline.className" placeholder="请输入班级名称" clearable />
                <el-button type="primary" @click="getTreeData">搜索</el-button>
              </el-form-item>
            </el-form>
            <div class="tree-box">
              <el-tree ref="treeRef" :data="classList" :props="defaultProps" node-key="id" show-checkbox
                check-on-click-node @check="handleClick" />
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="11">
        <div class="stu-container">
          <div class="title-box">已选学生</div>
          <ul class="list">
            <li v-for="item of selectStuList" :key="item.studentId">
              <div class="text-box">
                <i class="user"></i>
                <p class="name">{{ item.studentName }}</p>
              </div>
              <el-icon :size="18" @click="handleDelete(item)">
                <Delete />
              </el-icon>
            </li>
          </ul>
        </div>
      </el-col>
    </el-row>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          添加
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from 'vue';
import * as api from '@/api/teacher'
import { Delete } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { useRoute } from 'vue-router';

interface Student {
  classId: string
  studentId: string
  className: string
  studentName: string
  studentCode: string
}

interface ClassItem {
  classId: string
  className: string
  children: Student[]
}

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})
const loading = ref(false),
  treeRef = ref(),
  route = useRoute(),
  emit = defineEmits(['close', 'submit']),
  classList = ref<ClassItem[]>([]),//班级列表
  selectStuList = ref<Student[]>([])//已选择的学生


const defaultProps = {
  children: 'children',
  label: 'label',
}

const formInline = reactive({
  className: '',
})

const getTreeData = async () => {
  loading.value = true
  const params = {
    batchId: route.query.batchId,
    ...formInline
  }
  const { data: { code, msg, data } } = await api.getClassStudentList(params);
  loading.value = false
  if (code === 200) {
    const classMap = new Map();
    data.forEach((e: Student) => {
      if (!classMap.has(e.classId)) {
        classMap.set(e.classId, {
          id: e.classId,
          label: e.className,
          children: []
        })
      }
      classMap.get(e.classId).children.push({
        ...e,
        id: e.studentId,
        label: e.studentName
      })

    })
    classList.value = Array.from(classMap.values())
  } else {
    ElMessage.error(msg)
  }
}
const handleDelete = (item: Student) => {
  selectStuList.value = selectStuList.value.filter((e) => e.studentId != item.studentId)
  treeRef.value?.setCheckedNodes(selectStuList.value)
}
const handleClick = () => {
  const arr = treeRef.value?.getCheckedNodes()
  selectStuList.value = arr.filter((e: ClassItem) => e.children === undefined)
}
const handleClose = () => {
  emit('close')
}
const handleSubmit = () => {
  if (selectStuList.value.length === 0) {
    ElMessage.error('请选择添加的学生')
    return;
  }
  loading.value = true
  const arr = selectStuList.value.map((e) => {
    return {
      studentId: e.studentId,
      studentName: e.studentName,
      studentCode: e.studentCode,
      classId: e.classId,
      className: e.className
    }
  })
  const params = {
    trainId: route.query.trainId,
    studentIds: JSON.stringify(arr)
  }
  api.addTrainStudent(params).then(res => {
    if (res.data.code === 200) {
      emit('submit')
    } else {
      ElMessage.error(res.data.msg)
    }
  }).finally(() => {
    loading.value = false
  })
}
watch(() => props.visible, (nVal) => {
  if (nVal) {
    selectStuList.value = []
    formInline.className = ''
    getTreeData()
  }
})
</script>
<style scoped lang="scss">
$height: 450px;

.stu-container {
  border: 1px solid #ddd;
  height: $height;
  display: flex;
  flex-direction: column;

  .title-box {
    background-color: #ededed;
    height: 35px;
    line-height: 35px;
    text-indent: 10px;
    color: #333;
  }

  .list {
    padding: 10px 5px;
    overflow: auto;
    height: calc(100% - 38px);

    li {
      display: flex;
      justify-content: space-between;
      align-items: center;
      line-height: 40px;
      height: 40px;
      overflow: hidden;
      background-color: #f6fafc;
      padding: 0 10px;
      border: 1px solid transparent;
      border-radius: 4px;
      margin-bottom: 4px;
      user-select: none;

      .text-box {
        flex: 1;
        display: flex;
        align-items: center;
        overflow: hidden;

        .name {
          flex: 1;
          @include text-ellipsis();
        }
      }

      .user {
        display: inline-block;
        width: 24px;
        height: 24px;
        background: url('@/assets/images/icon/icon-user.png') no-repeat center;
        margin-right: 5px;
        vertical-align: middle;
      }

      &:hover {
        border-color: var(--el-color-primary-light-3);

        .el-icon {
          color: var(--el-color-primary-light-3);
          cursor: pointer;
        }
      }
    }
  }

  .main-box {
    padding: 10px;
    flex: 1;
    overflow: hidden;


    &.flex {
      display: flex;
      flex-direction: column;
    }

    .tree-box {
      flex: 1;
      overflow: auto;
    }

    .el-form {
      margin-bottom: 10px;
    }

    &.border {
      border-left: 1px solid #ddd;
      height: calc($height - 36px);
    }

  }

}



.demo-form-inline {
  :deep(.el-form-item) {
    margin: 0;
    width: 100%;
  }

  .el-form-item__content {
    .el-input {
      flex: 1;
    }
  }
}
</style>
