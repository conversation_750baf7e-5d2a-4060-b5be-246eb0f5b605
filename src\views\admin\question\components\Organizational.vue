<template>
  <div class="organizational">
    <OptionList v-model:options="options" :used-option-ids="usedOptionIds" @drag-start="handleOptionDragStart"
      @toggle-collapse="handleResize" />
    <div class="organizational-right">
      <div class="right-header">
        <span>组织架构</span>
        <span class="tip-text">请先设置选项后再绘制组织架构。</span>
      </div>
      <div class="graph-wrapper" ref="graphWrapperRef">
        <div class="graph" ref="graphRef"></div>
      </div>
    </div>
    <!-- 添加编辑弹窗 -->
    <el-dialog v-model="editDialogVisible" title="编辑节点" width="550px" :close-on-click-modal="false"
      @close="handleEditDialogClose">
      <el-form :model="editForm" label-width="80px">
        <el-form-item label="是否预设">
          <el-switch v-model="editForm.isPreSet" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleEditConfirm">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import OptionList from './OptionList.vue'
import { Graph } from '@antv/x6'
import { Dnd } from '@antv/x6-plugin-dnd'
import type { OptionItem } from '../../types'
import cellHover from "@/components/flow/composables/cellHover";
import cellSelect from "@/components/flow/composables/cellSelect";
import { registerNode, unregisterNode } from "@/components/flow/shapeConfig/registerNode";
import { registerNodeTools, unregisterNodeTools } from "@/components/flow/tools/registerNodeTools";
import { adminDefaultConfig } from '@/components/flow/shapeConfig/staticConfig'
import type { Cell, Node } from '@antv/x6'


const props = defineProps({
  initContent: {
    type: String,
    default: ''
  },
  taskOptions: {
    type: String,
    default: ''
  }
})


const options = ref<OptionItem[]>([])


// 计算已使用的选项名称集合
const usedOptionIds = ref<Set<string>>(new Set())
//当前拖拽的选项
const currentOption = ref<OptionItem>()

const graphWrapperRef = ref<HTMLDivElement | null>()
const graphRef = ref<HTMLDivElement | null>()
const graph = ref<Graph>()
const dnd = ref<Dnd>();

// 编辑弹窗相关
const editDialogVisible = ref(false)
const editForm = ref({
  isPreSet: false
})
const currentEditNode = ref<Node | null>(null)

const injectData = () => {
  if (props.initContent) {
    const data = JSON.parse(props.initContent)
    if (graph.value) {
      graph.value.fromJSON(data)
      data.forEach((node: Cell) => {
        if (node.data?.id) {
          usedOptionIds.value.add(node.data.id)
        }
        setTimeout(() => {
          const ports = document.querySelectorAll(
            `g[data-cell-id="${node.id}"] .x6-port`
          );
          ports.forEach((port) => {
            (port as SVGElement).style.visibility = "hidden";
          });
        }, 100)
      })
      graph.value.centerContent()
    }
  }
}

const initGraph = async () => {
  await nextTick();
  if (!graphRef.value) return
  graph.value = new Graph({
    container: graphRef.value,
    ...adminDefaultConfig,
  });
  dnd.value = new Dnd({
    target: graph.value,
    validateNode() {
      return true
    },
  });

  injectData()
  cellHover(graph.value)
  cellSelect(graph.value)
  graph.value.on('node:added', () => {
    if (currentOption.value) {
      usedOptionIds.value.add(currentOption.value!.id)
    }
  })
  graph.value.on('node:removed', ({ node }) => {
    usedOptionIds.value.delete(node.data.id)
  })
  graph.value.on('node:dblclick', ({ node }) => {
    console.log(node)
    currentEditNode.value = node
    editForm.value.isPreSet = node.data?.isPreSet || false
    editDialogVisible.value = true
  })
}


const handleOptionDragStart = (event: DragEvent, item: OptionItem) => {
  const node = graph.value!.createNode({
    id: item.id, shape: 'general-node', ports: {
      items: [{
        group: 'port_g',
        id: 'p_top'
      },
      {
        group: 'port_g',
        id: 'p_right'
      }],
      groups: {
        port_g: {
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              strokeWidth: 1,
              fill: '#fff'
            }
          },
          position: 'ellipseSpread'
        }
      }
    },
    tools: [{
      name: 'cus-button-remove', // 工具名称
      args: {
        x: 130,
        y: 4,
        offset: {
          x: -5,
          y: -5
        }
      }
    }],
  });
  node.attrs!.label.text = item.name;
  node.attrs!.title.text = item.name;
  node.setData({
    name: item.name,
    id: item.id,
    isPreSet: false
  })
  currentOption.value = item
  dnd.value?.start(node, event);
}


// 处理编辑确认
const handleEditConfirm = () => {
  if (!currentEditNode.value) return
  currentEditNode.value.setData({
    ...currentEditNode.value.data,
    isPreSet: editForm.value.isPreSet
  })

  editDialogVisible.value = false
}

// 处理弹窗关闭
const handleEditDialogClose = () => {
  editForm.value.isPreSet = false
  currentEditNode.value = null
}

const handleResize = () => {
  setTimeout(() => {
    if (graph.value) {
      const { width, height } = graphWrapperRef.value!.getBoundingClientRect()
      graph.value.resize(width, height)
      graph.value.centerContent()
    }
  }, 350)
}

const validate = () => {
  return new Promise((resolve, reject) => {
    const data = graph.value?.toJSON();
    if (!data?.cells?.length) {
      ElMessage.error('请至少添加一个组织机构')
      reject()
    }
    resolve({
      initContent: JSON.stringify(data?.cells),
      taskOptions: JSON.stringify(options.value)
    })
  })
}

onMounted(() => {
  registerNode(Graph);
  registerNodeTools(Graph);
  initGraph();
  if (props.taskOptions) {
    options.value = JSON.parse(props.taskOptions)
  }
})

onBeforeUnmount(() => {
  unregisterNode(Graph)
  unregisterNodeTools(Graph)
})

defineExpose({
  validate
})
</script>

<style scoped lang="scss">
.organizational {
  @include flex(row, flex-start, flex-start, nowrap);
  gap: 20px;
  position: relative;
  min-height: 600px;


  .organizational-right {
    height: 600px;
    flex: 1;
    min-width: 0;
    @include flex(column, flex-start, stretch, nowrap);
    gap: 16px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 14px;
    border-radius: 6px;

    .right-header {
      @include flex(row, flex-start, flex-start, nowrap);
      gap: 14px;

      span {
        font-weight: bold;
        color: var(--el-text-color-primary);
      }

      .tip-text {
        color: var(--el-text-color-secondary);
        font-size: 14px;
        font-weight: normal;
      }
    }
  }

  .graph-wrapper {
    min-height: 0;
    flex: 1;

    .graph {
      height: 100%;
    }
  }
}
</style>
