<template>
  <div class="question-content" v-loading="loading">
    <div class="content-header">
      <div class="header-left">
        <span class="title">内容管理</span>
        <span class="question-type">({{ questionTypeText }})</span>
      </div>
      <div class="header-right">
        <el-button type="primary" plain @click="handleClose">
          返回
        </el-button>
        <el-button type="primary" :loading="loading" @click="handleSave">保存</el-button>
      </div>
    </div>
    <div class="content-body">
      <div class="body-content">
        <el-form :model="form" ref="formRef" label-width="100px" :rules="rules" label-position="top">
          <el-form-item label="任务说明" prop="taskDesc">
            <div class="editor-wrapper">
              <CuCkeditor v-model="form.taskDesc" />
            </div>
          </el-form-item>
          <el-form-item label="任务分值" prop="questionScore" required>
            <el-input-number v-model="form.questionScore" :min="0" :max="100" :precision="2" :step="1" :controls="false"
              placeholder="请输入任务分值" class="w-200" />
            <span class="score-tip">分</span>
          </el-form-item>
          <el-form-item v-if="[7, 8].includes(+questionType!)" label="时间限制" prop="timeLimit" required>
            <el-switch v-model="form.timeLimit" :active-value="1" :inactive-value="0"
              @change="handleTimeLimitChange"></el-switch>
          </el-form-item>
          <el-form-item v-if="form.timeLimit" label="做题时间" prop="answerTime" required>
            <el-input v-model="form.answerTime" @input="handleAnswerTimeInput" placeholder="请输入做题时间" class="w-200">
              <template #append>
                秒
              </template>
            </el-input>
          </el-form-item>
          <el-form-item label="任务设置" required>
            <div class="set-wrapper flex-1">
              <component v-if="loadComplete" ref="setComponentRef" :is="setComponent" :enterpriseId="enterpriseId"
                :init-content="form.initContent" :task-options="form.taskOptions" />
            </div>
          </el-form-item>
          <el-form-item label="任务附件" prop="stuffFile">
            <div class="upload-wrapper">
              <div class="upload-tip">
                请添加背景资料，支持文件类型为xls\doc\pdf\mp4\mp3\jpg\png\zip\rar，单文件大小不超过500M
              </div>
              <el-upload class="upload-files" :auto-upload="false" :show-file-list="false" :on-change="handleFileChange"
                multiple>
                <el-button type="primary">
                  <el-icon>
                    <Upload />
                  </el-icon>
                  选择文件
                </el-button>
              </el-upload>
              <!-- 自定义文件列表 -->
              <div class="file-list" v-if="fileList.length">
                <div v-for="(file, index) in fileList" :key="index" class="file-item">
                  <div class="file-info">
                    <el-icon>
                      <Document />
                    </el-icon>
                    <span class="file-name" :title="file.name">{{ file.name }}</span>
                  </div>
                  <el-icon class="delete-icon" @click="handleFileRemove(index)">
                    <Close />
                  </el-icon>
                </div>
              </div>
            </div>
          </el-form-item>
        </el-form>
        <div class="form-footer">

        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed, defineAsyncComponent } from 'vue'
import { Close, Upload, Document } from '@element-plus/icons-vue'
import { useRouter, useRoute } from 'vue-router'
import { QUESTION_TYPE } from '@/utils/constant'
import { decodeName } from '@/utils/tools'
import { ElMessage } from 'element-plus'
import type { UploadFile } from 'element-plus'
import CuCkeditor from "@/components/ckeditor/index.vue"
import { getQuestionContent, saveQuestionContent } from '@/api/admin'

const RiskCoordinate = defineAsyncComponent(() => import('./components/RiskCoordinate.vue'))
const SystemSetting = defineAsyncComponent(() => import('./components/SystemSetting.vue'))
const PointControl = defineAsyncComponent(() => import('./components/PointControl.vue'))
const StrategyMap = defineAsyncComponent(() => import('./components/StrategyMap.vue'))
const StrategyMapFlow = defineAsyncComponent(() => import('./components/StrategyMapFlow.vue'))
const BCGMatrix = defineAsyncComponent(() => import('./components/StrategyAnalysis/BCGMatrix.vue'))
const TimeAxis = defineAsyncComponent(() => import('./components/StrategyAnalysis/TimeAxis.vue'))
const RadarChart = defineAsyncComponent(() => import('./components/StrategyAnalysis/RadarChart.vue'))
const MindMap = defineAsyncComponent(() => import('./components/StrategyAnalysis/MindMap.vue'))
const Organizational = defineAsyncComponent(() => import('./components/Organizational.vue'))
const BusinessFlow = defineAsyncComponent(() => import('./components/BusinessFlow.vue'))
const FlowDesign = defineAsyncComponent(() => import('./components/FlowDesign.vue'))

const router = useRouter()
const route = useRoute()
const formRef = ref()
const setComponentRef = ref()
const { questionType, questionId, questionName, questionSecondType } = route.query

// 添加接口定义
interface FileItem {
  name: string
  raw?: File
  url?: string
  id?: string
  isNew: boolean
}

interface FormData {
  taskDesc: string
  stuffFile: File[]
  delFileIds: string[]
  taskOptions: string  // 支持数组或字符串类型
  initContent: string
  questionScore: number
  timeLimit: number
  answerTime: string
}

// 表单数据
const form = reactive<FormData>({
  taskDesc: '',
  stuffFile: [],
  delFileIds: [],
  initContent: '',
  taskOptions: '',
  questionScore: 0,
  timeLimit: 0,
  answerTime: '',
})
const loadComplete = ref(false)
const enterpriseId = ref(route.query.enterpriseId as string)
const rules: any = {
  taskDesc: [
    { required: false, message: '请输入任务说明', trigger: 'blur' },
  ],
  questionScore: [
    { required: true, message: '请输入任务分值', trigger: 'change' },
    { type: 'number', min: 1, message: '分值必须大于0', trigger: 'change' }
  ]
}

const setComponent = computed(() => {
  if (questionSecondType && questionSecondType !== '0') {
    return ['', '', '', '', '', '', '', '', ['', BCGMatrix, TimeAxis, RadarChart, MindMap]][+questionType!]?.[+questionSecondType]
  }
  return [Organizational, StrategyMap, BusinessFlow, PointControl, SystemSetting, RiskCoordinate, FlowDesign, StrategyMapFlow][+questionType!] || CuCkeditor
})

// 修改文件列表类型
const fileList = ref<FileItem[]>([])
const loading = ref(false)

// 获取题目内容
const getContent = async () => {
  try {
    loading.value = true
    const { data: { code, data } } = await getQuestionContent({ questionId })
    loadComplete.value = true
    if (code === 200 && data) {
      form.taskDesc = data.taskDesc || ''
      form.taskOptions = data.taskOptions || ''
      form.questionScore = +data.questionScore || 0
      form.initContent = data.initContent || ''
      form.timeLimit = data.timeLimit || 0
      form.answerTime = data.answerTime || ''
      // 转换已有文件列表格式
      fileList.value = (data.ninBcQuestionFileList || []).map((file: any) => ({
        name: file.fileName,
        url: file.filePath,
        id: file.questionFileId,
        isNew: false
      }))
    }
  } catch (error) {
    console.error('获取题目内容失败:', error)
  } finally {
    loading.value = false
  }
}

const handleTimeLimitChange = () => {
  form.answerTime = ''
}

const handleAnswerTimeInput = (value: string) => {
  form.answerTime = value.replace(/\D/g, '')
}

// 修改保存函数
const handleSave = async () => {
  if (!formRef.value) return
  try {
    // 表单验证
    await formRef.value.validate()
    const setData = await setComponentRef.value?.validate()
    if (!setData) return
    loading.value = true

    const params: Record<string, any> = {
      ...form,
      ...setData,
      questionId,
      questionType: Number(questionType),
    }

    // 只提交新文件
    params.stuffFile = fileList.value
      .filter((item): item is FileItem & { raw: File } => item.isNew && !!item.raw)
      .map(item => item.raw)

    const { data: { code, msg } } = await saveQuestionContent(params)

    if (code === 200) {
      ElMessage.success('保存成功')
    } else {
      ElMessage.error(msg || '保存失败')
    }
  } catch (error) {
    if (error instanceof Error) {
      console.error('保存失败:', error.message)
    }
  } finally {
    loading.value = false
  }
}

// 修改文件处理函数的类型
const handleFileChange = (uploadFile: UploadFile) => {
  if (uploadFile.raw) {
    const isValid = beforeUpload(uploadFile.raw)
    if (isValid) {
      fileList.value.push({
        name: uploadFile.name,
        raw: uploadFile.raw,
        isNew: true
      })
    }
  }
}

const handleFileRemove = (index: number) => {
  const file = fileList.value[index]
  fileList.value.splice(index, 1)
  if (!file.isNew && file.id) {
    form.delFileIds.push(file.id)
  }
}

const beforeUpload = (file: File): boolean => {
  // 检查文件类型
  const validTypes = ['xls', 'xlsx', 'doc', 'docx', 'pdf', 'mp4', 'mp3', 'jpg', 'png', 'zip', 'rar']
  const extension = file.name.split('.').pop()?.toLowerCase()
  const isValidType = extension && validTypes.includes(extension)

  if (!isValidType) {
    ElMessage.error('不支持的文件类型')
    return false
  }

  // 检查文件大小
  const maxSize = 500 * 1024 * 1024 // 500M
  if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过500M')
    return false
  }

  return true
}

// 计算题型文字
const questionTypeText = computed(() => {
  const type = QUESTION_TYPE.find(item => item.value === Number(questionType))
  return decodeName(questionName as string) + '-' + (type ? type.label : '') + '题型'
})

onMounted(() => {
  if (questionId) {
    getContent()
  }
})

// 关闭按钮处理
const handleClose = () => {
  router.back()
}
</script>

<style scoped lang="scss">
.question-content {
  height: 100%;
  @include flex(column, flex-start, flex-start, nowrap);

  .content-header {
    width: 100%;
    height: 60px;
    padding: 0 20px;
    border-bottom: 1px solid var(--el-border-color);
    @include flex(row, space-between, center, nowrap);
    background-color: #fff;

    .header-left {
      @include flex(row, flex-start, center, nowrap);
      gap: 8px;

      .title {
        font-size: 16px;
        font-weight: bold;
        color: var(--el-text-color-primary);
      }

      .question-type {
        color: var(--el-text-color-secondary);
      }
    }

    .header-right {
      .close-icon {
        font-size: 20px;
      }
    }
  }

  .content-body {
    flex: 1;
    width: 100%;
    min-height: 0;
    padding: 20px;
    overflow-y: auto;
    overflow-x: hidden;
  }

  .body-content {
    background-color: #fff;
    min-height: 100%;
    padding: 20px;

    .editor-wrapper {
      width: 100%;
    }

    .set-wrapper {
      width: 100%;
      max-height: 85vh;
      overflow: hidden;
    }

    .upload-wrapper {
      .upload-tip {
        color: var(--el-text-color-secondary);
        font-size: 14px;
        margin-bottom: 12px;
      }

      .upload-files {
        :deep(.el-upload-list) {
          margin-top: 12px;
        }
      }

      .file-list {
        margin-top: 16px;
        border: 1px solid var(--el-border-color);
        border-radius: 4px;

        .file-item {
          padding: 4px 8px;
          @include flex(row, space-between, center, nowrap);

          .file-info {
            @include flex(row, flex-start, center, nowrap);
            gap: 8px;
            flex: 1;
            min-width: 0;

            .el-icon {
              color: var(--el-text-color-secondary);
            }

            .file-name {
              flex: 1;
              @include text-ellipsis();
            }
          }

          .delete-icon {
            cursor: pointer;
            color: var(--el-text-color-secondary);
            font-size: 16px;

            &:hover {
              color: var(--el-color-danger);
            }
          }
        }
      }
    }

    .form-footer {
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid var(--el-border-color);
      text-align: center;
    }

    .w-200 {
      width: 200px;
    }

    .score-tip {
      margin-left: 8px;
      color: var(--el-text-color-regular);
    }
  }
}
</style>
