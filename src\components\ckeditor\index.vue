<template>
  <ckeditor :editor="editor" v-model="content" :config="editorConfig" :disabled="editorDisabled" @ready="onReady"
    class="editctor_container">
  </ckeditor>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from "vue";
import DecoupledEditor from "@ckeditor/ckeditor5-build-decoupled-document";
import CustomUploadAdapter from "./plugins/CustomUpload";
import "./zh-cn";

const props = defineProps({
  value: {
    type: String,
    default: "",
  },
  placeholder: {
    type: String,
    default: "请输入内容~",
  },
  //是否禁用
  editorDisabled: {
    type: Boolean,
    default: () => false,
  },
  platform: {
    type: String,
    default: "common",
  },
  //默认工具栏
  toolbar: {
    type: Array,
    default: () => {
      return [
        "heading",
        "undo",
        "redo",
        "|",
        "fontColor",
        "fontBackgroundColor",
        "|",

        "alignment",
        "bold",
        "italic",
        "Underline",

        "|",
        "imageUpload",
        "insertTable",
        // "MediaEmbed"
      ];
    },
  },
});

let editor = DecoupledEditor;
const content = ref("");

const editorConfig = {
  language: "zh-cn",
  placeholder: props.placeholder,
  link: {
    decorators: {
      addTargetToExternalLinks: {
        mode: "automatic",
        callback: () => true,
        attributes: {
          target: "_blank",
        },
      },
    },
  },
  image: {
    toolbar: [
      "imageTextAlternative",
      "|",
      "imageStyle:alignLeft",
      "imageStyle:alignRight",
      "imageStyle:inline",
    ],
  },
  toolbar: props.toolbar,
};

const onReady = (editor: any) => {
  editor.plugins.get("FileRepository").createUploadAdapter = (loader: any) => {
    return new CustomUploadAdapter(loader);
  };
  editor.ui
    .getEditableElement()
    .parentElement.insertBefore(
      editor.ui.view.toolbar.element,
      editor.ui.getEditableElement()
    );
};

onMounted(() => {
  content.value = props.value;
});
onUnmounted(() => {
  editor = null;
});
</script>

<style lang="scss" scoped>
//ckeditor
.ck-editor__editable_inline {
  background: #fff;
  height: 260px !important;
  border: 1px solid #c4c4c4 !important;
  border-top-width: 0 !important;
}
</style>
