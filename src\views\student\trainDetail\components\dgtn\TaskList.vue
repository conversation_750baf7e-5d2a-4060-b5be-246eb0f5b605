<template>
  <div class="task-list">
    <div class="section-title-light">
      <div class="title-label">任务列表</div>
    </div>
    <div class="list-content">
      <el-scrollbar height="100%">
        <div class="list-inner">
          <el-collapse>
            <el-collapse-item :class="['icon-' + item.categoryIcon]" v-for="item in taskList" :title="item.categoryName"
              :name="item.categoryName" :icon="ArrowRightBold" :key="item.categoryName">
              <template #title>
                <div class="collapse-title" :title="item.categoryName">
                  {{ item.categoryName }}
                </div>
              </template>
              <div class="task-item"
                :class="{ active: currentTask?.questionId === task.questionId, checked: task.ifFinish }"
                v-for="task in item.questionList" :key="task.questionId" :title="task.questionName"
                @click="handleClick(task)">
                <span class="task-name">{{ task.questionName }}</span>
              </div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowRightBold } from '@element-plus/icons-vue'
import type { TaskItem, TaskListItem } from '../../types'

interface Props {
  taskList: TaskListItem[]
  currentTask: TaskItem | null
}

withDefaults(defineProps<Props>(), {
  taskList: () => []
})

const emit = defineEmits<{
  'update:currentTask': [task: TaskItem]
  'change': [task: TaskItem]
}>()


const handleClick = (task: TaskItem) => {
  emit('update:currentTask', task)
  emit('change', task)
}
</script>

<style scoped lang="scss">
.task-list {
  width: 192px;
  height: 100%;
  @include flex(column, flex-start, flex-start, nowrap);
  background-image: linear-gradient(to bottom, #fff 0%, rgba(255, 255, 255, 0.5) 100%);
  box-shadow: 0px 2px 4px 0px rgba(120, 147, 187, 0.15);
  border-radius: 6px;
  border: solid 1px #ffffff;
  overflow: hidden;
  color: #313052;

  .list-content {
    width: 100%;
    flex: 1;
    min-height: 0;

    .list-inner {
      padding: 8px 16px;
    }

    .task-item {
      width: 100%;
      height: 40px;
      color: #313052;
      padding-left: 50px;
      @include flex(row, flex-start, center, nowrap);
      position: relative;
      cursor: pointer;
      padding-right: 2px;

      &::before {
        content: '';
        position: absolute;
        left: 24px;
        top: 50%;
        width: 18px;
        height: 14px;
        background: url('@/assets/images/icon/icon-task.png') no-repeat;
        background-size: 100% 100%;
        transform: translateY(-50%);
      }

      .task-name {
        flex: 1;
        min-width: 0;
        @include text-ellipsis();
      }

      &:hover {
        font-weight: bold;
      }

      &.active {
        border-radius: 4px;
        background: linear-gradient(to right,
            #ffbbf4 0%,
            #f8e9fc 100%);
        font-weight: bold;

      }

      &.checked::before {
        content: '';
        position: absolute;
        left: 20px;
        top: 50%;
        width: 20px;
        height: 20px;
        background: url('@/assets/svg/complete.svg') no-repeat;
        background-size: 100% 100%;
        transform: translateY(-50%);
      }
    }

    .collapse-title {
      @include text-ellipsis();
    }

    :deep(.el-collapse) {
      border: 0;

      .el-collapse-item {
        &.is-active {
          .el-collapse-item__header::after {
            transform: scale(1);
          }

          .el-collapse-item__arrow {
            transform: rotate(-90deg);
          }
        }

        @for $i from 1 through 12 {
          &.icon-#{$i} {
            .el-collapse-item__header {
              background: left center url('@/assets/images/taskIcon/icon-task-#{$i}.png') no-repeat;
              background-size: 21px 20px;
            }

          }
        }
      }

      .el-collapse-item__header {
        height: 52px;
        background: transparent;
        color: #313052;
        border: 0;
        font-size: 16px;
        font-style: normal;
        padding-left: 32px;
        position: relative;

        .el-collapse-item__arrow {
          transform: rotate(90deg);
          font-size: 12px;
          color: #4b5ee7;
        }
      }

      .el-collapse-item__wrap {
        background: transparent;
        border: 0;
        color: #fff;
      }

      .el-collapse-item__content {
        padding-bottom: 0;
      }

    }


  }


}
</style>
