<template>
  <div class="key-point-control">
    <OptionList v-model:options="options" :used-option-ids="usedOptionIds" />
    <div class="control-right">
      <div class="right-header">
        <span>产品生命周期分析</span>
        <span class="tip-text">请先设置选项后再拖拽到产品生命周期中</span>
      </div>
      <div class="lifecycle-wrapper">
        <div class="lifecycle-title">
          <h3>产品生命周期分析 (Product Life Cycle)</h3>
          <!-- <el-button class="add-stage-btn" @click="addStage" type="primary" size="small" :icon="Plus"
            style="margin-left: 16px;align-self: flex-start;">新增阶段</el-button> -->
        </div>
        <div class="timeline-container">
          <div class="lifecycle-stages">
            <div class="stage-container" v-for="(stage, stageIdx) in lifecycleData" :key="stageIdx">
              <div class="stage-point"></div>
              <div class="stage-content">
                <div class="drop-section lifecycle-section">
                  <div class="section-header">
                    <el-input v-model.trim="stage.lifecycleHint" placeholder="输入生命周期提示" size="small" :maxlength="15"
                      show-word-limit>
                      <template #prefix>
                        <el-icon>
                          <InfoFilled />
                        </el-icon>
                      </template>
                    </el-input>
                  </div>
                  <div class="drop-area" @drop="handleLifecycleDrop($event, stageIdx)" @dragover.prevent
                    @dragenter="handleDragEnter($event)" @dragleave="handleDragLeave($event)">
                    <el-scrollbar>
                      <div v-if="stage.lifecycle.length === 0" class="content-placeholder">
                        {{ stage.lifecycleHint || '拖拽选项到此区域' }}
                      </div>
                      <div v-else class="content-items">
                        <div v-for="(item, itemIndex) in stage.lifecycle" :key="itemIndex"
                          class="content-item lifecycle-item" draggable="true"
                          @dragstart="handleItemDragStart($event, item)" :title="item">
                          {{ item }}
                          <div class="delete-icon" @click.stop="handleDeleteItem(stageIdx, 'lifecycle', itemIndex)">
                            <el-icon>
                              <Close />
                            </el-icon>
                          </div>
                        </div>
                      </div>
                    </el-scrollbar>
                  </div>
                </div>
                <div class="stage-header">
                  <span class="stage-period">
                    <el-input v-model.trim="stage.period" size="small" style="width: 110px;" placeholder="时间区间" />
                  </span>
                  <!-- <el-button v-if="lifecycleData.length > 1" @click.stop="deleteStage(stageIdx)" type="danger"
                    size="small" circle :icon="Delete" style="margin-left: 8px;" title="删除阶段" /> -->
                </div>

                <div class="drop-section strategy-section">
                  <div class="section-header">
                    <el-input v-model.trim="stage.strategyHint" placeholder="输入战略特点提示" size="small" :maxlength="15"
                      show-word-limit>
                      <template #prefix>
                        <el-icon>
                          <InfoFilled />
                        </el-icon>
                      </template>
                    </el-input>
                  </div>
                  <div class="drop-area" @drop="handleStrategyDrop($event, stageIdx)" @dragover.prevent
                    @dragenter="handleDragEnter($event)" @dragleave="handleDragLeave($event)">
                    <el-scrollbar>
                      <div v-if="stage.strategy.length === 0" class="content-placeholder">
                        {{ stage.strategyHint || '拖拽选项到此区域' }}
                      </div>
                      <div v-else class="content-items">
                        <div v-for="(item, itemIndex) in stage.strategy" :key="itemIndex"
                          class="content-item strategy-item" draggable="true"
                          @dragstart="handleItemDragStart($event, item)" :title="item">
                          {{ item }}
                          <div class="delete-icon" @click.stop="handleDeleteItem(stageIdx, 'strategy', itemIndex)">
                            <el-icon>
                              <Close />
                            </el-icon>
                          </div>
                        </div>
                      </div>
                    </el-scrollbar>
                  </div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Close, Delete, Plus, InfoFilled } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import OptionList from '../OptionList.vue'
import type { OptionItem } from '../../../types'

const props = defineProps({
  initContent: {
    type: String,
    default: ''
  },
  taskOptions: {
    type: String,
    default: ''
  }
})

interface StageData {
  period: string
  lifecycle: string[]  // 产品生命周期（多个）
  strategy: string[]   // 战略特点（多个）
  lifecycleHint: string     // 生命周期提示
  strategyHint: string      // 战略特点提示
}

const lifecycleData = ref<StageData[]>([])

// 默认阶段模板
const defaultStages: StageData = {
  period: '',
  lifecycle: [],
  strategy: [],
  lifecycleHint: '请将产品生命周期拖动到此区域',
  strategyHint: '请将战略特点拖动到此区域'
}


const initDefaultStages = () => {
  return Array(4).fill(0).map(() => ({ ...JSON.parse(JSON.stringify(defaultStages)) }))
}

const options = ref<OptionItem[]>([])

const optionsMap = computed(() => {
  const map = new Map<string, string>()
  options.value.forEach(item => {
    map.set(item.name, item.id)
  })
  return map
})

// 计算已使用的选项名称集合
const usedOptionIds = computed(() => {
  const ids = new Set<string>()
  lifecycleData.value.forEach(stage => {
    stage.lifecycle.forEach(item => {
      const id = optionsMap.value.get(item)
      if (id) ids.add(id)
    })
    stage.strategy.forEach(item => {
      const id = optionsMap.value.get(item)
      if (id) ids.add(id)
    })
  })
  return ids
})

// 处理拖拽进入
const handleDragEnter = (event: DragEvent) => {
  const element = event.currentTarget as HTMLElement
  element.classList.add('highlight-drop')
}

// 处理拖拽离开
const handleDragLeave = (event: DragEvent) => {
  const element = event.currentTarget as HTMLElement
  element.classList.remove('highlight-drop')
}


// 处理生命周期拖拽放置
const handleLifecycleDrop = (event: DragEvent, stageIdx: number) => {
  const data = event.dataTransfer?.getData('text/plain')
  if (!data) return
  const option = JSON.parse(data) as OptionItem
  removeOptionFromOtherPlaces(option.name)
  lifecycleData.value[stageIdx].lifecycle.push(option.name)
  const element = event.currentTarget as HTMLElement
  element.classList.remove('highlight-drop')
}

// 处理战略特点拖拽放置
const handleStrategyDrop = (event: DragEvent, stageIdx: number) => {
  const data = event.dataTransfer?.getData('text/plain')
  if (!data) return
  const option = JSON.parse(data) as OptionItem
  removeOptionFromOtherPlaces(option.name)
  lifecycleData.value[stageIdx].strategy.push(option.name)
  const element = event.currentTarget as HTMLElement
  element.classList.remove('highlight-drop')
}

// 移除选项从其他位置
const removeOptionFromOtherPlaces = (optionName: string) => {
  lifecycleData.value.forEach(stage => {
    const lifecycleIndex = stage.lifecycle.indexOf(optionName)
    if (lifecycleIndex !== -1) {
      stage.lifecycle.splice(lifecycleIndex, 1)
    }

    const strategyIndex = stage.strategy.indexOf(optionName)
    if (strategyIndex !== -1) {
      stage.strategy.splice(strategyIndex, 1)
    }
  })
}

// 拖拽开始处理
const handleItemDragStart = (event: DragEvent, itemName: string) => {
  const option = options.value.find(opt => opt.name === itemName)
  if (option) {
    event.dataTransfer?.setData('text/plain', JSON.stringify(option))
    createDragImage(event, itemName)
  }
}

// 创建拖拽图像
const createDragImage = (event: DragEvent, text: string) => {
  const dragImage = document.createElement('div')
  dragImage.textContent = text
  dragImage.style.cssText = `
    position: absolute;
    left: -9999px;
    background: rgb(101, 151, 247);
    color: #fff;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: var(--font-size-small);
    white-space: nowrap;
    pointer-events: none;
    z-index: 9999;
    width: 200px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
  `
  document.body.appendChild(dragImage)
  event.dataTransfer!.setDragImage(dragImage, 0, 0)

  setTimeout(() => {
    document.body.removeChild(dragImage)
  }, 0)
}

// 删除项目
const handleDeleteItem = (stageIdx: number, type: 'lifecycle' | 'strategy', itemIndex: number) => {
  lifecycleData.value[stageIdx][type].splice(itemIndex, 1)
}

// 新增阶段
const addStage = () => {
  lifecycleData.value.push({ ...JSON.parse(JSON.stringify(defaultStages)) })
}

// 删除阶段
const deleteStage = (stageIdx: number) => {
  lifecycleData.value.splice(stageIdx, 1)
}

// 验证函数
const validate = () => {
  return new Promise((resolve, reject) => {
    if (options.value.length === 0) {
      ElMessage.error('请添加选项')
      reject(new Error('请添加选项'))
      return
    }

    const hasEmpty = lifecycleData.value.some(stage =>
      stage.lifecycle.length === 0 || stage.strategy.length === 0 || !stage.period
    )
    if (hasEmpty) {
      ElMessage.error('请完善数据内容')
      reject(new Error('请完善数据内容'))
      return
    }

    resolve({
      initContent: JSON.stringify(lifecycleData.value),
      taskOptions: JSON.stringify(options.value)
    })
  })
}

onMounted(() => {
  if (props.initContent) {
    try {
      const data = JSON.parse(props.initContent)

      if (Array.isArray(data)) {
        lifecycleData.value = data
      } else {
        lifecycleData.value = initDefaultStages()
      }
    } catch (e) {
      console.error('解析初始数据失败', e)
      lifecycleData.value = initDefaultStages()
    }
  } else {
    lifecycleData.value = initDefaultStages()
  }
  options.value = props.taskOptions ? JSON.parse(props.taskOptions) : []
})

defineExpose({
  validate
})
</script>

<style scoped lang="scss">
.key-point-control {
  @include flex(row, flex-start, flex-start, nowrap);
  gap: 20px;
  position: relative;
  width: 100%;

  .control-right {
    flex: 1;
    min-width: 0;
    @include flex(column, flex-start, stretch, nowrap);
    gap: 16px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 14px;
    border-radius: 6px;
    max-height: 80vh;
    overflow: hidden;

    .right-header {
      @include flex(row, flex-start, flex-start, nowrap);
      gap: 14px;

      span {
        font-weight: bold;
        color: var(--el-text-color-primary);
      }

      .tip-text {
        color: var(--el-text-color-secondary);
        font-size: 14px;
        font-weight: normal;
      }
    }
  }

  .lifecycle-wrapper {
    @include flex(column, flex-start, stretch, nowrap);
    gap: 30px;

    .lifecycle-title {
      text-align: center;

      h3 {
        margin: 0;
        color: var(--el-text-color-primary);
        font-size: 20px;
        font-weight: bold;
      }
    }

    .timeline-container {
      position: relative;
      padding: 20px;
      overflow-y: auto;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 0;
        right: 0;
        height: 3px;
        background: #409EFF;
        border-radius: 2px;
        transform: translateY(-50%);
        z-index: 1;
      }


      .lifecycle-stages {
        @include flex(row, flex-start, flex-start, nowrap);
        position: relative;
        z-index: 2;
        gap: 20px;

        .stage-container {
          @include flex(column, flex-start, center, nowrap);
          flex-shrink: 0;
          position: relative;

          .stage-point {
            width: 16px;
            height: 16px;
            background: #ffffff;
            border: 3px solid #409EFF;
            border-radius: 50%;
            margin-bottom: 20px;
            z-index: 3;
            position: relative;
          }

          .stage-content {
            width: 280px;
            @include flex(column, flex-start, stretch, nowrap);
            gap: 16px;
            background: #ffffff;
            border: 2px solid #e4e7ed;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            border-color: #409EFF;

            .stage-point {
              border-color: #409EFF;
            }

            &.highlight-drop {
              border-color: #67C23A;
              box-shadow: 0 0 0 3px rgba(103, 194, 58, 0.2);
              transform: translateY(-5px);
            }

            .stage-header {
              text-align: center;

              .stage-period {
                font-size: 14px;
                color: var(--el-text-color-secondary);
                font-weight: normal;
              }
            }

            .drop-section {
              @include flex(column, flex-start, stretch, nowrap);
              gap: 8px;

              .drop-area {
                height: 94px;
                border: 2px dashed #d9d9d9;
                border-radius: 8px;
                transition: all 0.2s ease;
                @include flex(column, flex-start, stretch, nowrap);
                gap: 8px;
                overflow: hidden;

                &.highlight-drop {
                  border-color: #67C23A;
                  background: rgba(103, 194, 58, 0.05);
                }

                .content-placeholder {
                  height: 100%;
                  @include flex(row, center, center, nowrap);
                  color: var(--el-text-color-placeholder);
                  font-size: 12px;
                  text-align: center;
                  transition: all 0.3s ease;
                }

                .content-items {
                  @include flex(column, flex-start, stretch, nowrap);
                  gap: 8px;
                  height: 100%;
                  padding: 8px;

                  .content-item {
                    padding: 4px 8px;
                    border-radius: 4px;
                    cursor: move;
                    position: relative;
                    font-size: 12px;
                    line-height: 24px;
                    transition: all 0.2s ease;
                    @include text-ellipsis();

                    &.lifecycle-item {
                      background: linear-gradient(135deg, #409EFF, #5DADE2);
                      color: #ffffff;
                      border: 1px solid rgba(64, 158, 255, 0.3);
                    }

                    &.strategy-item {
                      background: linear-gradient(135deg, #67C23A, #85D47E);
                      color: #ffffff;
                      border: 1px solid rgba(103, 194, 58, 0.3);
                    }

                    &:hover {
                      transform: translateY(-2px);
                      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);

                      .delete-icon {
                        opacity: 1;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  // 删除图标样式
  .delete-icon {
    position: absolute;
    top: 50%;
    right: 2px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    transition: all 0.2s ease;
    color: #ffffff;
    background-color: rgba(0, 0, 0, 0.4);
    transform: translateY(-50%);
    @include flex(row, center, center, nowrap);

    &:hover {
      transform: scale(1.2) translateY(-50%);
      background-color: rgba(0, 0, 0, 0.6);
    }

    .el-icon {
      font-size: 10px;
      font-weight: bold;
    }
  }
}
</style>
