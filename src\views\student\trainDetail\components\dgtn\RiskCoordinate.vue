<template>
  <div class="risk-coordinate">
    <div class="section-title-light">
      <div class="title-label">{{ taskDetail.questionName }}
        <span class="title-desc">（请设置风险坐标，点击可以切换颜色）</span>
      </div>

    </div>
    <div class="coordinate-content">
      <div class="sub-title">风险坐标图</div>
      <div class="coordinate-wrapper">
        <div class="coordinate-effect-wrapper">
          <div class="coordinate-effect-title">影响</div>
          <div class="coordinate-effect" v-for="(item, i) in yEffectList" :key="i">
            {{ item }}
          </div>
        </div>
        <div class="coordinate-inner">
          <div class="coordinate-row" v-for="(item, i) in list" :key="i">
            <div class="coordinate-cell" v-for="(child, j) in item" :key="j"
              :class="{ middle: child === 1, high: child === 2 }" @click="handleToggle(i, j)"></div>
          </div>
        </div>
      </div>
      <div class="coordinate-effect-wrapper-x">
        <div class="coordinate-effect-x" v-for="(item, i) in xEffectList" :key="i">
          {{ item }}
        </div>
        <div class="coordinate-effect-title-x">可能性</div>
      </div>
      <div class="flex flex-wrap respond">
        <div class="sub-title">应对：</div>
        <div class="flex-1">{{ taskDetail?.taskOptions }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import type { TaskDetailItem } from '../../types'

interface IProps {
  taskDetail: TaskDetailItem
  disabled?: boolean,
  mode?: 'answer' | 'review'
}

const props = withDefaults(defineProps<IProps>(), {
  disabled: false,
  mode: 'answer'
})

const xEffectList = ['极低', '低', '中等', '高', '极高']
const yEffectList = ['极高', '高', '中等', '低', '极低']
const list = ref(Array.from(Array(5), () => Array.from(Array(5), () => 0)))
const handleToggle = (i: number, j: number) => {
  if (props.disabled) return
  const value = list.value[i][j] + 1;
  list.value[i][j] = value > 3 ? 0 : value
}

onMounted(() => {
  if (props.taskDetail?.stuAnswer) {
    list.value = JSON.parse(props.taskDetail.stuAnswer)
  }
})

const getStuAnswer = () => {
  return new Promise((resolve) => {
    const stuAnswer = JSON.stringify(list.value)
    resolve(stuAnswer)
  })
}

defineExpose({
  getStuAnswer
})
</script>

<style scoped lang="scss">
.risk-coordinate {
  height: 100%;
  @include flex(column, flex-start, flex-start, nowrap);
  background-image: linear-gradient(to bottom, #fff 0%, rgba(255, 255, 255, 0.5) 100%);
  box-shadow: 0px 2px 4px 0px rgba(120, 147, 187, 0.15);
  border-radius: 6px;
  border: solid 1px #ffffff;
  overflow: hidden;
  position: relative;

  .coordinate-content {
    width: 100%;
    min-height: 0;
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    color: #333;
    font-size: 14px;
    background: url('@/assets/images/train/bg-light-task.png') no-repeat bottom center;
    background-size: 100% auto;
  }

  .sub-title {
    font-weight: 700;
    margin-bottom: 40px;
  }

  .coordinate-wrapper {
    @include flex(row, flex-start, flex-start, nowrap);
  }

  .coordinate-effect-wrapper {
    @include flex(column, flex-start, flex-start, nowrap);
    height: 100%;
    padding: 0 15px 10px 20px;
    user-select: none;
    position: relative;

    &::after {
      position: absolute;
      content: '';
      right: -6px;
      top: -2px;
      border: 6px solid transparent;
      border-bottom-color: #000;
      font-size: 20px;
      transform: translateY(-50%);
    }
  }

  .coordinate-effect-wrapper-x {
    @include flex(row, flex-start, center, nowrap);
    display: inline-flex;
    padding: 16px 0 0 74px;
    position: relative;

    &::after {
      position: absolute;
      content: '';
      right: -19px;
      top: 0;
      border: 6px solid transparent;
      border-left-color: #000;
      font-size: 20px;
      transform: translateY(-50%);
    }
  }

  .coordinate-effect-title {
    line-height: 28px;
    height: 28px;

  }

  .coordinate-effect {
    line-height: 52px;
    height: 52px;
    margin-bottom: 2px;
  }

  .coordinate-effect-x {
    width: 52px;
    text-align: center;
    margin-right: 2px;
  }

  .coordinate-inner {
    padding: 28px 50px 11px 11px;
    border-bottom: 1px solid #000;
    border-left: 1px solid #000;
  }

  .coordinate-row {
    @include flex(row, flex-start, flex-start, nowrap);
    gap: 2px;
    margin-bottom: 2px;

    .coordinate-cell {
      width: 52px;
      height: 52px;
      background: #07D66D;
      cursor: pointer;
      transition: all 0.35s;

      &:hover {
        transform: scale(1.05);
      }

      &.middle {
        background: #1BCBDE;
      }

      &.high {
        background: #254CCE;
      }
    }
  }

  .respond {
    margin-top: 40px;
  }
}
</style>
