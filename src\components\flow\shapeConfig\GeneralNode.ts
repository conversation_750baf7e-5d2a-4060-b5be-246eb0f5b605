import { Shape } from "@antv/x6"

class GeneralNode extends Shape.Rect {
}

GeneralNode.config({
  width: 126,
  height: 38,
  zIndex: 100,
  markup: [
    {
      tagName: 'defs',
      children: [
        {
          tagName: 'linearGradient',
          selector: 'gradient',
          attrs: {
            id: 'nodeGradient',
            x1: '0%',
            y1: '0%',
            x2: '0%',
            y2: '100%'
          },
          children: [
            {
              tagName: 'stop',
              selector: 'gradientStart',
              attrs: {
                offset: '0%',
                'stop-color': '#E7F7FF'
              }
            },
            {
              tagName: 'stop',
              selector: 'gradientEnd',
              attrs: {
                offset: '100%',
                'stop-color': '#6F92AA'
              }
            }
          ]
        }
      ]
    },
    {
      tagName: 'rect',
      selector: 'main',
      attrs: {
        fill: 'url(#nodeGradient)'
      }
    },
    {
      tagName: 'title',
      selector: 'title',
    },
    {
      tagName: 'text',
      selector: 'label',
      attrs: {
        fill: '#000',
        'font-size': 12,
        'text-anchor': 'middle',
        'pointer-events': 'none',
        'font-weight': 'bold'
      }
    }
  ],
  attrs: {
    main: {
      stroke: 'none',
      fill: 'url(#nodeGradient)',
      width: 126,
      height: 38
    },
    title: {
      text: '',
    },
    label: {
      text: '',
      fill: '#001E22',
      strokeWidth: 2,
      fontSize: 14,
      textWrap: {
        width: 100, // 宽度减少 10px
        height: '80%', // 高度为参照元素高度的一半
        ellipsis: true, // 文本超出显示范围时，自动添加省略号
        breakWord: true, // 是否截断单词
      },
    }
  },
})
export default GeneralNode
