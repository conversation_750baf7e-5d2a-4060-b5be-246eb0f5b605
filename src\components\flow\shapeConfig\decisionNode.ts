import { Shape } from "@antv/x6"
import { lightPorts } from "./staticConfig"

class decisionNode extends Shape.Polygon {
}

decisionNode.config({
  width: 110,
  height: 68,
  zIndex: 100,
  markup: [
    {
      tagName: 'path',
      selector: 'main',
    },
    {
      tagName: 'title',
      selector: 'title',
    },
    {
      tagName: 'text',
      selector: 'label',
      attrs: {
        fill: '#fff',
        'font-size': 14,
        'text-anchor': 'middle',
        'pointer-events': 'none',
      }
    }
  ],
  data: {
    fontColor: '#ffffff',
    backgroundColor: '#ffa272',
    borderColor: '#da7b48'
  },
  attrs: {
    main: {
      refDResetOffset: 'M 0 50 L 50 0 L 100 50 L 50 100 L 0 50 z',
      fill: '#ffa272',
      stroke: '#da7b48',
      strokeWidth: 2,
      refX: 0,
      refY: 0,
      refWidth: '100%',
      refHeight: '100%'
    },
    title: {
      text: '',
    },
    label: {
      text: '',
      fill: '#fff',
      strokeWidth: 2,
      fontSize: 14,
      textWrap: {
        refWidth: '80%', // 宽度减少 10px
        height: '40%', // 高度为参照元素高度的一半
        ellipsis: true, // 文本超出显示范围时，自动添加省略号
        breakWord: true, // 是否截断单词
      },
    }
  },
  ports: { ...lightPorts }

})
export default decisionNode
