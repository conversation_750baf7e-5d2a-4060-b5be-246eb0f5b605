import { ref, onMounted, onBeforeUnmount } from 'vue'
import { Graph } from '@antv/x6'
import { Dnd } from '@antv/x6-plugin-dnd'
import { Transform } from '@antv/x6-plugin-transform'
import { Selection } from '@antv/x6-plugin-selection'
import { Snapline } from '@antv/x6-plugin-snapline'
import { Keyboard } from '@antv/x6-plugin-keyboard'
import { Clipboard } from '@antv/x6-plugin-clipboard'
import { History } from '@antv/x6-plugin-history'
import { registerNode, unregisterNode } from '@/components/flow/shapeConfig/registerNode'
import { registerNodeTools, unregisterNodeTools } from '@/components/flow/tools/registerNodeTools'
import type { Cell } from '@antv/x6'

export interface FlowShape {
  type: string
  name: string
  shape: string
}

export interface FlowDesignOptions {
  disabled?: boolean
  mode?: 'admin' | 'answer' | 'viewAnswer'
  onNodeSelected?: (cell: Cell) => void
  onNodeUnselected?: (cell: Cell) => void
}

interface FlowNode {
  id: string
  shape: string
  data: {
    name: string
  }
  tools?: Record<string, unknown>
}

export function useFlowDesign(options: FlowDesignOptions) {
  const { disabled = false, mode = 'answer', onNodeSelected, onNodeUnselected } = options

  const graphRef = ref<HTMLElement | null>(null)
  const graph = ref<Graph>()
  const dnd = ref<Dnd>()
  const currentNode = ref<Cell | null>(null)
  const visible = ref(false)

  // 初始化图形
  const initGraph = async (config: Record<string, any> = {}) => {
    if (!graphRef.value) return

    graph.value = new Graph({
      container: graphRef.value,
      background: {
        color: "#002752", // 设置画布背景颜色
      },
      translating: {
        restrict: true,
      },
      panning: {
        enabled: true,
      },
      interacting: {
        nodeMovable: !disabled
      },
      mousewheel: {
        enabled: true,
        zoomAtMousePosition: true,
        modifiers: ['ctrl', 'meta'],
        minScale: 0.5,
        maxScale: 2,
      },
      connecting: {
        snap: {
          radius: 20,
        },
        allowBlank: false,
        allowLoop: true,
        highlight: true,
        sourceAnchor: {
          name: "center",
        },
        targetAnchor: "center",
        connectionPoint: "anchor",
        router: "manhattan",
        createEdge() {
          return this.createEdge({
            shape: 'custom-edge',
            connector: 'normal',
            data: {
              fontColor: '#000',
              backgroundColor: '#ffffff',
              borderColor: '#507af1',
              lineWidth: 2,
              lineColor: '#507af1',
              connector: 'normal',
              isAnimated: false,
              segments: true
            },
            attrs: {
              line: {
                stroke: '#507af1',
                strokeWidth: 2,
                targetMarker: {
                  name: "block",
                  args: {
                    size: "6",
                  },
                },
              },
            },

          })
        },
        validateConnection({ targetMagnet }) {
          return !!targetMagnet
        },
      },
      highlighting: {
        magnetAdsorbed: {
          name: 'stroke',
          args: {
            attrs: {
              fill: '#5F95FF',
              stroke: '#5F95FF',
            },
          },
        },
      },
      ...config
    })

    dnd.value = new Dnd({
      target: graph.value,
      validateNode() {
        return true
      },
    })
    if (!disabled) {
      graph.value
        .use(
          new Transform({
            resizing: true,
            rotating: true,
          }),
        ).use(new Clipboard())
        .use(new History())
        .use(new Snapline())
    }
    graph.value.use(
      new Selection({
        enabled: true,
        pointerEvents: 'none',
        showNodeSelectionBox: true,
      }),
    ).use(new Keyboard())
    registerEvents()
    bindKeyboardEvents()
  }

  // 注入数据
  const injectData = (initData: string) => {
    if (!graph.value || !initData) return
    const data = JSON.parse(initData) as FlowNode[]
    // data.forEach((node) => {
    //   if (node.shape === 'edge' || disabled) {
    //     node.tools = {}
    //   }
    // })
    graph.value.fromJSON(data)
    // setTimeout(() => {
    //   data.forEach((node) => {
    //     const ports = document.querySelectorAll(
    //       `g[data-cell-id="${node.id}"] .x6-port`
    //     )
    //     ports.forEach((port) => {
    //       (port as SVGElement).style.visibility = "hidden"
    //     })
    //   })
    // }, 100)
    graph.value.centerContent()
  }

  // 控制连接桩显示/隐藏
  const showPorts = (ports: NodeListOf<SVGElement>, show: boolean) => {
    for (let i = 0, len = ports.length; i < len; i += 1) {
      ports[i].style.visibility = show ? 'visible' : 'hidden'
    }
  }

  // 注册事件
  const registerEvents = () => {
    if (!graph.value) return

    graph.value.on('node:mouseenter', () => {
      if (disabled) return
      const ports = graphRef.value!.querySelectorAll(
        '.x6-port-body',
      ) as NodeListOf<SVGElement>
      showPorts(ports, true)
    })

    graph.value.on('node:mouseleave', () => {
      if (disabled) return
      const ports = graphRef.value!.querySelectorAll(
        '.x6-port-body',
      ) as NodeListOf<SVGElement>
      showPorts(ports, false)
    })

    graph.value.on('cell:selected', ({ cell }) => {
      currentNode.value = cell
      visible.value = true
      if (disabled) return
      if (cell.isEdge()) {
        cell.attr('line', {
          strokeWidth: (cell.data?.lineWidth || 2) + 1
        })
        const removeBtnCfg = {
          distance: '30%',
          color: '#A0331F'
        }
        cell.addTools({
          name: 'button-remove',
          args: removeBtnCfg
        })
        cell.addTools({
          name: 'segments',
          args: {
            snapRadius: 10,
            precision: 99,
            threshold: 20,
            attrs: {
              fill: '#444',
            },
          },
        })

      }
      if (cell.isNode()) {
        cell.addTools({
          name: 'cus-button-remove',
          args: {
            x: '100%',
            y: 4,
            offset: {
              x: 10,
              y: -5
            }
          }
        })
      }
      onNodeSelected?.(cell)
    })

    graph.value.on('cell:unselected', ({ cell }) => {
      currentNode.value = null
      visible.value = false
      if (cell.isEdge()) {
        cell.attr('line', {
          strokeWidth: cell.data?.lineWidth || 2
        })
        cell.removeTool('button-remove')
        cell.removeTool('segments')
      }
      if (cell.isNode()) {
        cell.removeTool('cus-button-remove')
      }
      onNodeUnselected?.(cell)
    })
  }
  const handleUndo = () => {
    if (graph.value?.canUndo()) {
      graph.value?.undo()
    }
    return false
  }

  const handleRedo = () => {
    if (graph.value?.canRedo()) {
      graph.value?.redo()
    }
    return false
  }

  const handleZoomOut = () => {
    if (!graph.value) return
    const zoom = graph.value.zoom()
    if (zoom > 0.5) {
      graph.value.zoom(-0.1)
    }
  }

  const handleZoomIn = () => {
    if (!graph.value) return
    const zoom = graph.value.zoom()
    if (zoom < 2) {
      graph.value.zoom(0.1)
    }
  }

  const handleResetZoom = () => {
    if (!graph.value) return
    graph.value.zoom(1, {
      absolute: true
    })
  }

  const bindKeyboardEvents = () => {
    if (!graph.value) return

    // #region 快捷键与事件
    graph.value.bindKey(['meta+c', 'ctrl+c'], () => {
      if (!graph.value) return
      const cells = graph.value.getSelectedCells()
      if (cells.length) {
        graph.value.copy(cells)
      }
      return false
    })
    graph.value.bindKey(['meta+x', 'ctrl+x'], () => {
      if (!graph.value) return
      const cells = graph.value.getSelectedCells()
      if (cells.length) {
        graph.value.cut(cells)
      }
      return false
    })
    graph.value.bindKey(['meta+v', 'ctrl+v'], () => {
      if (!graph.value) return
      if (!graph.value.isClipboardEmpty()) {
        const cells = graph.value.paste({ offset: 32 })
        graph.value.cleanSelection()
        graph.value.select(cells)
      }
      return false
    })

    // undo redo
    graph.value.bindKey(['meta+z', 'ctrl+z'], () => {
      if (!graph.value) return
      if (graph.value.canUndo()) {
        graph.value.undo()
      }
      return false
    })
    graph.value.bindKey(['meta+shift+z', 'ctrl+shift+z'], () => {
      if (!graph.value) return
      if (graph.value.canRedo()) {
        graph.value.redo()
      }
      return false
    })

    // select all
    graph.value.bindKey(['meta+a', 'ctrl+a'], () => {
      if (!graph.value) return
      const nodes = graph.value.getNodes()
      if (nodes) {
        graph.value.select(nodes)
      }
    })

    // delete
    graph.value.bindKey('backspace', () => {
      if (!graph.value) return
      const cells = graph.value.getSelectedCells()
      if (cells.length) {
        graph.value.removeCells(cells)
      }
    })

    // zoom
    graph.value.bindKey(['ctrl+1', 'meta+1'], () => {
      if (!graph.value) return
      handleZoomIn()
    })
    graph.value.bindKey(['ctrl+2', 'meta+2'], () => {
      if (!graph.value) return
      handleZoomOut()
    })

    graphRef.value!.addEventListener('keydown', (event) => {
      event.preventDefault()
    })
  }
  // 处理拖拽开始
  const handleShapeDragStart = (event: DragEvent, item: FlowShape) => {
    const node = graph.value!.createNode({
      shape: item.shape,
    })
    node.attrs!.label.text = item.name
    node.attrs!.title.text = item.name
    node.setData({
      name: item.name
    })
    if (item.type === 'text') {
      //文本节点和过程节点用同一个配置文件，手动修改一下背景色和data的数据
      node.setAttrs({
        main: {
          fill: '#f180f0',
          stroke: '#d007cf'
        }
      })
      node.setData({
        backgroundColor: '#f180f0',
        borderColor: '#d007cf'
      })
    }
    dnd.value?.start(node, event)
  }

  // 关闭属性面板
  const handleClosePanel = () => {
    visible.value = false
    currentNode.value = null
    graph.value?.cleanSelection()
  }

  // 获取图形数据
  const getGraphData = () => {
    const data = graph.value?.toJSON().cells
    if (!data) return Promise.resolve('')
    data.forEach((node) => {
      delete node.tools
    })
    return Promise.resolve(JSON.stringify(data))
  }



  onMounted(() => {
    registerNode(Graph)
    registerNodeTools(Graph)
  })

  onBeforeUnmount(() => {
    if (mode !== 'viewAnswer') {
      unregisterNode(Graph)
      unregisterNodeTools(Graph)
    }
    // 清理tooltip
    const tooltip = document.querySelector('.node-tooltip')
    if (tooltip) {
      document.body.removeChild(tooltip)
    }
  })

  return {
    initGraph,
    graphRef,
    graph,
    currentNode,
    visible,
    handleShapeDragStart,
    handleClosePanel,
    getGraphData,
    injectData,
    handleUndo,
    handleRedo,
    handleZoomOut,
    handleZoomIn,
    handleResetZoom
  }
}
