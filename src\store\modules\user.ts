import type { UserState } from "@/store/types"

const state: UserState = {
    userInfo: {},
    productInfoList: [] //平台列表 (平台相关信息)
}

const actions = {}
const mutations = {
    SET_USERINFO: (state: UserState, data: any) => {
        state.userInfo = data
    },
    CLEAR_USERINFO: (state: UserState) => {
        state.userInfo = {}
    },
    SET_PRODUCT_INFO_LIST: (state: UserState, data: any) => {
        state.productInfoList = data
    }
}

export default {
    namespaced: true,
    state,
    actions,
    mutations,
}
