<template>
  <div class="base-data-container" v-loading="loading">
    <div class="base-data-tabs">
      <el-tabs type="card" v-model="basicType" @change="handleBasicTypeChange">
        <el-tab-pane v-for="item in tabsList" :key="item.value" :label="item.label" :name="item.value">
        </el-tab-pane>
      </el-tabs>
      <el-button class="back-btn" type="primary" @click="handleBack">返回</el-button>
      <div class="tree-container">
        <div class="tree-header">
          <el-button type="primary" @click="append(null)" :icon="Plus">新增{{ ['1', '2'].includes(basicType) ? '一级'
            :
            '' }}</el-button>
          <el-button type="primary" @click="importBasicData()">导入数据</el-button>
        </div>
        <el-tree class="custom-tree" :data="baseDataList" :props="defaultProps" node-key="basicId" default-expand-all
          :highlight-current="true">
          <template #default="{ node, data }">
            <div class="custom-tree-node">
              <div class="node-content">
                <el-icon class="node-icon">
                  <component :is="getNodeIcon(data.basicType)" />
                </el-icon>
                <span class="node-label">{{ node.label }}</span>
              </div>
              <div class="node-actions">
                <el-button v-if="['1', '2'].includes(basicType)" type="primary" link :icon="Plus"
                  @click.stop="append(data)" title="新增子节点">
                </el-button>
                <el-button type="primary" link :icon="Edit" @click.stop="edit(data)" title="编辑">
                </el-button>
                <el-button :icon="Delete" link type="danger" @click.stop="remove(node, data)" title="删除">
                </el-button>
              </div>
            </div>
          </template>
        </el-tree>
      </div>
    </div>
    <el-dialog v-model="dialogVisible" :title="dialogType === 'add' ? '新增' : '编辑'" width="500px">
      <el-form :model="form" label-width="60px">
        <el-form-item label="名称">
          <el-input v-model.trim="form.basicName" :maxlength="20" show-word-limit @keyup.enter="handleDialogConfirm" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="handleDialogClose">取消</el-button>
        <el-button :loading="btnLoading" type="primary" @click="handleDialogConfirm">确定</el-button>
      </template>
    </el-dialog>

<el-dialog v-model="importDialogVisible" title="导入数据" width="500px">
  <el-button type="primary" @click="downloadTemplate">下载模板</el-button>

  <!-- 文件上传区域 -->
  <div class="upload-section">
    <el-upload
      v-if="!selectedFile"
      class="upload-demo"
      drag
      action="#"
      :auto-upload="false"
      :on-change="handleFileChange"
      :limit="1"
      accept=".xlsx,.xls"
    >
      <el-icon><Upload /></el-icon>
      <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
    </el-upload>

    <!-- 已选文件展示 -->
    <div v-else class="selected-file">
      <div class="file-info">
        <el-icon><Document /></el-icon>
        <span>{{ selectedFile.name }}</span>
      </div>
      <div class="actions">
        <el-button type="text" @click="removeFile">重新选择</el-button>
      </div>
    </div>
  </div>

  <template #footer>
    <el-button @click="handleCancel">取消</el-button>
    <el-button type="primary" @click="handleImportConfirm">确定</el-button>
  </template>
</el-dialog>
  </div>
</template>

<script setup lang="ts">
import { Delete, Plus, Edit, User, UserFilled, Document, Collection } from '@element-plus/icons-vue'
import { ref, computed } from 'vue'
import * as api from '@/api/admin'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { BaseDataItem, BaseDataItemTree } from '@/views/admin/types'
import type { Component } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Upload } from '@element-plus/icons-vue'

// 定义树节点类型
type TreeNode = {
  label: string
  data: BaseDataItem
  key: string
  children?: TreeNode[]
}

const defaultProps = {
  label: 'basicName',
  children: 'children'
}
const importDialogVisible = ref(false)
const selectedFile = ref<File | null>(null)
const router = useRouter()
const route = useRoute()
const enterpriseId = ref(route.query.enterpriseId as string)
const loading = ref(false)
const btnLoading = ref(false)
const basicType = ref('1')
const tabsList = ref([
  {
    label: '人员',
    value: '1'
  },
  {
    label: '角色',
    value: '2'
  },
  {
    label: '职级',
    value: '3'
  },
  {
    label: '表单',
    value: '4'
  },
  {
    label: '单据',
    value: '5'
  },
  {
    label: '流程步骤',
    value: '6'
  },
  {
    label: '岗位/角色',
    value: '7'
  },
  {
    label: '信息系统',
    value: '8'
  }
])
const dialogVisible = ref(false)
const form = ref({
  basicName: '',
})
const dialogType = ref<'add' | 'edit'>('add')
const currentData = ref<BaseDataItem | null>(null)

const handleBack = () => {
  router.back()
}

// 存储所有原始数据
const allRawData = ref<BaseDataItem[]>([])

// 根据当前类型过滤并转换为树形结构的数据
const baseDataList = computed(() => {
  const filteredData = allRawData.value.filter(item => item.basicType === +basicType.value)
  return buildTree(filteredData)
})

// 将列表数据转换为树形结构
const buildTree = (list: BaseDataItem[]): BaseDataItemTree[] => {
  const map: { [key: string]: BaseDataItemTree } = {}
  const result: BaseDataItemTree[] = []

  // 先将所有项转换为树节点并建立映射
  list.forEach(item => {
    map[item.basicId] = { ...item, children: [] }
  })

  // 构建树形结构
  list.forEach(item => {
    const node = map[item.basicId]
    if (item.parentId !== '0') {
      // 如果有父节点，将当前节点添加到父节点的children中
      const parent = map[item.parentId]
      if (parent) {
        parent.children = parent.children || []
        parent.children.push(node)
      }
    } else {
      // 如果没有父节点，则为根节点
      result.push(node)
    }
  })
  return result
}

const getBaseDataList = async () => {
  loading.value = true
  try {
    const { data: { code, msg, data } } = await api.getBaseDataList({ enterpriseId: enterpriseId.value })
    if (code === 200) {
      // 保存所有原始数据
      allRawData.value = data
    } else {
      ElMessage.error(msg)
    }
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }
}

const handleBasicTypeChange = (value: string) => {
  basicType.value = value
  // 不再需要重新请求数据，直接使用计算属性获取对应类型的数据
}

getBaseDataList()

const importBasicData = () => {
   importDialogVisible.value = true
}

const downloadTemplate = async () => {
  try {
    const res = await api.downLoadTemplate()
    const blob = new Blob([res.data])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = '基础数据模板.xlsx'
    link.click()
    window.URL.revokeObjectURL(url) // 释放内存
  } catch (error) {
    console.error('下载模板失败', error)
    ElMessage.error('下载模板失败')
  }
}
const removeFile = () => {
  selectedFile.value = null
}
const handleFileChange = (file: any) => {
  if (file.raw) {
    selectedFile.value = file.raw
  }
}
const handleCancel = () => {
  importDialogVisible.value = false
  selectedFile.value = null
}

const handleImportConfirm = async () => {
  if (!selectedFile.value) {
    ElMessage.warning('请选择一个文件')
    return
  }
  const parmas = {
            file: selectedFile.value,
            enterpriseId:enterpriseId.value
          }
  try {
    const { data: { code, msg } } = await api.importBaseData(parmas)
    if (code === 200) {
      ElMessage.success('导入成功')
      importDialogVisible.value = false
      getBaseDataList()
    } else {
      ElMessage.error(msg)
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('导入失败')
  }finally {
    selectedFile.value = null  
  }
}

const append = (data: BaseDataItem | null) => {
  dialogVisible.value = true
  dialogType.value = 'add'
  currentData.value = data
}

const edit = (data: BaseDataItem) => {
  dialogVisible.value = true
  dialogType.value = 'edit'
  currentData.value = data
  form.value.basicName = data.basicName
}

const remove = (node: TreeNode, data: BaseDataItem) => {
  if (node.children && node.children.length) {
    return ElMessage.warning('请先删除子节点')
  }
  ElMessageBox.confirm(
    '你确定要删除吗？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(async () => {
    try {
      const { data: { code, msg } } = await api.delBasicData({ basicId: data.basicId });
      if (code === 200) {
        ElMessage.success("删除成功！");
        getBaseDataList()
      } else {
        ElMessage.error(msg)
      }
    } catch (error) {
      console.error(error)
    }
  }).catch(() => { });
}

const handleDialogClose = () => {
  dialogVisible.value = false
  form.value.basicName = ''
}

const handleDialogConfirm = async () => {
  if (!form.value.basicName.trim()) return ElMessage.warning('请输入名称')
  const params: { [key: string]: string } = {
    ...form.value,
    enterpriseId: enterpriseId.value,
    basicType: basicType.value,
  }
  if (dialogType.value === 'edit' && currentData.value) {
    params.basicId = currentData.value!.basicId
    params.parentId = currentData.value!.parentId
  }
  if (dialogType.value === 'add' && currentData.value) {
    params.parentId = currentData.value.basicId
  }
  try {
    btnLoading.value = true
    const { data: { code, msg } } = await api.addEditBasicData(params);
    if (code === 200) {
      ElMessage.success("保存成功！");
      form.value.basicName = ''
      getBaseDataList()
      dialogVisible.value = false
    } else {
      ElMessage.error(msg)
    }
  } catch (error) {
    console.error(error)
  } finally {
    btnLoading.value = false
  }
}

// 获取节点图标
const getNodeIcon = (type: string) => {
  const iconMap: Record<string, Component> = {
    '1': User, // 人员
    '2': UserFilled, // 角色
    '3': Collection, // 职级
    '4': Document, // 表单
    '5': Document, // 单据
    '6': Document, // 流程步骤
    '7': Document, // 岗位/角色
    '8': Document, // 信息系统
  }
  return iconMap[type] || Document
}
</script>

<style scoped lang="scss">
.base-data-container {
  padding: 20px;

  .base-data-tabs {
    padding: 20px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
    position: relative;

    .back-btn {
      position: absolute;
      right: 20px;
      top: 20px;
    }

    .tree-container {
      margin-top: 20px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;

      .tree-header {
        padding: 12px 16px;
        border-bottom: 1px solid #e4e7ed;
        background-color: #f5f7fa;
        text-align: right;
      }

      .custom-tree {
        padding: 10px;

        :deep(.el-tree-node__content) {
          height: 40px;

          &:hover {
            background-color: #f5f7fa;
          }
        }

        .custom-tree-node {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: space-between;
          font-size: 14px;
          padding-right: 8px;
          width: 100%;

          .node-content {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
            overflow: hidden;

            .node-icon {
              font-size: 16px;
              color: var(--el-color-primary);
              flex-shrink: 0;
            }

            .node-label {
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }

          .node-actions {
            display: none;
            align-items: center;
            gap: 4px;

            .el-button {
              padding: 4px;

              &:hover {
                background-color: #f5f7fa;
                border-radius: 4px;
              }
            }
          }

          &:hover {
            .node-actions {
              display: flex;
            }
          }
        }
      }
    }
  }
}
.upload-section {
  margin: 20px 0;
}

.selected-file {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #f9f9f9;

  .file-info {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .actions {
    cursor: pointer;
    color: var(--el-color-primary);
  }
}
</style>
