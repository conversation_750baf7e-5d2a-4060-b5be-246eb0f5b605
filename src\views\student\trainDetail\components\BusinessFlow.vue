<template>
  <div class="organizational ">
    <div class="options-wrapper">
      <OptionList icon-src="improve" :options="options" :usedOptionIds="usedOptionIds" :disabled="disabled"
        @drag-start="handleOptionDragStart" />
    </div>
    <div class="base-content content">
      <div class="section-title">
        <div class="title-label">{{ taskDetail.questionName }}</div>
      </div>
      <div class="content-wrapper">
        <div class="graph" ref="graphRef"></div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import OptionList from './OptionList.vue'
import { onMounted, ref, nextTick, onBeforeUnmount, watch } from 'vue'
import { Graph, Shape } from '@antv/x6'
import { Dnd } from '@antv/x6-plugin-dnd'
import { registerNode, unregisterNode } from "@/components/flow/shapeConfig/registerNode";
import { registerNodeTools, unregisterNodeTools } from "@/components/flow/tools/registerNodeTools";
import type { OptionItem } from '@/views/admin/types'
import type { TaskDetailItem } from '../types'
import type { Node, Cell } from '@antv/x6'

interface IProps {
  taskDetail: TaskDetailItem,
  disabled?: boolean
  mode?: 'viewAnswer' | 'answer' // viewAnswers：查看答案模式，answer：答题模式
}


const props = defineProps<IProps>()

const usedOptionIds = ref<Set<string>>(new Set())
const options = ref<OptionItem[]>([])
const currentOption = ref<OptionItem>()

const graphRef = ref<HTMLDivElement | null>()
const graph = ref<Graph>()
const dnd = ref<Dnd>();

const initGraph = async () => {
  await nextTick();
  if (!graphRef.value) return
  graph.value = new Graph({
    container: graphRef.value,
    background: {
      color: "rgba(0,0,0,0)", // 设置画布背景颜色
    },
    translating: {
      restrict: true,
    },
    //画布拖拽
    panning: {
      enabled: true,
    },
    interacting: {
      nodeMovable: (view) => {
        const node = view.cell
        const { enableMove } = node.getData()
        return !!enableMove
      }
    },
    highlighting: {
      magnetAvailable: {
        name: "stroke",
        args: {
          padding: 4,
          attrs: {
            "stroke-width": 4,
            stroke: '#ffffff',
          },
        },
      },
      magnetAdsorbed: {
        name: "stroke",
        args: {
          padding: 4,
          attrs: {
            "stroke-width": 8,
            stroke: '#ffffff',
          },
        },
      },
    },
    connecting: {
      snap: true,
      allowBlank: false,
      allowLoop: false,
      highlight: true,
      allowMulti: false,
      sourceAnchor: {
        name: "center",
      },
      targetAnchor: "center",
      connectionPoint: "anchor",
      router: "manhattan",
      validateMagnet(data) {
        const { magnet } = data;
        return magnet.getAttribute("port-group") !== "in";
      },
      createEdge() {
        return new Shape.Edge({
          zIndex: -1,
          attrs: {
            line: {
              strokeDasharray: "5 5",
              stroke: '#ffffff',
              strokeWidth: 1,
              targetMarker: {
                name: "block",
                args: {
                  size: "6",
                },
              },
            },
          },
        });
      },
      validateConnection(data) {
        const { targetView, sourceMagnet, targetMagnet } = data;
        if (!sourceMagnet || !targetMagnet) {
          return false;
        }

        // 判断目标链接桩是否可连接
        const portId = targetMagnet.getAttribute("port");
        const node: any = targetView?.cell;

        const port = node?.getPort(portId);
        if (!port) {
          return false;
        }

        return true;
      },
    },
    embedding: {
      enabled: true,
      frontOnly: true,
      findParent({ node }) {
        const bbox = node.getBBox()
        return this.getNodes().filter((node) => {
          const data = node.getData<any>()
          if (data && data.answer) {
            const targetBBox = node.getBBox()
            return bbox.isIntersectWithRect(targetBBox)
          }
          return false
        })
      }
    }
  });
  dnd.value = new Dnd({
    target: graph.value,
    validateNode() {
      return true
    },
  });
  resigterEvent()
  injectData()

}

initGraph()

const onDelete = (data: any) => {
  const cell = data.cell as Cell
  usedOptionIds.value.delete(cell.data.id)
  cell?.setData({
    name: ''
  })
  cell?.setAttrs({
    label: {
      text: ' ',
    },
    title: {
      text: ' '
    }
  })
  cell?.removeTools()
}

const injectData = () => {
  if (!graph.value || !props.taskDetail.stuAnswer) return
  const data = JSON.parse(props.taskDetail.stuAnswer)
  data.forEach((node: any) => {
    //如果已拖拽进去的，收集已使用options
    if (node.shape !== 'edge' && node.data.name && node.data.answer) {
      usedOptionIds.value.add(node.data.id)
      if (!props.disabled && node.tools?.items) {
        node.tools.items = [{
          name: 'cus-button-remove',
          args: {
            x: 130,
            y: 4,
            offset: {
              x: -5,
              y: -5
            },
            onClick: onDelete
          }
        }]
      }
    }
    // 线和没有拖拽进去的节点，禁止模式下，把tools
    if (node.shape === 'edge' || (node.shape !== 'edge' && !node.data.name) || props.disabled || !node.data?.answer) {
      node.tools = {}
    }
  })
  graph.value.fromJSON(data)
  data.forEach((node: Cell) => {
    setTimeout(() => {
      const ports = document.querySelectorAll(
        `g[data-cell-id="${node.id}"] .x6-port`
      );
      ports.forEach((port) => {
        (port as SVGElement).style.visibility = "hidden";
      });
    }, 100)
  })
  graph.value.centerContent()
}

// 注册事件
const resigterEvent = () => {
  if (!graph.value) return
  graph.value.on('node:added', ({ node }) => {
    setTimeout(() => {
      if (!node.parent) {
        graph.value!.removeNode(node)
      }
    });
  })

  graph.value.on('node:embedded', ({ node, currentParent }) => {
    //如果父节点原本已经放置了子节点，直接把子节点删除掉
    if (currentParent?.data?.name) {
      usedOptionIds.value.delete(currentParent.data.id)
    }
    //作为子节点直接把name放入父节点中，然后再把子节点删除掉
    currentParent?.setAttrs({
      label: {
        text: node.data.name
      },
      title: {
        text: node.data.name
      }
    })
    currentParent?.setData({
      name: node.data.name,
      id: node.data.id
    })
    currentParent?.addTools({
      name: 'cus-button-remove',
      args: {
        x: 130,
        y: 4,
        offset: {
          x: -5,
          y: -5
        },
        onClick: onDelete
      }
    })

    usedOptionIds.value.add(currentOption.value!.id)
    node.remove()
  });
}

const handleOptionDragStart = (item: OptionItem, event: DragEvent) => {
  const node = graph.value!.createNode({ id: item.id, shape: 'general-node' });
  currentOption.value = item
  node.attrs!.label.text = item.name;
  node.attrs!.title.text = item.name;
  node.setData({
    name: item.name,
    id: item.id
  });
  dnd.value?.start(node, event);
}


const getStuAnswer = () => {
  return Promise.resolve(JSON.stringify(graph.value!.toJSON().cells))
}

defineExpose({
  getStuAnswer
})

onMounted(() => {
  registerNode(Graph);
  registerNodeTools(Graph);
  options.value = props.taskDetail.taskOptions ? JSON.parse(props.taskDetail.taskOptions) : []
})

onBeforeUnmount(() => {
  // todo 查看答案模式下不卸载
  if (props.mode !== 'viewAnswer') {
    unregisterNode(Graph)
    unregisterNodeTools(Graph)
  }
})


//为了提交之后，更新disabled状态
watch(() => props.disabled, (newVal) => {
  //如果禁止了，要重新更新一下画布
  if (newVal) {
    graph.value?.clearCells()
    injectData()
  }
})

</script>

<style scoped lang="scss">
.organizational {
  @include flex(row, flex-start, flex-start, nowrap);
  height: 100%;
  gap: 16px;

  .base-content {
    height: 100%;
    gap: 20px;
    @include flex(column, flex-start, flex-start, nowrap);

    .content-wrapper {
      width: 100%;
      flex: 1;
      min-height: 0;
      @include border-1px()
    }
  }

  .options-wrapper {
    width: 25%;
    height: 100%;
  }

  .content {
    min-width: 0;
    width: 75%;
  }

  .graph {
    height: 100%;
  }
}
</style>
