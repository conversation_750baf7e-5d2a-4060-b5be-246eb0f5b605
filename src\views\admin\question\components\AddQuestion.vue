<template>
  <el-dialog :model-value='visible' :title='dialogType === "add" ? "新增题目" : "编辑题目"' width='800px'
    :before-close='handleClose'>
    <el-form :model="form" ref="formRef" :rules="rules" label-width="100px">
      <el-form-item label="题目名称" prop="questionName">
        <el-input v-model.trim="form.questionName" placeholder="请输入题目名称，20字内" maxlength="20" show-word-limit />
      </el-form-item>
      <el-form-item label="所属分类" prop="categoryId">
        <el-select v-model="form.categoryId" placeholder="请选择分类" class="w-100">
          <el-option v-for="item in categoryList" :key="item.categoryId" :label="item.categoryName"
            :value="item.categoryId" />
        </el-select>
      </el-form-item>
      <el-form-item label="题型" prop="questionType">
        <div class="w-100 type-list">
          <el-select v-model="form.questionType" placeholder="请选择题型" :disabled="dialogType === 'edit'"
            @change="handleQuestionTypeChange">
            <el-option v-for="item in QUESTION_TYPE" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
          <el-select v-if="questionChildList.length && +form.questionType === 8" v-model="form.questionSecondType"
            placeholder="请选择" :disabled="dialogType === 'edit'">
            <el-option v-for="item in questionChildList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
      </el-form-item>
      <el-form-item v-if="+form.questionType === 6" label="所属企业" prop="enterpriseId">
        <el-select v-model="form.enterpriseId" placeholder="请选择企业" class="w-100" :disabled="dialogType === 'edit'">
          <el-option v-for="item in enterpriseList" :key="item.enterpriseId" :label="item.enterpriseName"
            :value="item.enterpriseId" />
        </el-select>
      </el-form-item>
      <el-form-item label="案例资料" prop="taskNote">
        <div>
          <ckeditor v-model="form.taskNote" />
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submitForm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang='ts'>
import ckeditor from "@/components/ckeditor/index.vue"
import { ref, reactive, watch } from 'vue'
import { QUESTION_TYPE } from "@/utils/constant"
import { addOrUp, getCategoryList, getEnterpriseList as getEnterpriseListApi } from '@/api/admin'
import { ElMessage } from 'element-plus'
import type { CategoryItem, EnterpriseItem } from '@/views/admin/types'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dialogType: {
    type: String,
    default: 'add'
  },
  editData: {
    type: Object,
    default: () => ({})
  }
})

const emits = defineEmits(['update:visible', 'success'])
const questionChildList = ref<{ label: string, value: number }[]>([])
const formRef = ref()
const loading = ref(false)
const form = reactive({
  questionName: '',
  questionType: '',
  categoryId: '',
  taskNote: '',
  enterpriseId: '',
  questionSecondType: 0
})

const enterpriseList = ref<EnterpriseItem[]>([])
const getEnterpriseList = async () => {
  loading.value = true;
  const { data: { code, data } } = await getEnterpriseListApi({});
  loading.value = false
  if (code === 200) {
    enterpriseList.value = data;
  }
}

getEnterpriseList()

const categoryList = ref<CategoryItem[]>([])

const getCategoryLists = async () => {
  loading.value = true;
  const { data: { code, msg, data } } = await getCategoryList({});
  loading.value = false
  if (code === 200) {
    categoryList.value = data;
  } else {
    ElMessage.error(msg)
  }
}

getCategoryLists()

watch(() => props.visible, (newVal) => {
  if (newVal) {
    if (props.dialogType === 'edit') {
      form.questionName = props.editData.questionName
      form.questionType = props.editData.questionType
      form.categoryId = props.editData.categoryId
      form.taskNote = props.editData.taskNote
      form.enterpriseId = props.editData.enterpriseId
      form.questionSecondType = props.editData.questionSecondType
      const questionTypeItem = QUESTION_TYPE.find(item => item.value === +props.editData.questionType)
      if (questionTypeItem?.children) {
        questionChildList.value = questionTypeItem.children
      }
    }
    formRef.value?.clearValidate()
  }
})

const rules = {
  questionName: [
    { required: true, message: '请输入题目名称', trigger: 'blur' },
  ],
  categoryId: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ],
  questionType: [
    { required: true, message: '请选择题型', trigger: 'change' }
  ],
  enterpriseId: [
    { required: true, message: '请选择企业', trigger: 'change' }
  ]
}

const handleClose = () => {
  formRef.value?.resetFields()
  // 重置表单数据
  Object.assign(form, {
    questionName: '',
    questionType: '',
    categoryId: '',
    taskNote: '',
    enterpriseId: '',
    questionSecondType: 0
  })
  emits('update:visible', false)
}

const handleQuestionTypeChange = () => {
  form.enterpriseId = ''
  form.questionSecondType = 0
  const item = QUESTION_TYPE.find(item => item.value === +form.questionType)
  if (item?.children) {
    questionChildList.value = item.children
    form.questionSecondType = item.children[0].value
  }
}

const submitForm = async () => {
  if (!formRef.value) return
  try {
    await formRef.value.validate()
    loading.value = true

    const { data: { code, msg } } = await addOrUp({ ...form, questionId: props.editData.questionId });
    if (code === 200) {
      ElMessage.success('保存成功')
      emits('success', form)
      handleClose()
    } else {
      ElMessage.error(msg || '保存失败')
    }
  } catch (error) {
    console.log('表单验证失败:', error)
  } finally {
    loading.value = false // 关闭加载状态
  }
}
</script>

<style scoped lang='scss'>
.w-100 {
  width: 100%;
}

.type-list {
  @include flex(row, flex-start, flex-start, nowrap);
  gap: 10px;
}
</style>
