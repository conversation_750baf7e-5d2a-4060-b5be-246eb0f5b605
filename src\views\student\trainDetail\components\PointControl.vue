<template>
  <div class="point-control">
    <div class=" options-wrapper">
      <OptionList icon-src="flag" :options="options" :used-option-ids="usedOptionIds" :disabled="disabled"
        @drag-start="handleOptionDragStart" />
    </div>
    <div class="base-content content">
      <div class="section-title">
        <div class="title-label">{{ taskDetail.questionName }}</div>
      </div>
      <div class="content-wrapper ">
        <el-scrollbar height="100%">
          <div class="table-wrapper">
            <table border="0" class="table">
              <tbody>
                <tr v-for="(item, rowIndex) in tableList" :key="rowIndex">
                  <td class="">
                    <div class="row-title">{{ item.name }}</div>
                  </td>
                  <td class="cell-wrapper">
                    <div v-for="(child, index) in item.children" :key="index" class="cell" :data-row-id="rowIndex"
                      :data-index="index" @dragover.prevent @dragenter="handleDragEnter($event)"
                      @dragleave="handleDragLeave($event)" @drop="handleDrop($event, rowIndex, index)"
                      :draggable="!disabled" @dragstart="handleCellDragStart($event, rowIndex, index)">
                      <div v-if="child" class="cell-content" :title="child">
                        {{ child || '' }}
                        <div v-if="!disabled" class="delete-icon" @click.stop="handleDelete(rowIndex, index)">
                          <el-icon>
                            <Close />
                          </el-icon>
                        </div>
                      </div>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import OptionList from './OptionList.vue'
import { Close } from '@element-plus/icons-vue'
import type { TaskDetailItem } from '../types'
import type { OptionItem } from '@/views/admin/types'

interface IProps {
  taskDetail: TaskDetailItem,
  disabled?: boolean
}

interface TableItem {
  name: string
  children: string[]  // 改为存储选项名称
}

const props = withDefaults(defineProps<IProps>(), {
  disabled: false
})

const options = ref<OptionItem[]>([])


const tableList = ref<TableItem[]>([])


const optionsMap = computed(() => {
  const map = new Map<string, string>()
  options.value.forEach(item => {
    map.set(item.name, item.id)
  })
  return map
})

// 计算已使用的选项名称集合
const usedOptionIds = computed(() => {
  const names = new Set<string>()
  tableList.value.forEach(item => {
    item.children.forEach(name => {
      if (name) {
        const id = optionsMap.value.get(name)
        if (id) {
          names.add(id)
        }
      }
    })
  })
  return names
})

// 处理选项拖拽开始
const handleOptionDragStart = (item: OptionItem, event: DragEvent) => {
  event.dataTransfer?.setData('text/plain', JSON.stringify(item))
}

// 处理放置
const handleDrop = (event: DragEvent, rowIndex: number, index: number) => {
  const data = event.dataTransfer?.getData('text/plain')
  if (!data) return

  const option = JSON.parse(data) as OptionItem
  const currentRow = tableList.value[rowIndex]

  // 如果该选项已经在其他位置，先移除
  tableList.value.forEach(item => {
    item.children = item.children.map(name =>
      name === option.name ? '' : name
    )
  })

  // 设置新位置的值
  currentRow.children[index] = option.name

  // 移除高亮效果
  const cell = event.currentTarget as HTMLElement
  cell.classList.remove('highlight-drop')
}

const handleCellDragStart = (event: DragEvent, rowIndex: number, index: number) => {
  const row = tableList.value[rowIndex]
  const optionName = row?.children[index]
  if (optionName) {
    const option = options.value.find(opt => opt.name === optionName)
    if (option) {
      event.dataTransfer?.setData('text/plain', JSON.stringify(option))
      event.dataTransfer!.effectAllowed = 'move'

      // 创建拖拽图像
      const dragImage = document.createElement('div')
      dragImage.textContent = optionName
      dragImage.style.cssText = `
        position: absolute;
        left: -9999px;
        background: rgb(101, 151, 247);
        color: #fff;
        padding: 8px 16px;
        border-radius: 4px;
        font-size: var(--font-size-small);
        white-space: nowrap;
        pointer-events: none;
        z-index: 9999;
        width: 200px;
        text-align: center;
        overflow: hidden;
        text-overflow: ellipsis;
      `
      document.body.appendChild(dragImage)
      event.dataTransfer!.setDragImage(dragImage, 0, 0)

      // 在下一帧移除临时元素
      setTimeout(() => {
        document.body.removeChild(dragImage)
      }, 0)
    }
  }
}

const handleDragEnter = (event: DragEvent) => {
  const cell = event.currentTarget as HTMLElement
  cell.classList.add('highlight-drop')
}

const handleDragLeave = (event: DragEvent) => {
  const cell = event.currentTarget as HTMLElement
  cell.classList.remove('highlight-drop')
}

// 添加删除处理函数
const handleDelete = (rowIndex: number, index: number) => {
  const currentRow = tableList.value[rowIndex]
  if (currentRow) {
    currentRow.children[index] = ''
  }
}

onMounted(() => {
  tableList.value = props.taskDetail.stuAnswer ? JSON.parse(props.taskDetail.stuAnswer) : []
  options.value = props.taskDetail.taskOptions ? JSON.parse(props.taskDetail.taskOptions) : []
})

const getStuAnswer = () => {
  return Promise.resolve(JSON.stringify(tableList.value))
}

defineExpose({
  getStuAnswer
})
</script>

<style scoped lang="scss">
.point-control {
  @include flex(row, flex-start, flex-start, nowrap);
  height: 100%;
  gap: 16px;

  .base-content {
    height: 100%;
    gap: 20px;
    @include flex(column, flex-start, flex-start, nowrap);

    .content-wrapper {
      width: 100%;
      flex: 1;
      min-height: 0;
      @include border-1px()
    }
  }

  .options-wrapper {
    width: 25%;
    height: 100%;
  }

  .content {
    min-width: 0;
    width: 75%;
  }

  .table-wrapper {
    padding: 20px;
    user-select: none;
  }

  .table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
    font-family: "Microsoft YaHei UI";

    td {
      max-width: 200px;
      width: 30%;
      border: 1px solid #2BA0AD;
      word-break: break-all;
      height: 40px;
      line-height: 40px;

      &.cell-wrapper {
        width: 70%;
      }
    }

    .row-title {
      font-size: var(--font-size);
      font-weight: 700;
      max-width: 200px;
      color: #fff;
      text-align: center;
      line-height: 24px;
      margin: 0 auto;
      padding: 0 12px;
      @include multi-ellipsis();
    }

    .cell {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
      font-size: var(--font-size-small);
      position: relative;
      line-height: 40px;

      .cell-content {
        background-color: rgb(101, 151, 247, 70%);
        color: #fff;
        cursor: move;
        width: 100%;
        text-align: center;
        padding: 0 14px;
        height: 100%;
        @include text-ellipsis();
        box-shadow: 0 0 0 1px #409EFF;


        &:hover {
          .delete-icon {
            opacity: 1;
          }
        }
      }

      &+.cell {
        border-top: 1px solid #2BA0AD;
      }

      &.highlight-drop {
        box-shadow: 0 0 0 2px #409EFF;
        border-color: #409EFF;
        background: rgba(64, 158, 255, 0.3);
        z-index: 1;

        &::after {
          content: '';
          position: absolute;
          inset: -2px;
          border: 2px dashed #409EFF;
          pointer-events: none;
          animation: borderDash 20s linear infinite;
        }
      }

      .delete-icon {
        position: absolute;
        top: 50%;
        right: 10px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        cursor: pointer;
        opacity: 0;
        transition: all 0.2s ease;
        color: #fff;
        transform: translateY(-50%);
        @include flex(row, center, center, nowrap);
        background-color: rgba(0, 0, 0, 0.2);

        &:hover {
          transform: translateY(-50%) scale(1.2);
          background-color: rgba(0, 0, 0, 0.4);
          color: #fff;
        }

        .el-icon {
          font-size: var(--font-size-small);
          font-weight: bold;
        }
      }
    }

    @media screen and (max-width: 1440px) {
      .cell {
        height: 34px;
        line-height: 34px;
      }
    }
  }
}

@keyframes borderDash {
  to {
    background-position: 100% 100%;
  }
}
</style>
