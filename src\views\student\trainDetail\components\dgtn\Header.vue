<template>
  <div class="header">
    <div class="left flex items-center">
      <div class="flex items-center">
        <img class="train-logo" src="@/assets/images/icon/icon-train-1.png" alt="">
        <span class="train-name">{{ trainName }}</span>
      </div>
      <div v-if="multipleTask" class="task-num">
        共计 {{ taskNum }}个任务/完成 {{ finishNum }}个任务
      </div>
    </div>
    <div class="right">
      <div v-if="taskDetail?.questionId" class="task-name" :title="taskDetail.questionName">
        {{ taskDetail.questionName }}
        (<template v-if="taskDetail.ifFinish">
          {{ taskDetail.stuScore }}分/
        </template>
        {{ taskDetail.questionScore }} 分)
      </div>
      <div v-else></div>
      <div class="flex items-center">
        <template v-if="taskDetail?.questionId">
          <el-button v-if="taskDetail?.showAnswer" type="success" plain @click="visibleAnswer = true">查看答案</el-button>
          <el-button v-if="taskDetail?.taskNote" type="primary" plain @click="handleShowDesc(1)">案例资料</el-button>
          <el-button v-if="taskDetail?.taskDesc" type="primary" plain @click="handleShowDesc(2)">{{
            taskDetail?.questionType
              === 7 ? '前景提要' : '任务说明'
          }}</el-button>
          <el-button v-if="taskDetail?.ninBcQuestionFileList?.length" type="primary" plain
            @click="visibleAttach = true">附件资料</el-button>
          <el-button v-if="taskDetail?.showIfReset" class="primary-btn" type="primary" @click="handleReset()"
            :disabled="disabled">重置</el-button>
          <el-button v-if="!disabled" class="primary-btn" type="primary" @click="handleSubmit()"
            :disabled="disabled">保存</el-button>
        </template>
        <div class="right-btn flex items-center justify-center">
          <el-button v-if="!disabled" class="yellow-btn" @click="handleSubmit(1)">提交</el-button>
          <div class="close-btn " @click="handleQuit" title="退出">
            <el-icon size="22" @click="handleQuit">
              <Close />
            </el-icon>
          </div>
        </div>
      </div>
    </div>
    <Answer v-model:visible="visibleAnswer" :taskDetail="taskDetail"></Answer>
    <!-- 前景提要和任务说明,战略地图绘制和战略分析的叫做前景提要，其余叫做任务说明 -->
    <OutlookSummary v-model:visible="visibleOutlookSummary" :content="taskDesc" @handleHidden="handleHiddenDesc" />
    <NinDialog v-model:visible="visibleDesc" :title="descTitle" width="750px">
      <RichPreview :str="taskDesc">
      </RichPreview>
    </NinDialog>
    <AttachmentList v-model:visible="visibleAttach" :attachments="taskDetail?.ninBcQuestionFileList"></AttachmentList>
  </div>
</template>

<script setup lang="ts">
import OutlookSummary from './OutlookSummary.vue'
import RichPreview from '@/components/global/RichPreview.vue'
import NinDialog from '@/components/global/NinDialog.vue'
import Answer from '../Answer.vue'
import AttachmentList from '../AttachmentList.vue'
import { Close } from '@element-plus/icons-vue'
import { computed, ref, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import type { TaskDetailItem, TaskListItem } from '../../types'
import { decodeName } from '@/utils/tools'
import { useStore } from 'vuex';

interface Props {
  taskList: TaskListItem[]
  taskDetail: TaskDetailItem | null
  disabled?: boolean,
  multipleTask?: boolean
}

const store = useStore()
const router = useRouter()
const route = useRoute()
const emit = defineEmits(['submit', 'reset', 'hiddenDialog', 'save'])
const props = withDefaults(defineProps<Props>(), {
  taskList: () => [],
  disabled: false,
})

const trainName = computed(() => {
  return route.query.trainName ? decodeName(route.query.trainName as string) : '实训任务'
})

const platformCode = computed(() => {
  return route.query.platformCode as string
})

const taskNum = computed(() => {
  return props.taskList.reduce((total, item) => {
    return total + item.questionList.length
  }, 0)
})

const finishNum = computed(() => {
  let total = 0
  props.taskList.forEach(item => {
    item.questionList.forEach(task => {
      if (task.ifFinish) total++
    })
  })
  return total
})

const handleQuit = () => {

  window.close()
  if (platformCode.value === 'ninBusinessCanvas') {
    store.dispatch('userLogout')
    router.replace('/login')
  }
}


const visibleDesc = ref(false)
const visibleAttach = ref(false)
const visibleOutlookSummary = ref(false)
const taskDesc = ref('');
const descTitle = ref('');
const handleShowDesc = (type: number) => {
  if (type === 1) {
    descTitle.value = '案例资料'
    taskDesc.value = props.taskDetail?.taskNote || ''
    visibleDesc.value = true
  } else {
    descTitle.value = props.taskDetail?.questionType === 7 ? '前景提要' : '任务说明'
    taskDesc.value = props.taskDetail?.taskDesc || ''
    visibleOutlookSummary.value = props.taskDetail?.questionType === 7
    visibleDesc.value = props.taskDetail?.questionType !== 7
  }
}

const handleSubmit = (isAll = 0) => {
  emit('submit', isAll)
}

const visibleAnswer = ref(false)

const handleHiddenDesc = () => {
  visibleOutlookSummary.value = false
  emit('hiddenDialog', 'desc')
}

const handleReset = () => {
  emit('reset')
}

watch(() => props.taskDetail, (newVal, oldVal) => {
  if (newVal?.questionId !== oldVal?.questionId) {
    //只有战略地图绘制需要主动展示弹窗
    visibleOutlookSummary.value = props.taskDetail?.questionType === 7 && newVal?.questionHint !== 1 && !props.disabled && !!newVal?.taskDesc
    taskDesc.value = newVal?.taskDesc || ''
  }
}, { immediate: true })
</script>

<style scoped lang="scss">
.header {
  width: 100%;
  height: 48px;
  flex-shrink: 0;
  @include flex(row, space-between, center, nowrap);
  color: #313052;
  background: #fff;
  box-shadow: 0px 1px 0px 0px #aac1eb;

  .left {
    height: 100%;
    font-size: var(--font-size);
    padding: 0 26px 0 24px;
    border-right: 1px solid #dae4f5;
  }

  .train-name {
    font-weight: bold;
  }

  .center {
    margin-left: 24px;
    @include flex(row, flex-start, center, nowrap);
    gap: 16px;
  }

  .train-logo {
    width: 24px;
    height: 22px;
    margin-right: 8px;
  }

  .task-num {
    text-align: center;
    font-size: var(--font-size-small);
    line-height: 18px;
    height: 34px;
    color: #fff;
    @include flex(row, center, center, nowrap);
    padding: 0 20px;
    border-radius: 20px;
    background-image: linear-gradient(90deg,
        #507af1 0%,
        #8182ef 68%,
        #eb84d7 100%),
      linear-gradient(#333333,
        #333333);
    background-blend-mode: normal,
      normal;
    box-shadow: inset 0px 1px 2px 0px rgba(0, 0, 0, 0.56);
    margin-left: 50px;
  }

  .task-btn {
    width: 138px;
  }

  .task-name {
    font-size: var(--font-size);
    background: url('@/assets/images/icon/icon-data-1.png') no-repeat left center;
    background-size: 18px 18px;
    padding-left: 26px;
    line-height: 18px;
    @include flex(row, start, center, nowrap);
    @include text-ellipsis();
  }

  .right {
    height: 100%;
    flex: 1;
    @include flex(row, space-between, center, nowrap);
    position: relative;
    padding-left: 24px;

    &>* {
      height: 100%;
    }

    .el-button+.el-button {
      margin-left: 8px;
    }

    :deep(.el-button) {
      min-width: 80px;
      padding: 0 10px;
      height: 28px;
      border-radius: 4px;

      &.primary-btn {
        background-image: linear-gradient(0deg,
            #4b5ee7 0%,
            #6075f3 50%,
            #758cfe 99%),
          linear-gradient(#ffffff,
            #ffffff);
        background-blend-mode: normal,
          normal;
        box-shadow: 0px -1px 0px 0px #667ef9,
          0px 1px 0px 0px #3446c8;
        border: 0;

        &:hover:not(.is-disabled) {
          background-image: linear-gradient(0deg,
              #4b5ee7 0%,
              #6075f3 50%,
              #4b5ee7 99%),
            linear-gradient(#ffffff,
              #ffffff);
        }
      }

      &.el-button--success.is-plain {
        background-color: #fff;
        border-color: #30c1a1;
        color: #0bb694;

        &:hover:not(.is-disabled) {
          background-color: #0bb694;
          color: #fff;
        }
      }

      &.el-button--primary.is-plain {
        background-color: #fff;
        border-color: #738afd;
        color: #5064ea;

        &:hover:not(.is-disabled) {
          background-color: #5064ea;
          color: #fff;
        }
      }
    }
  }

  .right-btn {
    height: 100%;
    @include flex(row, center, center, nowrap);
    border-left: 1px solid #dae4f5;
    padding: 0 12px;
    margin-left: 12px;
    gap: 8px;
  }



  .yellow-btn {
    width: 80px;
    height: 28px;
    background-image: linear-gradient(0deg,
        #ff704b 0%,
        #ff9b6d 50%,
        #ffc68e 99%),
      linear-gradient(#ffffff,
        #ffffff);
    background-blend-mode: normal,
      normal;
    box-shadow: 0px -1px 0px 0px #ffb07d,
      0px 1px 0px 0px #d3513c;
    border-radius: 4px;
    color: #fff;
    border: 0;

    &:hover {
      color: #fff;
      background-image: linear-gradient(0deg,
          #ff704b 0%,
          #ff9b6d 50%,
          #ff704b 99%),
        linear-gradient(#ffffff,
          #ffffff);
    }
  }

  .close-btn {
    color: #313052;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;

    &:hover {
      background: #dae4f5;
    }
  }

  @media screen and (max-width: 1440px) {
    .left {
      padding: 0 16px;

      .task-num {
        margin-left: 16px;
        height: 30px;
      }
    }

    .right {
      padding-left: 14px;

      .el-button {
        height: 28px;
      }

      .primary-btn,
      .yellow-btn {
        width: 72px;
      }
    }

    .right-btn {
      padding-left: 14px;
      margin-left: 14px;
    }

    .el-button {
      height: 28px;
      width: 84px;
    }
  }
}
</style>
