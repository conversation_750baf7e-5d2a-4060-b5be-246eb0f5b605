body,
dl,
dt,
dd,
ul,
ol,
li,
pre,
form,
fieldset,
input,
figure,
p,
blockquote,
th,
td {
    font-weight: 400;
    margin: 0;
    padding: 0;
    list-style: none;
}

h1,
h2,
h3,
h4,
h4,
h5 {
    margin: 0;
    padding: 0;
}

html {
    height: 100%;
    box-sizing: border-box;
}

body {
    height: 100%;
    -moz-osx-font-smoothing: grayscale;
    -webkit-font-smoothing: antialiased;
    text-rendering: optimizeLegibility;
    font-family: "Microsoft YaHei", "Helvetica Neue", "Helvetica", "PingFang SC", "Hiragino Sans GB",
        "Arial, sans-serif";
    background-color: #eff4fe;
}

label {
    font-weight: 700;
}

html,
body {
    scroll-behavior: smooth;
}

body {
    overflow: overlay;
}

*,
*:before,
*:after {
    box-sizing: inherit;
}

a:focus,
a:active {
    outline: none;
}

a,
a:focus,
a:hover {
    cursor: pointer;
    color: inherit;
    text-decoration: none;
}

:focus {
    outline: none;
}

p {
    word-wrap: break-word;
    word-break: break-all;
}
