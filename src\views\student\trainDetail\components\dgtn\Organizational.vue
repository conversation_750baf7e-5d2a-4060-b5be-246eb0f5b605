<template>
  <div class="organizational ">
    <div class="options-wrapper">
      <OptionList :options="options" :usedOptionIds="usedOptionIds" :disabled="disabled"
        @drag-start="handleOptionDragStart" />
    </div>
    <div class="base-content content">
      <div class="section-title-light">
        <div class="title-label">{{ taskDetail.questionName }}</div>
      </div>
      <div class="content-wrapper ">
        <div class="graph" ref="graphRef"></div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import OptionList from './OptionList.vue'
import { onMounted, ref, nextTick, onBeforeUnmount, watch, computed } from 'vue'
import { Graph } from '@antv/x6'
import { Dnd } from '@antv/x6-plugin-dnd'
import { registerNode, unregisterNode } from "@/components/flow/shapeConfig/registerNode";
import { registerNodeTools, unregisterNodeTools } from "@/components/flow/tools/registerNodeTools";
import type { OptionItem } from '@/views/admin/types'
import type { TaskDetailItem } from '../../types'
import type { Cell } from '@antv/x6'
interface IProps {
  taskDetail: TaskDetailItem,
  disabled?: boolean,
  mode?: 'viewAnswer' | 'answer' // viewAnswers：查看答案模式，answer：答题模式
}

const props = withDefaults(defineProps<IProps>(), {
  disabled: false,
  mode: 'answer'
})

const usedOptionName = ref<Set<string>>(new Set())
const optionsMap = computed(() => {
  return options.value.reduce((acc, option) => {
    acc[option.name] = option.id
    return acc
  }, {} as Record<string, string>)
})
const usedOptionIds = computed(() => {
  return new Set(Array.from(usedOptionName.value).map((name) => optionsMap.value[name]))
})
const options = ref<OptionItem[]>([])

const graphRef = ref<HTMLDivElement | null>()
const graph = ref<Graph>()
const dnd = ref<Dnd>();

const initGraph = async () => {
  await nextTick();
  if (!graphRef.value) return
  graph.value = new Graph({
    container: graphRef.value,
    connecting: {
      snap: {
        radius: 20,
      },
      allowBlank: false,
      allowLoop: false,
      highlight: false,
      sourceAnchor: {
        name: "center",
      },
      targetAnchor: "center",
      connectionPoint: "anchor",
      router: "manhattan",
      validateMagnet() {
        return false
      },
      validateConnection() {
        return false
      },
    },
    panning: {
      enabled: true,
    },
    interacting: {
      nodeMovable: false, //节点拖动
    },
    embedding: {
      enabled: true,
      frontOnly: true,
      findParent({ node }) {
        const bbox = node.getBBox()
        return this.getNodes().filter((node) => {
          if (!node.data?.isPreSet) {
            const targetBBox = node.getBBox()
            return bbox.isIntersectWithRect(targetBBox)
          }
          return false
        })
      }
    }
  });
  dnd.value = new Dnd({
    target: graph.value,
    validateNode() {
      return true
    },
  });
  resigterEvent()
  injectData()
}

initGraph()

const onDelete = (data: any) => {
  try {
    const cell = data.cell as Cell
    usedOptionName.value.delete(cell.data.name)
    cell?.setData({
      name: ''
    })
    cell?.setAttrs({
      label: {
        text: ' ',
      },
      title: {
        text: ' '
      }
    })
    cell?.removeTools()
  } catch (error) {
    console.error(error)
  }
}

const transformStyle = (cell: any) => {
  if (cell.shape.indexOf('edge') > -1) {
    cell.attrs.line.stroke = '#6a7ef6'
  }
}

const handleInitData = () => {
  if (props.taskDetail.initContent) {
    const initData = JSON.parse(props.taskDetail.initContent)
    console.log(initData)
    initData.forEach((node: any) => {
      transformStyle(node)
      if (node.shape.indexOf('edge') === -1 && !node.data?.isPreSet) {
        node.data.name = ''
        node.attrs.text.text = ''
        node.attrs.title.text = ''
        node.tools = {}
      }
    })
    return initData
  }
  return []
}

const handleStuAnswer = () => {
  if (props.taskDetail.stuAnswer) {
    const stuAnswer = JSON.parse(props.taskDetail.stuAnswer)
    stuAnswer.forEach((node: any) => {
      transformStyle(node)
      if (node.shape.indexOf('edge') === -1) {
        console.log(node)
        if (!node.data.name) {
          if (!node.attrs.title) {
            node.attrs.title = {
              text: ''
            }
          }
          if (!node.attrs.label) {
            node.attrs.label = {
              text: ''
            }
          }
          node.attrs.title.text = ''
          node.attrs.label.text = ''
          node.tools = {}
        } else {
          // console.log(node)
          //如果已拖拽进去的，收集已使用options
          usedOptionName.value.add(node.data.name)
          if (!props.disabled && !node.data.isPreSet) {
            node.tools = {
              items: [{
                name: 'cus-button-remove',
                args: {
                  fill: '#666',
                  x: '100%',
                  y: 0,
                  offset: {
                    x: 5,
                    y: 0
                  },
                  onClick: onDelete
                }
              }]
            }
          }
        }

      }

      // 线和没有拖拽进去的节点，禁止模式下，把tools
      if (node.shape.indexOf('edge') > -1 || node.data?.isPreSet || (node.shape.indexOf('edge') === -1 && !node.data.name) || props.disabled) {
        node.tools = {}
      }
    })
    return stuAnswer
  }
  return []
}
const injectData = () => {
  if (!graph.value) return
  const data = props.taskDetail.stuAnswer ? handleStuAnswer() : handleInitData()
  graph.value.fromJSON(data)
  setTimeout(() => {
    const ports = document.querySelectorAll(
      `g .x6-port`
    );
    ports.forEach((port) => {
      (port as SVGElement).style.visibility = "hidden";
    });
  }, 100)
  graph.value.centerContent()
}

// 注册事件
const resigterEvent = () => {
  if (!graph.value) return
  graph.value.on('node:added', ({ node }) => {
    setTimeout(() => {
      if (!node.parent) {
        graph.value!.removeNode(node)
      }
    });
  })

  graph.value.on('node:embedded', async ({ node, currentParent }) => {
    try {
      node.remove()
      //如果父节点原本已经放置了子节点，直接把子节点删除掉
      if (currentParent?.data?.name) {
        usedOptionName.value.delete(currentParent.data.name)
      }

      //作为子节点直接把name放入父节点中，然后再把子节点删除掉
      currentParent?.setAttrs({
        label: {
          text: node.data.name
        },
        title: {
          text: node.data.name
        }
      })
      currentParent?.setData({
        name: node.data.name,
        id: node.data.id
      })
      currentParent?.addTools({
        name: 'cus-button-remove',
        args: {
          fill: '#666',
          x: '100%',
          y: 0,
          offset: {
            x: 5,
            y: 0
          },
          onClick: onDelete
        }
      })

      usedOptionName.value.add(node.data.name)
    } catch (error) {
      node.remove()
      console.error(error)
    }

  });
}

const handleOptionDragStart = (item: OptionItem, event: DragEvent) => {
  const node = graph.value!.createNode({
    id: item.id, shape: 'general-node',
    ports: {
      items: [{
        group: 'port_g',
        id: 'p_top'
      },
      {
        group: 'port_g',
        id: 'p_right'
      }],
      groups: {
        port_g: {
          attrs: {
            circle: {
              r: 4,
              magnet: true,
              strokeWidth: 1,
              fill: '#fff'
            }
          },
          position: 'ellipseSpread'
        }
      }
    },
    tools: [{
      name: 'cus-button-remove', // 工具名称
      args: {
        x: 130,
        y: 4,
        offset: {
          x: -5,
          y: -5
        }
      }
    }],
  });
  node.attrs!.label.text = item.name;
  node.attrs!.title.text = item.name;
  node.setData({
    name: item.name,
    id: item.id
  });
  dnd.value?.start(node, event);
}


const getStuAnswer = () => {
  return Promise.resolve(JSON.stringify(graph.value!.toJSON().cells))
}

defineExpose({
  getStuAnswer
})

onMounted(() => {
  registerNode(Graph);
  registerNodeTools(Graph);
  options.value = props.taskDetail.taskOptions ? JSON.parse(props.taskDetail.taskOptions) : []
})

onBeforeUnmount(() => {
  // todo 查看答案模式下不卸载
  if (props.mode !== 'viewAnswer') {
    unregisterNode(Graph)
    unregisterNodeTools(Graph)
  }
})


//为了提交之后，更新disabled状态
watch(() => props.disabled, (newVal) => {
  //如果禁止了，要重新更新一下画布
  if (newVal) {
    graph.value?.clearCells()
    injectData()
  }
})

</script>

<style scoped lang="scss">
.organizational {
  @include flex(row, flex-start, flex-start, nowrap);
  height: 100%;
  gap: 16px;

  .base-content {
    height: 100%;
    @include flex(column, flex-start, flex-start, nowrap);
    position: relative;

    .content-wrapper {
      width: 100%;
      flex: 1;
      min-height: 0;
      padding: 20px;
      background: url('@/assets/images/train/bg-light-task.png') no-repeat bottom center;
      background-size: 100% auto;
    }
  }

  .options-wrapper {
    width: 240px;
    height: 100%;
  }

  .content {
    min-width: 0;
    flex: 1;
    background-image: linear-gradient(to bottom, #fff 0%, rgba(255, 255, 255, 0.5) 100%);
    box-shadow: 0px 2px 4px 0px rgba(120, 147, 187, 0.15);
    border-radius: 6px;
    border: solid 1px #ffffff;
    overflow: hidden;
  }

  .graph {
    height: 100%;
  }

  @media screen and (max-width: 1440px) {
    .options-wrapper {
      width: 200px;
    }
  }
}
</style>
