! function (e) {
    const t = e["zh-cn"] = e["zh-cn"] || {};
    t.dictionary = Object.assign(t.dictionary || {}, {
        "%0 of %1": "第 %0 步，共 %1 步",
        "Block quote": "块引用",
        Bold: "加粗",
        "Bulleted List": "项目符号列表",
        Cancel: "取消",
        "Cannot upload file:": "无法上传的文件：",
        "Centered image": "图片居中",
        "Change image text alternative": "更改图片替换文本",
        "Choose heading": "标题类型",
        Column: "列",
        "Could not insert image at the current position.": "无法在当前位置插入图片",
        "Could not obtain resized image URL.": "无法获取重设大小的图片URL",
        "Decrease indent": "减少缩进",
        "Delete column": "删除本列",
        "Delete row": "删除本行",
        Downloadable: "可下载",
        "Dropdown toolbar": "下拉工具栏",
        "Edit link": "修改链接",
        "Editor toolbar": "编辑器工具栏",
        "Enter image caption": "输入图片标题",
        "Full size image": "图片通栏显示",
        "Header column": "标题列",
        "Header row": "标题行",
        Heading: "标题",
        "Heading 1": "标题 1",
        "Heading 2": "标题 2",
        "Heading 3": "标题 3",
        "Heading 4": "标题 4",
        "Heading 5": "标题 5",
        "Heading 6": "标题 6",
        "Image toolbar": "图片工具栏",
        "image widget": "图像小部件",
        "Increase indent": "增加缩进",
        "Insert column left": "左侧插入列",
        "Insert column right": "右侧插入列",
        "Insert image": "插入图像",
        "Insert image or file": "插入图片或文件",
        "Insert media": "插入媒体",
        "Insert paragraph after block": "在后面插入段落",
        "Insert paragraph before block": "在前面插入段落",
        "Insert row above": "在上面插入一行",
        "Insert row below": "在下面插入一行",
        "Insert table": "插入表格",
        "Inserting image failed": "插入图片失败",
        Italic: "倾斜",
        "Left aligned image": "图片左侧对齐",
        Link: "超链接",
        "Link URL": "链接网址",
        "Media URL": "媒体URL",
        "media widget": "媒体小部件",
        "Merge cell down": "向下合并单元格",
        "Merge cell left": "向左合并单元格",
        "Merge cell right": "向右合并单元格",
        "Merge cell up": "向上合并单元格",
        "Merge cells": "合并单元格",
        Next: "下一步",
        "Numbered List": "项目编号列表",
        "Open in a new tab": "在新标签页中打开",
        "Open link in new tab": "在新标签页中打开链接",
        Paragraph: "段落",
        "Paste the media URL in the input.": "在输入中粘贴媒体URL",
        Previous: "上一步",
        Redo: "重做",
        "Rich Text Editor": "富文本编辑器",
        "Rich Text Editor, %0": "富文本编辑器， %0",
        "Right aligned image": "图片右侧对齐",
        Row: "行",
        Save: "保存",
        "Select all": "全选",
        "Select column": "选择列",
        "Select row": "选择行",
        "Selecting resized image failed": "选择重设大小的图片失败",
        "Show more items": "显示更多",
        "Side image": "图片侧边显示",
        "Split cell horizontally": "水平分割单元格",
        "Split cell vertically": "垂直拆分单元格",
        "Table toolbar": "表格工具栏",
        "Text alternative": "替换文本",
        "The URL must not be empty.": "URL不可以为空。",
        "This link has no URL": "此链接没有设置网址",
        "This media URL is not supported.": "不支持此媒体URL。",
        "Tip: Paste the URL into the content to embed faster.": "提示：将URL粘贴到内容中可更快地嵌入",
        Undo: "撤销",
        Unlink: "取消超链接",
        "Upload failed": "上传失败",
        "Upload in progress": "正在上传",
        "Widget toolbar": "小部件工具栏",
        "Font Color": "字体颜色",
        "Font Background Color": "背景色",
        "Text alignment": "对齐方式",
        "Underline": "下划线",
        "Strikethrough": "删除线",
        "Black": "黑色",
        "Dim grey": "暗灰色",
        "Grey": "灰色",
        "Light grey": "浅灰色",
        "Red": "红色",
        "Orange": "橙色",
        "Yellow": "黄色",
        "Light green": "浅绿色",
        "Green": "绿色",
        "Aquamarine": "海蓝色",
        "Light blue": "淡蓝色",
        "Turquoise": "青绿色",
        "Blue": "蓝色",
        "Purple": "紫色",
        "White": "白色",
        "Remove color": "清除颜色",
        "Align left": "左对齐",
        "Align right": "右对齐",
        "Align center": "居中",
        "Justify": "两端对齐",
        "In line":"行内显示"
    }), t.getPluralForm = function (e) {
        return 0
    }
}(window.CKEDITOR_TRANSLATIONS || (window.CKEDITOR_TRANSLATIONS = {}));