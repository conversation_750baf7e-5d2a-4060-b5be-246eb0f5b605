<template>
  <div class="business-flow">
    <div class="business-flow-left">
      <div class="sub-title">流程图设计</div>
      <div class="flow-shapes">
        <ShapeItem v-for="item in flowShapes" :key="item.type" :item="item" :draggable="true"
          @dragstart="(e) => handleShapeDragStart(e, item)" />
      </div>
    </div>
    <div class="business-flow-main">
      <div class="graph-wrapper">
        <div class="graph" ref="graphRef"></div>
      </div>
      <GraphTools @undo="handleUndo" @redo="handleRedo" @zoom-out="handleZoomOut" @zoom-in="handleZoomIn"
        @reset-zoom="handleResetZoom" />
    </div>
    <PropertyPanel mode="admin" :enterpriseId="enterpriseId" :visible="visible" :node="(currentNode! as Cell)"
      @close="handleClosePanel">
    </PropertyPanel>
  </div>
</template>

<script setup lang="ts">
import GraphTools from '@/components/flow/components/GraphTools.vue'
import ShapeItem from '@/components/flow/components/ShapeItem.vue'
import PropertyPanel from '@/components/flow/components/PropertyPanel.vue'
import { ElMessage } from 'element-plus'
import { onMounted } from 'vue'
import { useFlowDesign } from '@/components/flow/hooks/useFlowDesign'
import type { Cell } from '@antv/x6'
import { flowShapes } from '@/components/flow/config/constant'

const props = defineProps({
  initContent: {
    type: String,
    default: ''
  },
  taskOptions: {
    type: String,
    default: ''
  },
  enterpriseId: {
    type: String,
    default: ''
  }
})

const {
  graphRef,
  currentNode,
  visible,
  handleShapeDragStart,
  handleClosePanel,
  getGraphData,
  initGraph,
  injectData,
  handleUndo,
  handleRedo,
  handleZoomOut,
  handleZoomIn,
  handleResetZoom
} = useFlowDesign({
  mode: 'admin',
})

onMounted(() => {
  initGraph({
    background: {
      color: "#ffffff", // 设置画布背景颜色
    },
    grid: {
      size: 10,
      visible: true,
      type: 'doubleMesh',
      args: [
        {
          color: '#eee',
          thickness: 1
        },
        {
          color: '#eee',
          thickness: 1,
          factor: 4
        }
      ]
    },
  })
  injectData(props.initContent)
})


const validate = () => {
  return new Promise((resolve, reject) => {
    getGraphData().then((data: string) => {
      const cells = JSON.parse(data)
      if (!cells?.length) {
        ElMessage.error('流程设计不能为空')
        reject()
      }
      resolve({
        initContent: JSON.stringify(cells),
      })
    })
  })
}

defineExpose({
  validate
})
</script>

<style scoped lang="scss">
.business-flow {
  @include flex(row, flex-start, flex-start, nowrap);
  gap: 20px;
  position: relative;
  height: 650px;
  overflow: hidden;

  .business-flow-left {
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    padding: 14px;
    flex-shrink: 0;
  }

  .sub-title {
    font-size: 14px;
  }

  .business-flow-main {
    height: 650px;
    flex: 1;
    min-width: 0;
    @include flex(column, flex-start, stretch, nowrap);
    gap: 16px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    position: relative;
  }

  .graph-wrapper {
    min-height: 0;
    flex: 1;

    .graph {
      height: 100%;
    }
  }
}

.flow-shapes {
  @include flex(column, flex-start, stretch, nowrap);
  gap: 32px;
  margin-top: 12px;
}

:deep(.el-dialog__body) {
  padding-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

:deep {
  .x6-widget-transform {
    margin: -1px 0 0 -1px;
    padding: 0px;
    border: 1px solid #239edd;
  }

  .x6-widget-transform>div {
    border: 1px solid #239edd;
  }

  .x6-widget-transform>div:hover {
    background-color: #3dafe4;
  }

  .x6-widget-transform-active-handle {
    background-color: #3dafe4;
  }

  .x6-widget-transform-resize {
    border-radius: 0;
  }

  .x6-widget-selection-inner {
    border: 1px solid #239edd;
  }

  .x6-widget-selection-box {
    opacity: 0;
  }
}
</style>
<style>
@keyframes ant-line {
  to {
    stroke-dashoffset: -1000
  }
}
</style>
