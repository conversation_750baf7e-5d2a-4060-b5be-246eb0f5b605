<template>
  <div class="login-wrap">
    <h2 class="login-title">{{ settings.title }}</h2>
    <el-form ref="ruleFormRef" style="max-width: 450px" :model="ruleForm" :rules="rules" label-width="auto"
      label-position="top" class="demo-ruleForm" size="large">
      <p class="login-welcome">欢迎您到来做题端</p>
      <el-form-item label="账号：" prop="username">
        <el-input v-model="ruleForm.username" placeholder="请输入账号" />
      </el-form-item>
      <el-form-item label="密码：" prop="password">
        <el-input v-model="ruleForm.password" type="password" placeholder="请输入密码" autocomplete="new-password" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="submitForm(ruleFormRef)" :loading="loading" class="login-button">
          登录
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import settings from '@/settings'
import * as api from '@/api/login'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus';
import { reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { md5 } from "js-md5";
import { randomString } from '@/utils/tools'
import { Base64 } from "js-base64";

const router = useRouter()
const ruleFormRef = ref<FormInstance>()
const ruleForm = ref({
  username: '',
  password: '',
})

const loading = ref(false)
const rules = reactive<FormRules>({
  username: [{ required: true, message: "请输入账号", trigger: "blur" }],
  password: [
    { required: true, message: "请输入密码", trigger: "blur", },
    { min: 6, max: 16, message: "密码长度不能少于6位", trigger: "blur" },
  ],
})

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid) => {

    if (valid) {
      loading.value = true
      const obj = {
        type: 2,
        userLogin: ruleForm.value.username.trim(),
        userPwd: md5(ruleForm.value.password.trim()),
      };
      console.log(ruleForm.value.username.trim())
      const info =
        randomString(3) +
        Base64.encode(JSON.stringify(obj)) +
        randomString(3);
      api.login({ info }).then(res => {
        if (res.data.code === 200) {
          const { token, userInfo } = res.data.data
          if (token) {
            let redirect = ''
            if (userInfo.userType === 3) {
              redirect = '/student/trainDetail'
            } else {
              redirect = '/admin/questionList'
            }
            const queryParams: { 
              access_token: string;
              redirect: string;
              trainId: any;
              platformCode: any;
              viewAnswer: number;
              ifdesign?: number;
            } = {
              access_token: token,
              redirect,
              trainId: userInfo.trainId,
              platformCode: userInfo.platformCode,
              viewAnswer: 6,
              ifdesign : 1
            };
            router.replace({
              path: '/apiLogin',
              query: queryParams
            })
          }
        } else {
          ElMessage.error(res.data.msg)
        }
      }).finally(() => {
        loading.value = false
      })
    }
  })
}
</script>

<style scoped lang="scss">
$uiWidth: 1440;
$uiHeight: 900;

//计算属性
@mixin clamp($key, $min, $max) {
  #{$key}: clamp(#{$min}px, calc(100vh * (#{$max} / #{$uiHeight})), #{$max}px)
}

@mixin clampW($key, $min, $max) {
  #{$key}: clamp(#{$min}px, calc(100vw * (#{$max} / #{$uiWidth})), #{$max}px)
}

.login-wrap {
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  height: 100vh;
  display: flex;
  align-items: center;
  background: url('@/assets/images/bg.png') no-repeat center;
  background-size: cover;
  @include clamp(padding-top, 60, 120);

  .login-title {
    @include clamp(font-size, 24, 44);
    color: #333;
    @include clamp(margin-bottom, 2, 4);
    @include clamp(margin-bottom, 20, 32);
    color: var(--el-color-primary);
  }

  .login-desc {
    @include clamp(font-size, 12, 14);
    color: #333;
    @include clamp(margin-bottom, 20, 32);
    color: var(--el-color-primary);
  }

  .login-button {
    width: 100%;
    @include clamp(height, 30, 44);
    @include clamp(margin-top, 30, 46);
    border-radius: 4px;
    color: #fff;
    background: var(--el-color-primary);
  }

  .login-welcome {
    font-weight: 600;
    @include clamp(font-size, 12, 24);
    @include clamp(margin-bottom, 10, 22);
    color: var(--el-color-primary);
  }

  .demo-ruleForm {
    @include clamp(width, 424, 424);
    @include clamp(padding, 10, 32);
    flex-shrink: 0;
    border-radius: 4px;
    background: #FFF;
    box-shadow: 0 0 16px 0 var(--el-color-primary-light-7);
    backdrop-filter: blur(8px);
  }
}

.code-row {
  width: 100%;
  display: flex;
  gap: 10px;
  align-items: center;

  .img {
    height: 38px;
    cursor: pointer;
  }

  .el-col {
    &:last-child {
      text-align: right;
    }
  }
}
</style>
