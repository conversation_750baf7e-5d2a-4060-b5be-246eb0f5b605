.teacher-wrap {
    display: flex;
    flex: 1;
    height: 100%;
    overflow: auto;
    flex-direction: column;
    background-color: #fff;

    .teacher-head {
        border-bottom: 1px solid #ddd;
        line-height: 55px;
        padding: 0 10px;

        .name {

            display: inline-block;
            line-height: 52px;
            padding: 0 10px;
            position: relative;

            &:after {
                position: absolute;
                left: 0;
                right: 0;
                bottom: -4px;
                content: "";
                border-bottom: 3px solid var(--el-color-primary);
            }
        }

    }

    .teacher-tabs {
        border-bottom: 1px solid #ddd;
        line-height: 55px;
        padding: 0 10px;

        li {
            display: inline-block;
            margin-right: 30px;
            padding: 0 10px;
            cursor: pointer;

            &.actived {
                border-bottom: 3px solid var(--el-color-primary);
            }
        }
    }

    .teacher-sub-nav {
        border-bottom: 1px solid #ededed;
        line-height: 55px;
        padding: 10px 20px;

        .line {
            display: inline-block;
            margin: 0 20px
        }
    }

    .teacher-main {
        padding: 20px;
        // display: flex;
        flex: 1;
        flex-direction: column;
        overflow: auto;

        .head-search {
            .el-select {
                &.mini {
                    width: 120px;
                }
            }

            .el-input {
                &.mini {
                    width: 120px;
                }
            }
        }

        .head-operation {
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
        }

        .table-flex {
            flex: 1;
            overflow: auto;
        }

        .el-table {
            .el-link {
                margin-right: 10px;
            }
        }
    }
}
