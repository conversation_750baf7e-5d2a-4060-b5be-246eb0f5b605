<template>
  <el-dialog class="cus-dialog-dgth" :model-value="visible" title="选择" width="700px" :close-on-click-modal="false"
    align-center :before-close="close">
    <template #header>
      <div class="section-title-light">
        <div class="title-label">选择</div>
      </div>
    </template>
    <div class="tree-container">
      <el-tree :data="treeData" :props="defaultProps" node-key="basicId" highlight-current :default-expand-all="true"
        class="custom-tree">
        <template #default="{ node, data }">
          <div class="custom-tree-node">
            <div class="node-content">
              <span class="node-label">{{ node.label }}</span>
            </div>
            <div class="node-actions" @click.stop>
              <el-checkbox v-model="checkeds[data.basicId]" />
            </div>
          </div>
        </template>
      </el-tree>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="close">取消</el-button>
        <el-button color="#507af1" type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { ref, defineProps, defineEmits, watch } from 'vue'
import type { PropType } from 'vue'
const props = defineProps({
  visible: { type: Boolean, default: false },
  treeData: { type: Array, default: () => [] },
  checkedList: {
    type: Array as PropType<string[]>,
    default: () => []
  }
})
const emits = defineEmits(['update:visible', 'change'])

const defaultProps = {
  label: 'basicName',
  children: 'children'
}


const close = () => {
  emits('update:visible', false)
}

const checkeds = ref<{ [key: string]: boolean }>({})

const handleConfirm = () => {
  const values = Object.keys(checkeds.value).filter(key => checkeds.value[key])
  if (!values.length) {
    return ElMessage.warning('请选择数据')
  }
  close()
  emits('change', values)
}

watch(() => props.checkedList, () => {
  checkeds.value = props.checkedList.reduce((acc, cur) => {
    acc[cur] = true
    return acc
  }, {} as { [key: string]: boolean })
}, { immediate: true })

</script>

<style scoped lang="scss">
.tree-container {
  padding-bottom: 20px;
  padding-top: 16px;
  min-height: 400px;
  max-height: 70vh;
}

.custom-tree-node {
  @include flex(row, space-between, center, nowrap);
  width: 100%;
  padding-right: 14px;

  .node-content {
    color: #313052;
    font-size: 14px;
    padding: 0 12px 0 20px;
    background: left url('@/assets/images/icon/icon-data.png') no-repeat;
    background-size: 12px 12px;
  }

  .node-actions {
    padding: 0 4px;
  }

  .el-checkbox {
    --el-color-primary: #507af1;

    :deep(.el-checkbox__inner) {
      border-color: #507af1;
    }
  }
}

.dialog-footer {
  margin-bottom: 14px;
}
</style>
