<template>
  <div class="background">
    <img :src="src" alt="">
  </div>
</template>

<script setup lang="ts">
import bg from '@/assets/images/bg.png'
import dgtn from '@/assets/images/bg-dgtn.png'
import { computed } from 'vue'

const props = defineProps({
  type: {
    type: String,
    default: 'default'
  }
})

const src = computed(() => {
  return props.type === 'dgtn' ? dgtn : bg
})
</script>

<style scoped lang="scss">
.background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
  }
}
</style>
