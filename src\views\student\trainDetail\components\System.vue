<template>
  <div class="system">
    <div class="section-title">
      <div class="title-label">{{ taskDetail.questionName }}</div>
      <span class="title-desc">（请设置各部门的适用制度）</span>
    </div>
    <div class="system-content">
      <el-table class="system-table" :data="rowHeaders" border style="width: 100%" @cell-click="handleCellClick">
        <el-table-column prop="name" label="制度名称" width="180" align="center">
        </el-table-column>
        <el-table-column v-for="(column, columnIndex) in columnHeaders" :label="column" align="center"
          :key="columnIndex">
          <template #default="scope">
            <div class="icon-check-w">
              <img v-if="data[scope.$index]?.[columnIndex]" src="@/assets/images/icon/icon-check-w.png" alt="">
            </div>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { TaskDetailItem } from '../types'
import { ref, onMounted } from 'vue'

interface IProps {
  taskDetail: TaskDetailItem,
  disabled?: boolean
}

const props = withDefaults(defineProps<IProps>(), {
  disabled: false
})

const columnHeaders = ref<string[]>([])
const rowHeaders = ref<{ name: string; index: number }[]>([])
const data = ref<number[][]>([])


const handleCellClick = (row: any, column: any) => {
  if (props.disabled) return
  if (column.property === 'name') return
  const colIndex = column.getColumnIndex() - 1
  const rowIndex = row.index
  data.value[rowIndex][colIndex] = data.value[rowIndex][colIndex] ? 0 : 1
}

onMounted(() => {
  if (props.taskDetail?.stuAnswer) {
    const stuAnswer = JSON.parse(props.taskDetail.stuAnswer)
    columnHeaders.value = stuAnswer.columnHeaders || []
    rowHeaders.value = stuAnswer.rowHeaders.map((item: string, index: number) => {
      return { name: item, index }
    }) || []
    data.value = stuAnswer.data || Array(rowHeaders.value.length).fill(0).map(() => Array(columnHeaders.value.length).fill(0))
  }
})

const getStuAnswer = () => {
  return new Promise((resolve) => {
    const stuAnswer = {
      data: data.value,
      columnHeaders: columnHeaders.value,
      rowHeaders: rowHeaders.value.map(item => item.name)
    }
    resolve(JSON.stringify(stuAnswer))
  })
}

defineExpose({
  getStuAnswer
})
</script>

<style scoped lang="scss">
.system {
  height: 100%;
  @include flex(column, flex-start, flex-start, nowrap);
  gap: 20px;

  .system-content {
    width: 100%;
    min-height: 0;
    flex: 1;
    border: 1px solid;
    border-image: linear-gradient(180deg, #31D2FF, #002752) 1;
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    overflow-y: auto;

    :deep(.el-table) {
      color: #fff;
      background-color: rgba(255, 255, 255, 0.1);
      --el-table-header-bg-color: transparent;
      --el-table-tr-bg-color: transparent;
      --el-table-row-hover-bg-color: rgba(255, 255, 255, 0.2);
      --el-table-header-text-color: #fff;
      --el-table-border-color: #2BA0AD;

      .cell {
        font-weight: bold;
      }
    }

    .icon-check-w {
      width: 20px;
      height: 20px;
      margin: 0 auto;

      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
