<template>
    <div class="back-top">
        <div class="left-box">
            <el-button :icon="Back" type="primary" plain @click="router.back()">返回</el-button>
            <span class="line"></span>
            <span class="name" :title="name">{{ name }}</span>
        </div>
        <slot></slot>
    </div>
</template>

<script setup lang="ts">
import { decodeName } from '@/utils/tools';
import { Back } from '@element-plus/icons-vue'
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const router = useRouter(),
    route = useRoute(),
    name = computed(() => decodeName(route.query.trainName as string) || '未命名')


</script>

<style scoped lang="scss">
.back-top {
    margin-bottom: 20px;
    border-bottom: 1px solid #ededed;
    padding-bottom: 10px;
    display: flex;
    justify-content: space-between;

    .left-box {
        display: flex;
        align-items: center;
    }

    .line {
        border-right: 1px solid #ddd;
        width: 0;
        height: 15px;
        display: inline-block;
        margin: 0 10px;
    }

    .name {
        max-width: 60%;
        display: inline-block;
        overflow: hidden;
        height: 22px;
        line-height: 22px;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}
</style>
