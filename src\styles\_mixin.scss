$uiWidth: 1920;
$uiHeight: 1080;

// flex 布局混合器
@mixin flex($direction: row, $justify: flex-start, $align: stretch, $wrap: nowrap) {
  display: flex;
  flex-direction: $direction;
  justify-content: $justify;
  align-items: $align;
  flex-wrap: $wrap;
}

// 单行省略
@mixin text-ellipsis() {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 多行省略
// $line: 需要省略的行数
// $lineHeight: 行高，默认为1.2
@mixin multi-ellipsis($line: 2, $lineHeight: 1.2) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: $line;
  -webkit-box-orient: vertical;

  // 兼容 IE
  @supports (-ms-ime-align: auto) {
    display: block;
    position: relative;

    &::after {
      content: '...';
      position: absolute;
      right: 0;
      bottom: 0;
      background: linear-gradient(to right, transparent, #fff 50%);
    }
  }
}

@mixin border-1px() {
  border: 1px solid;
  border-image: linear-gradient(180deg, #31D2FF, #002752) 1;
  background: rgba(255, 255, 255, 0.1);
}

@mixin clamp($key, $min, $max) {
  #{$key}: clamp(#{$min}px, calc(100vh * (#{$max} / #{$uiHeight})), #{$max}px)
}
