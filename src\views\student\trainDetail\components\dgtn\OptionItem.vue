<template>
  <div :class="['option-item', { 'full': !empty, disabled: disabled, gray: gray }]" :style="itemStyle" :title="name"
    :draggable="!disabled && !empty" @dragstart="handleDragStart">
    <div class="option-item-bg"></div>
    <div class="option-item-text" :class="{ 'placeholder': !name }">{{ name || placeholder }}</div>
    <el-icon v-if="showDelete" :size="16" class="delete-icon" @click.stop.prevent="handleDeleteClick">
      <Close />
    </el-icon>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Close } from '@element-plus/icons-vue'

const props = withDefaults(
  defineProps<{
    name: string
    empty?: boolean
    disabled?: boolean
    gray?: boolean
    width?: number | string
    height?: number | string
    polygonSize?: number
    showDelete?: boolean
    placeholder?: string
  }>(),
  {
    empty: false,
    disabled: false,
    gray: false,
    width: 160,
    height: 36,
    polygonSize: 12,
    showDelete: false,
    placeholder: '',
  },
)

const emit = defineEmits<{
  'delete': [name: string]
}>()

const itemStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height,
  '--polygon-size': `${props.polygonSize}px`,
}))

const handleDragStart = (event: DragEvent) => {
  if (props.disabled || props.empty || props.gray) return
  if (event.dataTransfer) {
    const dragPreview = document.createElement('div')
    dragPreview.classList.add('option-drag-preview')
    dragPreview.textContent = props.name.substring(0, 2)
    document.body.appendChild(dragPreview)

    event.dataTransfer.setDragImage(dragPreview, 20, 20)

    requestAnimationFrame(() => {
      document.body.removeChild(dragPreview)
    })
  }
}

const handleDeleteClick = () => {
  emit('delete', props.name)
}
</script>

<style scoped lang="scss">
.option-item {
  min-width: 80px;
  flex-shrink: 0;
  color: #333;
  font-size: var(--font-size-small);
  cursor: pointer;
  background: linear-gradient(to right, #897dff, #3845ec);
  clip-path: polygon(0% 0%,
      calc(100% - var(--polygon-size)) 0%,
      100% var(--polygon-size),
      100% 100%,
      var(--polygon-size) 100%,
      0% calc(100% - var(--polygon-size)));
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  @include flex(row, center, center, nowrap);

  .option-item-bg {
    position: absolute;
    top: 1px;
    left: 1px;
    right: 1px;
    bottom: 1px;
    z-index: 1;
    @include flex(row, center, center, nowrap);
    $checker-size: 10px;

    background-size: $checker-size $checker-size, auto, auto;
    background-position: 50% 50%, 0 0, 0 0;
    background-blend-mode: normal, soft-light, normal;
    box-shadow: inset 0px 1px 2px 0px rgba(255, 255, 255, 0.35);
    border-style: solid;
    border-width: 1px;
    border-image-source: linear-gradient(90deg, #897dff 0%, #3845ec 100%);
    clip-path: polygon(0% 0%,
        calc(100% - var(--polygon-size)) 0%,
        100% var(--polygon-size),
        100% 100%,
        var(--polygon-size) 100%,
        0% calc(100% - var(--polygon-size)));
    background-image: repeating-conic-gradient(rgba(255, 255, 255, 0.25) 0% 25%,
        transparent 0% 50%),
      linear-gradient(0deg, #869ce8 0%, #ffffff 100%), linear-gradient(#d0d5ff, #d0d5ff);

    &::after {
      content: '';
      position: absolute;
      display: inline-block;
      left: -0;
      width: -0;
      top: -0;
      bottom: -0;
      transition: all 0.3s ease;
      background-image: repeating-conic-gradient(rgba(255, 255, 255, 0.08) 0% 25%, transparent 0% 50%),
        linear-gradient(0deg, #384ada 0%, #b787ff 100%), linear-gradient(#6476ff, #6476ff);
      background-size: $checker-size $checker-size, auto, auto;
      background-position: 50% 50%, 0 0, 0 0;
      background-blend-mode: normal, soft-light, normal;
      box-shadow: inset 0px 1px 2px 0px rgba(255, 255, 255, 0.35);
      border-style: solid;
      border-width: 1px;
      border-image-source: linear-gradient(90deg, #897dff 0%, #3845ec 100%);
      clip-path: polygon(0% 0%,
          calc(100% - var(--polygon-size)) 0%,
          100% var(--polygon-size),
          100% 100%,
          var(--polygon-size) 100%,
          0% calc(100% - var(--polygon-size)));
    }
  }

  .option-item-text {
    width: 100%;
    line-height: var(--line-height, 36px);
    position: relative;
    z-index: 2;
    padding: 0 12px;
    text-align: center;
    @include text-ellipsis();

    &.placeholder {
      color: #898989;
    }
  }

  &.highlight-drop:not(.disabled):not(.gray) {
    transform: scale(1.05);
  }

  &.full {
    background: linear-gradient(to right, #9ea4f2, #8a9de1);
    color: #fff;

    .option-item-bg {
      top: 0px;

      &::after {
        top: -1px;
        left: -1px;
        bottom: -1px;
        width: calc(100% + 2px);
      }

    }
  }

  &.gray {
    filter: grayscale(80%);
  }

  &.disabled {
    cursor: not-allowed;
  }

  .delete-icon {
    position: absolute;
    top: 50%;
    right: 4px;
    width: 20px;
    height: 20px;
    color: #fff;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
    cursor: pointer;
    z-index: 3;
    transform: translateY(-50%);
    @include flex(row, center, center, nowrap);
    opacity: 0;
    transition: opacity 0.3s ease;

    &:hover {
      background-color: rgba(0, 0, 0, 0.5);
    }
  }

  &:hover .delete-icon {
    opacity: 1;
  }
}
</style>

<style lang="scss">
.option-drag-preview {
  position: fixed;
  top: -9999px;
  left: -9999px;
  z-index: 9999;
  pointer-events: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #4a69ff;
  color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  font-weight: 700;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.25);
}
</style>
