<template>
  <div class="key-point-control">
    <OptionList v-model:options="options" :used-option-ids="usedOptionIds" />
    <div class="control-right">
      <div class="right-header">
        <span>战略地图绘制</span>
        <span class="tip-text">请先设置选项后再拖拽到战略地图中</span>
      </div>
      <div class="map-wrapper">
        <!-- 主要名称标题区域 -->
        <div class="main-title-area">
          <div class="main-title-content" @drop="handleMainTitleDrop($event)" @dragover.prevent
            @dragenter="handleDragEnter($event)" @dragleave="handleDragLeave($event)">
            <div v-if="!mainTitle" class="title-placeholder">
              请将维度拖动到此区域
            </div>
            <div v-else class="main-title-item" draggable="true" @dragstart="handleMainTitleDragStart($event)">
              {{ mainTitle }}
              <div class="delete-icon" @click.stop="handleDeleteMainTitle()">
                <el-icon>
                  <Close />
                </el-icon>
              </div>
            </div>
          </div>
        </div>

        <!-- 列表区域 -->
        <div class="columns-area">
          <div class="column-item" v-for="(column, columnIndex) in columnList" :key="columnIndex">
            <el-button type="danger" size="small" class="delete-column" :icon="Delete"
              @click="handleDeleteColumn(columnIndex)">
            </el-button>

            <div class="column-content">
              <!-- 左侧主要标题 -->
              <div class="column-title">
                <div class="title-drop-area" @drop="handleColumnTitleDrop($event, columnIndex)" @dragover.prevent
                  @dragenter="handleDragEnter($event)" @dragleave="handleDragLeave($event)">
                  <div v-if="!column.title" class="title-placeholder">
                    目标
                  </div>
                  <div v-else class="title-item" draggable="true"
                    @dragstart="handleColumnTitleDragStart($event, column.title)">
                    {{ column.title }}
                    <div class="delete-icon" @click.stop="handleDeleteColumnTitle(columnIndex)">
                      <el-icon>
                        <Close />
                      </el-icon>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 右侧内容区域 -->
              <div class="column-content-area">
                <div class="content-drop-area" @drop="handleContentDrop($event, columnIndex)" @dragover.prevent
                  @dragenter="handleDragEnter($event)" @dragleave="handleDragLeave($event)">
                  <div v-if="column.contents.length === 0" class="content-placeholder">
                    请将内容拖动到此区域
                  </div>
                  <div v-else class="content-items">
                    <div v-for="(content, contentIndex) in column.contents" :key="contentIndex" class="content-item"
                      draggable="true" @dragstart="handleContentDragStart($event, content)">
                      {{ content }}
                      <div class="delete-icon" @click.stop="handleDeleteContent(columnIndex, contentIndex)">
                        <el-icon>
                          <Close />
                        </el-icon>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <el-divider />
          </div>

          <!-- 添加新列 -->
          <div class="add-column">
            <el-button type="primary" size="small" :icon="Plus" @click="handleAddColumn()">
              添加新列
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Delete, Plus, Close } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import OptionList from './OptionList.vue'
import type { OptionItem } from '../../types'

const props = defineProps({
  initContent: {
    type: String,
    default: ''
  },
  taskOptions: {
    type: String,
    default: ''
  }
})

interface ColumnItem {
  title: string
  contents: string[]
}

interface StrategyMapData {
  mainTitle: string
  columns: ColumnItem[]
}

const options = ref<OptionItem[]>([])
const mainTitle = ref('')
const columnList = ref<ColumnItem[]>([])

const optionsMap = computed(() => {
  const map = new Map<string, string>()
  options.value.forEach(item => {
    map.set(item.name, item.id)
  })
  return map
})

// 计算已使用的选项名称集合
const usedOptionIds = computed(() => {
  const names = new Set<string>()

  // 主标题
  if (mainTitle.value) {
    const id = optionsMap.value.get(mainTitle.value)
    if (id) names.add(id)
  }

  // 列标题和内容
  columnList.value.forEach(column => {
    if (column.title) {
      const id = optionsMap.value.get(column.title)
      if (id) names.add(id)
    }
    column.contents.forEach(content => {
      const id = optionsMap.value.get(content)
      if (id) names.add(id)
    })
  })

  return names
})

// 处理拖拽进入
const handleDragEnter = (event: DragEvent) => {
  const cell = event.currentTarget as HTMLElement
  cell.classList.add('highlight-drop')
}

// 处理拖拽离开
const handleDragLeave = (event: DragEvent) => {
  const cell = event.currentTarget as HTMLElement
  cell.classList.remove('highlight-drop')
}

// 主标题拖拽处理
const handleMainTitleDrop = (event: DragEvent) => {
  const data = event.dataTransfer?.getData('text/plain')
  if (!data) return

  const option = JSON.parse(data) as OptionItem

  // 移除其他位置的相同选项
  removeOptionFromOtherPlaces(option.name)

  mainTitle.value = option.name

  const cell = event.currentTarget as HTMLElement
  cell.classList.remove('highlight-drop')
}

// 列标题拖拽处理
const handleColumnTitleDrop = (event: DragEvent, columnIndex: number) => {
  const data = event.dataTransfer?.getData('text/plain')
  if (!data) return

  const option = JSON.parse(data) as OptionItem

  // 移除其他位置的相同选项
  removeOptionFromOtherPlaces(option.name)

  columnList.value[columnIndex].title = option.name

  const cell = event.currentTarget as HTMLElement
  cell.classList.remove('highlight-drop')
}

// 内容拖拽处理
const handleContentDrop = (event: DragEvent, columnIndex: number) => {
  const data = event.dataTransfer?.getData('text/plain')
  if (!data) return

  const option = JSON.parse(data) as OptionItem

  // 移除其他位置的相同选项
  removeOptionFromOtherPlaces(option.name)

  if (!columnList.value[columnIndex].contents.includes(option.name)) {
    columnList.value[columnIndex].contents.push(option.name)
  }

  const cell = event.currentTarget as HTMLElement
  cell.classList.remove('highlight-drop')
}

// 移除选项从其他位置
const removeOptionFromOtherPlaces = (optionName: string) => {
  // 从主标题移除
  if (mainTitle.value === optionName) {
    mainTitle.value = ''
  }

  // 从列中移除
  columnList.value.forEach(column => {
    if (column.title === optionName) {
      column.title = ''
    }
    column.contents = column.contents.filter(content => content !== optionName)
  })
}

// 拖拽开始处理
const handleMainTitleDragStart = (event: DragEvent) => {
  const option = options.value.find(opt => opt.name === mainTitle.value)
  if (option) {
    event.dataTransfer?.setData('text/plain', JSON.stringify(option))
    createDragImage(event, mainTitle.value)
  }
}

const handleColumnTitleDragStart = (event: DragEvent, title: string) => {
  const option = options.value.find(opt => opt.name === title)
  if (option) {
    event.dataTransfer?.setData('text/plain', JSON.stringify(option))
    createDragImage(event, title)
  }
}

const handleContentDragStart = (event: DragEvent, content: string) => {
  const option = options.value.find(opt => opt.name === content)
  if (option) {
    event.dataTransfer?.setData('text/plain', JSON.stringify(option))
    createDragImage(event, content)
  }
}

// 创建拖拽图像
const createDragImage = (event: DragEvent, text: string) => {
  const dragImage = document.createElement('div')
  dragImage.textContent = text
  dragImage.style.cssText = `
    position: absolute;
    left: -9999px;
    background: rgb(101, 151, 247);
    color: #fff;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: var(--font-size-small);
    white-space: nowrap;
    pointer-events: none;
    z-index: 9999;
    width: 200px;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
  `
  document.body.appendChild(dragImage)
  event.dataTransfer!.setDragImage(dragImage, 0, 0)

  setTimeout(() => {
    document.body.removeChild(dragImage)
  }, 0)
}

// 删除处理函数
const handleDeleteMainTitle = () => {
  mainTitle.value = ''
}

const handleDeleteColumnTitle = (columnIndex: number) => {
  columnList.value[columnIndex].title = ''
}

const handleDeleteContent = (columnIndex: number, contentIndex: number) => {
  columnList.value[columnIndex].contents.splice(contentIndex, 1)
}

const handleDeleteColumn = (columnIndex: number) => {
  columnList.value.splice(columnIndex, 1)
}

const handleAddColumn = () => {
  columnList.value.push({
    title: '',
    contents: []
  })
}

const validate = () => {
  return new Promise((resolve, reject) => {
    if (columnList.value.length === 0 || options.value.length === 0) {
      ElMessage.error('请添加选项和战略地图')
      reject(new Error('请添加选项'))
    }

    const strategyMapData: StrategyMapData = {
      mainTitle: mainTitle.value,
      columns: columnList.value
    }

    resolve({
      initContent: JSON.stringify(strategyMapData),
      taskOptions: JSON.stringify(options.value)
    })
  })
}

onMounted(() => {
  if (props.initContent) {
    const data = JSON.parse(props.initContent) as StrategyMapData
    mainTitle.value = data.mainTitle || ''
    columnList.value = data.columns || []
  }
  options.value = props.taskOptions ? JSON.parse(props.taskOptions) : []
})

defineExpose({
  validate
})
</script>

<style scoped lang="scss">
.key-point-control {
  @include flex(row, flex-start, flex-start, nowrap);
  gap: 20px;
  position: relative;

  .control-right {
    flex: 1;
    min-width: 0;
    @include flex(column, flex-start, stretch, nowrap);
    gap: 16px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 14px;
    border-radius: 6px;
    max-height: 80vh;
    overflow-y: auto;

    .right-header {
      @include flex(row, flex-start, flex-start, nowrap);
      gap: 14px;

      span {
        font-weight: bold;
        color: var(--el-text-color-primary);
      }

      .tip-text {
        color: var(--el-text-color-secondary);
        font-size: 14px;
        font-weight: normal;
      }
    }
  }

  .map-wrapper {
    @include flex(column, flex-start, stretch, nowrap);
    gap: 20px;

    // 主标题区域
    .main-title-area {
      .main-title-content {
        padding: 16px;
        border: 2px dashed #409EFF;
        border-radius: 8px;
        min-height: 80px;
        background: rgba(64, 158, 255, 0.05);
        transition: all 0.2s ease;
        @include flex(row, center, center, nowrap);

        &.highlight-drop {
          border-color: #67C23A;
          background: rgba(103, 194, 58, 0.1);
          box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2);
        }

        .title-placeholder {
          color: var(--el-text-color-secondary);
          font-size: 16px;
          font-weight: 500;
        }

        .main-title-item {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: #ffffff;
          padding: 12px 24px;
          border-radius: 8px;
          font-size: 18px;
          font-weight: bold;
          cursor: move;
          position: relative;
          min-width: 200px;
          text-align: center;

          &:hover .delete-icon {
            opacity: 1;
          }
        }
      }
    }

    // 列区域
    .columns-area {
      @include flex(column, flex-start, stretch, nowrap);
      gap: 16px;

      .column-item {
        position: relative;
        padding-right: 40px;

        .delete-column {
          position: absolute;
          top: 10px;
          right: 0;
          z-index: 10;
        }

        .column-content {
          @include flex(row, flex-start, stretch, nowrap);
          border: 1px solid #e4e7ed;
          border-radius: 8px;
          overflow: hidden;

          // 左侧标题区域
          .column-title {
            width: 200px;
            background: #f5f7fa;
            border-right: 1px solid #e4e7ed;

            .title-drop-area {
              padding: 16px;
              min-height: 120px;
              @include flex(row, center, center, nowrap);
              transition: all 0.2s ease;

              &.highlight-drop {
                background: rgba(64, 158, 255, 0.1);
                border: 2px dashed #409EFF;
              }

              .title-placeholder {
                color: var(--el-text-color-secondary);
                font-size: 14px;
              }

              .title-item {
                background: rgb(52, 114, 233);
                color: #ffffff;
                padding: 3px 12px;
                border-radius: 6px;
                cursor: move;
                position: relative;
                width: 100%;
                text-align: center;
                font-weight: 500;
                line-height: 24px;
                @include multi-ellipsis(2);

                &:hover .delete-icon {
                  opacity: 1;
                }
              }
            }
          }

          // 右侧内容区域
          .column-content-area {
            flex: 1;

            .content-drop-area {
              padding: 16px;
              min-height: 120px;
              transition: all 0.2s ease;

              &.highlight-drop {
                background: rgba(64, 158, 255, 0.05);
                border: 2px dashed #409EFF;
              }

              .content-placeholder {
                height: 88px;
                @include flex(row, center, center, nowrap);
                color: var(--el-text-color-secondary);
                font-size: 14px;
              }

              .content-items {
                @include flex(row, flex-start, flex-start, wrap);
                gap: 8px;

                .content-item {
                  background: rgba(52, 114, 233, 0.8);
                  color: #ffffff;
                  padding: 4px 8px;
                  border-radius: 4px;
                  cursor: move;
                  position: relative;
                  font-size: 14px;
                  @include text-ellipsis();
                  min-width: 120px;
                  max-width: 180px;

                  &:hover .delete-icon {
                    opacity: 1;
                  }
                }
              }
            }
          }
        }
      }

      .add-column {
        @include flex(row, center, center, nowrap);
        padding: 20px;
      }
    }
  }

  // 删除图标样式
  .delete-icon {
    position: absolute;
    top: 50%;
    right: 8px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    cursor: pointer;
    opacity: 0;
    transition: all 0.2s ease;
    color: #ffffff;
    transform: translateY(-50%);
    @include flex(row, center, center, nowrap);
    background-color: rgba(0, 0, 0, 0.3);

    &:hover {
      transform: translateY(-50%) scale(1.2);
      background-color: rgba(0, 0, 0, 0.5);
    }

    .el-icon {
      font-size: 12px;
      font-weight: bold;
    }
  }
}

@keyframes borderDash {
  to {
    background-position: 100% 100%;
  }
}
</style>
