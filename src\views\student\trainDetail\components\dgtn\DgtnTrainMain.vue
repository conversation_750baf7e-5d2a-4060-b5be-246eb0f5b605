<template>
  <DgtnLayout class="train-detail dgtn" v-loading="loading">
    <template #header>
      <DgtnHeader :taskList="taskList" :multipleTask="multipleTask" :taskDetail="taskDetail" @submit="onSubmit"
        @hidden-dialog="hiddenDialog" :disabled="disabled" @reset="handleReset">
      </DgtnHeader>
    </template>
    <div class="main-wrapper flex">
      <div class="left-wrapper" :class="{ 'case-materials': !multipleTask }">
        <DgtnTaskList v-if="multipleTask" v-model:currentTask="currentTask" v-model:taskList="taskList"
          @change="getStuTrainDetail">
        </DgtnTaskList>
        <CaseMaterials v-else-if="taskDetail?.taskNote" :taskDetail="taskDetail" />
      </div>
      <div class="right-wrapper">
        <component :key="taskDetail?.questionId" :is="currentComponent" ref="componentRef" :taskDetail="taskDetail"
          :disabled="disabled" :apiExtraParams="apiExtraParams" @submit="onSubmit" @hidden-dialog="hiddenDialog"
          @reset="handleReset" />
      </div>
    </div>
  </DgtnLayout>
</template>

<script setup lang="ts">
import StrategyMapFlow from './StrategyMapFlow.vue'
import CaseMaterials from './CaseMaterials.vue'
import DgtnHeader from './Header.vue'
import DgtnTaskList from './TaskList.vue'
import DgtnLayout from '@/layout/components/DgtnLayout.vue'
import FlowDesign from './FlowDesign.vue'
import BCGMatrix from './BCGMatrix.vue'
import RadarChart from './RadarChart.vue'
import TimeAxis from './TimeAxis.vue'
import Empty from '../Empty.vue'
import MindMap from './MindMap.vue'
import Organizational from './Organizational.vue'
import StrategyMap from './StrategyMap.vue'
import PointControl from './PointControl.vue'
import System from './System.vue'
import RiskCoordinate from './RiskCoordinate.vue'
import BusinessFlow from './BusinessFlow.vue'

import { computed } from 'vue'
import { useTrainDetail } from '../../hooks/useTrainDetail'

const {
  loading,
  taskList,
  currentTask,
  taskDetail,
  disabled,
  componentRef,
  getStuTrainDetail,
  handleSave,
  handleSubmit,
  hiddenDialog,
  apiExtraParams,
  handleReset
} = useTrainDetail()

const multipleTask = computed(() => {
  return !(taskList.value.length === 1 && taskList.value[0]?.questionList?.length === 1)
})

const currentComponent = computed(() => {
  if (taskDetail.value?.questionType === 8) {
    return [BCGMatrix, TimeAxis, RadarChart, MindMap][taskDetail.value?.questionSecondType - 1]
  }
  return taskDetail.value ? [Organizational, StrategyMap, BusinessFlow, PointControl, System, RiskCoordinate, FlowDesign, StrategyMapFlow][taskDetail.value?.questionType] || Empty : Empty
})


const onSubmit = (isAll: 0 | 1) => {
  if (isAll === 1) {
    handleSubmit()
  } else {
    handleSave()
  }
}

</script>

<style scoped lang="scss">
.train-detail {
  &.dgtn {
    :deep(.empty) {
      margin-top: 0;
      height: 100%;
      background-image: linear-gradient(to bottom, #fff 0%, rgba(255, 255, 255, 0.5) 100%);
      box-shadow: 0px 2px 4px 0px rgba(120, 147, 187, 0.15);
      border-radius: 12px;
      border: solid 1px #ffffff;

      img {
        filter: hue-rotate(17deg);
      }
    }
  }

  .left-wrapper {
    flex-shrink: 0;
  }

  .main-wrapper {
    height: 100%;
    gap: 12px;
    padding: 16px;
    position: relative;
  }

  .right-wrapper {
    flex: 1;
    min-width: 0;
  }

  @media screen and (max-width: 1440px) {

    .left-wrapper {
      max-width: 260px;
    }
  }
}
</style>
