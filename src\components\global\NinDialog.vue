<template>
  <teleport v-if="visible" to='body'>
    <div ref="dialogRef" class="nin-dialog" :style="dialogStyle" @mousedown="handleDialogClick">
      <!-- 标题栏 -->
      <div class="nin-dialog-header" @mousedown="startDrag">
        <slot name="header">
          <div class="nin-dialog-title ">
            {{ title }}
          </div>
        </slot>
        <el-button v-if="closable" :icon="Close" class="nin-dialog-close" @click="handleClose">

        </el-button>
      </div>

      <!-- 内容区域 -->
      <div class="nin-dialog-body">
        <slot></slot>
      </div>

      <!-- 底部操作区 -->
      <div v-if="$slots.footer" class="nin-dialog-footer">
        <slot name="footer"></slot>
      </div>

      <!-- 调整大小的拖拽点 -->
      <div v-if="resizable" class="resize-handles">
        <div class="resize-handle resize-n" @mousedown="startResize('n', $event)"></div>
        <div class="resize-handle resize-s" @mousedown="startResize('s', $event)"></div>
        <div class="resize-handle resize-w" @mousedown="startResize('w', $event)"></div>
        <div class="resize-handle resize-e" @mousedown="startResize('e', $event)"></div>
        <div class="resize-handle resize-nw" @mousedown="startResize('nw', $event)"></div>
        <div class="resize-handle resize-ne" @mousedown="startResize('ne', $event)"></div>
        <div class="resize-handle resize-sw" @mousedown="startResize('sw', $event)"></div>
        <div class="resize-handle resize-se" @mousedown="startResize('se', $event)"></div>
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { Close } from '@element-plus/icons-vue'
import { ref, computed, onMounted, onUnmounted } from 'vue'

interface Props {
  visible?: boolean
  title?: string
  width?: number | string
  height?: number | string
  top?: number | string
  left?: number | string
  closable?: boolean
  resizable?: boolean
  draggable?: boolean
  minWidth?: number
  minHeight?: number
  maxWidth?: number
  maxHeight?: number
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  title: '弹窗',
  width: 500,
  height: 400,
  top: 'center',
  left: 'center',
  closable: true,
  resizable: true,
  draggable: true,
  minWidth: 200,
  minHeight: 150,
  maxWidth: window.innerWidth - 50,
  maxHeight: window.innerHeight - 50
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
  close: []
}>()

const dialogRef = ref<HTMLElement>()
const dialogPosition = ref({ x: 0, y: 0 })
const dialogSize = ref({ width: 0, height: 0 })
const isDragging = ref(false)
const isResizing = ref(false)
const dragStart = ref({ x: 0, y: 0 })
const resizeDirection = ref('')
const resizeStart = ref({ x: 0, y: 0, width: 0, height: 0, left: 0, top: 0 })

// 初始化位置和大小
onMounted(() => {
  const width = typeof props.width === 'number' ? props.width : parseInt(props.width as string)
  const height = typeof props.height === 'number' ? props.height : parseInt(props.height as string)

  // 计算居中位置
  const centerX = (window.innerWidth - width) / 2
  const centerY = (window.innerHeight - height) / 2

  dialogPosition.value = {
    x: props.left === 'center' ? centerX : (typeof props.left === 'number' ? props.left : parseInt(props.left as string)),
    y: props.top === 'center' ? centerY : (typeof props.top === 'number' ? props.top : parseInt(props.top as string))
  }
  dialogSize.value = {
    width,
    height
  }
})

// 计算弹窗样式
const dialogStyle = computed(() => {
  return {
    width: `${dialogSize.value.width}px`,
    height: `${dialogSize.value.height}px`,
    left: `${dialogPosition.value.x}px`,
    top: `${dialogPosition.value.y}px`,
    transform: 'none'
  }
})

// 开始拖拽
const startDrag = (e: MouseEvent) => {
  if (!props.draggable) return
  e.preventDefault()
  isDragging.value = true
  dragStart.value = {
    x: e.clientX - dialogPosition.value.x,
    y: e.clientY - dialogPosition.value.y
  }
  document.addEventListener('mousemove', handleDrag)
  document.addEventListener('mouseup', stopDrag)
}

// 处理拖拽
const handleDrag = (e: MouseEvent) => {
  if (!isDragging.value) return
  const newX = e.clientX - dragStart.value.x
  const newY = e.clientY - dragStart.value.y

  // 边界检查
  const maxX = window.innerWidth - dialogSize.value.width
  const maxY = window.innerHeight - dialogSize.value.height

  dialogPosition.value = {
    x: Math.max(0, Math.min(newX, maxX)),
    y: Math.max(0, Math.min(newY, maxY))
  }
}

// 停止拖拽
const stopDrag = () => {
  isDragging.value = false
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
}

// 开始调整大小
const startResize = (direction: string, e: MouseEvent) => {
  if (!props.resizable) return
  e.preventDefault()
  e.stopPropagation()

  isResizing.value = true
  resizeDirection.value = direction
  resizeStart.value = {
    x: e.clientX,
    y: e.clientY,
    width: dialogSize.value.width,
    height: dialogSize.value.height,
    left: dialogPosition.value.x,
    top: dialogPosition.value.y
  }

  document.addEventListener('mousemove', handleResize)
  document.addEventListener('mouseup', stopResize)
}

// 处理调整大小
const handleResize = (e: MouseEvent) => {
  if (!isResizing.value) return

  const deltaX = e.clientX - resizeStart.value.x
  const deltaY = e.clientY - resizeStart.value.y

  let newWidth = resizeStart.value.width
  let newHeight = resizeStart.value.height
  let newLeft = resizeStart.value.left
  let newTop = resizeStart.value.top

  // 根据拖拽方向调整大小和位置
  if (resizeDirection.value.includes('e')) {
    newWidth = resizeStart.value.width + deltaX
  }
  if (resizeDirection.value.includes('w')) {
    newWidth = resizeStart.value.width - deltaX
    newLeft = resizeStart.value.left + deltaX
  }
  if (resizeDirection.value.includes('s')) {
    newHeight = resizeStart.value.height + deltaY
  }
  if (resizeDirection.value.includes('n')) {
    newHeight = resizeStart.value.height - deltaY
    newTop = resizeStart.value.top + deltaY
  }

  // 应用最小和最大尺寸限制
  newWidth = Math.max(props.minWidth, Math.min(newWidth, props.maxWidth))
  newHeight = Math.max(props.minHeight, Math.min(newHeight, props.maxHeight))

  // 边界检查
  if (newLeft < 0) {
    newWidth += newLeft
    newLeft = 0
  }
  if (newTop < 0) {
    newHeight += newTop
    newTop = 0
  }
  if (newLeft + newWidth > window.innerWidth) {
    newWidth = window.innerWidth - newLeft
  }
  if (newTop + newHeight > window.innerHeight) {
    newHeight = window.innerHeight - newTop
  }

  dialogSize.value = { width: newWidth, height: newHeight }
  dialogPosition.value = { x: newLeft, y: newTop }
}

// 停止调整大小
const stopResize = () => {
  isResizing.value = false
  resizeDirection.value = ''
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
}

// 处理关闭
const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}


// 阻止弹窗内部点击冒泡
const handleDialogClick = (e: MouseEvent) => {
  e.stopPropagation()
}

// 清理事件监听器
onUnmounted(() => {
  document.removeEventListener('mousemove', handleDrag)
  document.removeEventListener('mouseup', stopDrag)
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', stopResize)
})
</script>

<style scoped lang="scss">
.nin-dialog {
  position: absolute;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  user-select: none;
  z-index: 9999;
}

.nin-dialog-header {
  cursor: move;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 48px;
  position: relative;
  width: 100%;
  padding: 0 15px;
  position: relative;

  &::after {
    display: inline-block;
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 48px;
    background-image: linear-gradient(to bottom,
        #d7e1fa 0%,
        transparent 100%);
  }
}

.nin-dialog-title {
  position: relative;
  height: 100%;
  z-index: 1;
  border-bottom: 1px solid #c8ddfb;
  position: relative;
  display: flex;
  align-items: center;
  flex: 1;

  &::after {
    display: inline-block;
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 24px;
    height: 3px;
    background-color: #507af1;
  }
}

.nin-dialog-close {
  position: absolute;
  right: 12px;
  top: 12px;
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #313052;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s;
  z-index: 1;
}

.nin-dialog-close:hover {
  background: #f0f0f0;
  color: #666;
}

.nin-dialog-body {
  padding: 20px;
  flex: 1;
  overflow: auto;
}

.nin-dialog-footer {
  padding: 16px 20px;
  border-top: 1px solid #e8e8e8;
  display: flex;
  justify-content: flex-end;
  gap: 8px;
}

/* 调整大小的拖拽点 */
.resize-handles {
  pointer-events: none;
}

.resize-handle {
  position: absolute;
  pointer-events: auto;
  background: transparent;
}

/* 边缘拖拽点 */
.resize-n {
  top: -3px;
  left: 8px;
  right: 8px;
  height: 6px;
  cursor: n-resize;
}

.resize-s {
  bottom: -3px;
  left: 8px;
  right: 8px;
  height: 6px;
  cursor: s-resize;
}

.resize-w {
  left: -3px;
  top: 8px;
  bottom: 8px;
  width: 6px;
  cursor: w-resize;
}

.resize-e {
  right: -3px;
  top: 8px;
  bottom: 8px;
  width: 6px;
  cursor: e-resize;
}

/* 角落拖拽点 */
.resize-nw {
  top: -3px;
  left: -3px;
  width: 8px;
  height: 8px;
  cursor: nw-resize;
}

.resize-ne {
  top: -3px;
  right: -3px;
  width: 8px;
  height: 8px;
  cursor: ne-resize;
}

.resize-sw {
  bottom: -3px;
  left: -3px;
  width: 8px;
  height: 8px;
  cursor: sw-resize;
}

.resize-se {
  bottom: -3px;
  right: -3px;
  width: 8px;
  height: 8px;
  cursor: se-resize;
}

/* 拖拽时的样式 */
.nin-dialog:active {
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
}
</style>
