<template>
  <div class="point-control">
    <div class="options-wrapper">
      <OptionList :options="options" :used-option-ids="usedOptionIds" :disabled="disabled"
        @drag-start="handleOptionDragStart" />
    </div>
    <div class="base-content content">
      <div class="section-title-light">
        <div class="title-label">{{ taskDetail.questionName }}</div>
      </div>
      <div class="content-wrapper" ref="contentWrapper">
        <div class="radar-chart">
          <svg :width="size" :height="size" :viewBox="`0 0 ${size} ${size}`" xmlns="http://www.w3.org/2000/svg">
            <g class="radar-grid">
              <polygon v-for="(p, i) in pentagons" :key="`p-${i}`" :points="p.points" :fill="p.fill" stroke="#b7c9e3"
                stroke-width="1" />
            </g>
          </svg>
          <div class="force-labels">
            <div v-for="(force, index) in forces" :key="index" class="force-label" :class="getLabelClass(index)"
              :style="getLabelPositions[index]">
              <div class="force-header">
                <div class="force-name" :title="force.name">{{ force.name }}</div>
              </div>
              <div class="drop-area">
                <!-- <el-scrollbar height="100%"> -->
                <div class="content-items">
                  <div class="content-item" v-for="(content, optionIndex) in force.contents" :key="optionIndex">
                    <OptionItem :width="dropWidth" :name="content" :show-delete="!disabled && !!content"
                      :disabled="disabled" :empty="!content" @dragstart="handleItemDragStart($event, content)"
                      @dragover.prevent @dragenter="handleDragEnter($event)"
                      @drop="handleContentDrop($event, index, optionIndex)" @dragleave="handleDragLeave($event)"
                      @delete="handleDeleteContent(index, optionIndex, content)" />
                  </div>
                </div>
                <!-- </el-scrollbar> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import OptionList from './OptionList.vue'
import OptionItem from './OptionItem.vue'
import type { OptionItem as OptionItemType } from '@/views/admin/types'
import type { TaskDetailItem } from '../../types'
import * as api from '@/api/student'

interface IProps {
  taskDetail: TaskDetailItem,
  disabled?: boolean
  mode?: 'viewAnswer' | 'answer'
  apiExtraParams?: Record<string, string>
}

interface ForceData {
  name: string
  contents: string[]
}

const props = withDefaults(defineProps<IProps>(), {
  disabled: false,
  apiExtraParams: () => ({}),
  mode: 'answer'
})

const isDragging = ref(false)
const options = ref<OptionItemType[]>([])
const forces = ref<ForceData[]>([
  { name: '', contents: [''] },
  { name: '', contents: [''] },
  { name: '', contents: [''] },
  { name: '', contents: [''] },
  { name: '', contents: [''] }
])
const currentTarget = ref<HTMLElement | null>(null)

const dropWidth = ref(140)
// SVG dimensions - 动态调整
const size = ref(600)
const levels = ref(5)
const center = computed(() => size.value / 2)
const contentWrapper = ref<HTMLElement>()

// ResizeObserver 用于监听容器尺寸变化
let resizeObserver: ResizeObserver | null = null

const updateSize = () => {
  if (contentWrapper.value) {
    const rect = contentWrapper.value.getBoundingClientRect()
    const containerSize = Math.min(rect.width, rect.height) - 58 // 减去padding
    size.value = Math.min(containerSize * 0.85, 650) // 最大不超过650
    dropWidth.value = +(Math.min((rect.width - size.value) / 2 - 10, 160).toFixed(2))
  }
}


const pentagons = computed(() => {
  const result = []
  for (let i = levels.value; i > 0; i--) {
    const radius = (center.value * i) / levels.value * 1// Scale down to fit labels
    let points = ''
    for (let j = 0; j < 5; j++) {
      const angle = (j * 2 * Math.PI) / 5 - Math.PI / 2 // Start from top
      const x = center.value + radius * Math.cos(angle)
      const y = center.value + radius * Math.sin(angle)
      points += `${x},${y} `
    }
    result.push({
      points: points.trim(),
      fill: i % 2 !== 0 ? '#fff1ed' : '#fbfcff'
    })
  }
  return result
})

// 计算标签位置
const getLabelPositions = computed(() => {
  const rect = contentWrapper.value?.getBoundingClientRect()
  const offsetX = rect?.width ? (rect.width - size.value) / 2 : 0
  const offsetY = rect?.height ? (rect.height - size.value - 40) / 2 : 0
  const positions = []
  const outerRadius = center.value * 1 // 与 pentagon 的最外层半径完全一致
  const labelOffset = 0 // 标签距离顶点的偏移距离，可调整

  for (let i = 0; i < 5; i++) {
    const angle = (i * 2 * Math.PI) / 5 - Math.PI / 2 // 从顶部开始，与pentagon计算一致
    const x = center.value + (outerRadius + labelOffset) * Math.cos(angle) + offsetX
    const y = center.value + (outerRadius + labelOffset) * Math.sin(angle) + offsetY

    // 根据角度位置判断是否需要额外调整
    let transform = 'translate(-50%, -50%)' // 默认居中对齐
    let width = dropWidth.value + 'px'
    // 顶部标签向上偏移
    if (i === 0) {
      transform = 'translate(-50%, -100%)'
      width = '80%'
    }
    // 右上角标签向右上偏移
    else if (i === 1) {
      transform = 'translate(0, -50%)'
    }
    // 右下角标签向右下偏移
    else if (i === 2) {
      width = '40%'
      transform = 'translate(-20%, -10%)'
    }
    // 左下角标签向左下偏移
    else if (i === 3) {
      width = '40%'
      transform = 'translate(-80%, -10%)'
    }
    // 左上角标签向左上偏移
    else if (i === 4) {
      transform = 'translate(-110%, -50%)'
    }

    positions.push({
      width,
      left: `${x}px`,
      top: `${y}px`,
      transform
    })
  }

  return positions
})

const getLabelClass = (index: number) => {
  const positions = ['top', 'right-top', 'right-bottom', 'left-bottom', 'left-top']
  return positions[index]
}

const handleOptionDragStart = (option: OptionItemType, event: DragEvent) => {
  if (props.disabled) return
  event.dataTransfer?.setData('text/plain', JSON.stringify(option))
  isDragging.value = true
  currentTarget.value = event.currentTarget as HTMLElement
}

const handleItemDragStart = (event: DragEvent, optionName: string) => {
  if (props.disabled) return
  const option = options.value.find(opt => opt.name === optionName)
  if (option) {
    event.dataTransfer?.setData('text/plain', JSON.stringify(option))
    isDragging.value = true
    currentTarget.value = event.currentTarget as HTMLElement
  }
}

const handleDragEnter = (event: DragEvent) => {
  if (props.disabled) return
  const target = event.currentTarget as HTMLElement
  target.classList.add('highlight-drop')
}

const handleDragLeave = (event: DragEvent) => {
  const target = event.currentTarget as HTMLElement
  target.classList.remove('highlight-drop')
}

const handleSaveStep = async (answer: {
  content: string,
  index: number,
  operationType: 0 | 1,
  columnIndex: number
}) => {
  return new Promise(async (resolve, reject) => {
    try {
      const params = {
        questionId: props.taskDetail.questionId,
        ...props.apiExtraParams,
        ...answer
      }
      const { data: { code, msg, data } } = await api.analysisRealTime(params)
      if (code === 200) {
        if (data === 1) {
          ElMessage.success('回答正确')
        } else if (data === 0) {
          ElMessage.error('回答错误')
        }
        resolve(true)
      } else {
        ElMessage.error(msg)
        reject(msg)
      }
    } catch (error) {
      reject(error)
    }
  })
}

const removeOptionFromOtherPlaces = (optionName: string) => {
  return new Promise(async (resolve, reject) => {
    try {
      let forceIndex = -1
      let contentIndex = -1
      forces.value.forEach((force, index) => {
        const findIndex = force.contents.indexOf(optionName)
        if (findIndex > -1) {
          forceIndex = index
          contentIndex = findIndex
        }
      })
      if (forceIndex > -1) {
        await handleSaveStep({
          content: optionName,
          index: forceIndex,
          operationType: 1,
          columnIndex: contentIndex
        })
        forces.value[forceIndex].contents[contentIndex] = ''
      }
      resolve(true)
    } catch (error) {
      reject(error)
    }
  })
}

const handleContentDrop = async (event: DragEvent, index: number, contentIndex: number) => {
  if (props.disabled || !isDragging.value || currentTarget.value === event.currentTarget) return
  const target = event.currentTarget as HTMLElement
  target.classList.remove('highlight-drop')
  const data = event.dataTransfer?.getData('text/plain')
  if (!data) return

  try {
    if (isDragging.value) {
      const option = JSON.parse(data) as OptionItemType
      if (!option || !option.name || !option.id) return

      await removeOptionFromOtherPlaces(option.name)
      await handleSaveStep({
        content: option.name,
        index: index,
        columnIndex: contentIndex,
        operationType: 0
      })
      forces.value[index].contents[contentIndex] = option.name
      isDragging.value = false
    }
  } catch (e) {
    isDragging.value = false
    console.error('Invalid drag data format:', e)
  }
}

const handleDeleteContent = async (index: number, contentIndex: number, content: string) => {
  if (props.disabled) return
  try {
    await handleSaveStep({
      content,
      index: index,
      operationType: 1,
      columnIndex: contentIndex
    })
    forces.value[index].contents[contentIndex] = ''
  } catch (error) {
    console.error(error)
  }
}

const optionsMap = computed(() => {
  const map = new Map<string, string>()
  options.value.forEach(item => {
    map.set(item.name, item.id)
  })
  return map
})

const usedOptionIds = computed(() => {
  const ids = new Set<string>()
  forces.value.forEach(force => {
    force.contents.forEach(name => {
      const id = optionsMap.value.get(name)
      if (id) ids.add(id)
    })
  })
  return ids
})

const handleDragEnd = () => {
  isDragging.value = false
}

onMounted(() => {
  options.value = props.taskDetail.taskOptions ? JSON.parse(props.taskDetail.taskOptions) : []
  if (props.taskDetail.stuAnswer) {
    const stuAnswer = JSON.parse(props.taskDetail.stuAnswer)
    console.log(stuAnswer)
    forces.value = stuAnswer
  } else if (props.taskDetail.initContent) {
    const initContent = JSON.parse(props.taskDetail.initContent)
    if (Array.isArray(initContent) && initContent.length === 5) {
      forces.value = initContent.map(item => {
        return {
          name: item.name,
          contents: Array(item.contents.length).fill('')
        }
      })
    }
  }

  document.addEventListener('dragend', handleDragEnd)

  // 初始化尺寸
  updateSize()

  // 设置ResizeObserver监听容器尺寸变化
  if (contentWrapper.value) {
    resizeObserver = new ResizeObserver(() => {
      updateSize()
    })
    resizeObserver.observe(contentWrapper.value)
  }
})

// 组件卸载时移除事件监听和ResizeObserver
onUnmounted(() => {
  document.removeEventListener('dragend', handleDragEnd)
  if (resizeObserver) {
    resizeObserver.disconnect()
    resizeObserver = null
  }
})

const getStuAnswer = () => {
  return Promise.resolve(JSON.stringify(forces.value))
}

defineExpose({
  getStuAnswer
})

watch(() => props.taskDetail, (newVal) => {
  if (newVal.stuAnswer) {
    forces.value = JSON.parse(newVal.stuAnswer)
  }
})
</script>

<style scoped lang="scss">
.point-control {
  @include flex(row, flex-start, flex-start, nowrap);
  height: 100%;
  gap: 16px;

  .base-content {
    height: 100%;
    gap: 20px;
    @include flex(column, flex-start, flex-start, nowrap);
    position: relative;

    .content-wrapper {
      width: 100%;
      flex: 1;
      min-height: 0;
      padding: 14px;
      display: flex;
      justify-content: center;
      align-items: center;
      background: url('@/assets/images/train/bg-light-task.png') no-repeat bottom center;
      background-size: 100% auto;
    }
  }

  .options-wrapper {
    width: 240px;
    height: 100%;
  }

  .content {
    min-width: 0;
    flex: 1;
    background-image: linear-gradient(to bottom, #fff 0%, rgba(255, 255, 255, 0.5) 100%);
    box-shadow: 0px 2px 4px 0px rgba(120, 147, 187, 0.15);
    border-radius: 6px;
    border: solid 1px #ffffff;
    overflow: hidden;
  }

  .radar-chart {
    height: 100%;
    width: 100%;
    position: relative;

    .radar-chart-wrapper {
      position: relative;
      overflow: visible;
    }

  }

  .force-labels {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }

  .force-label {
    position: absolute;
    @include flex(column, center, center, nowrap);
    gap: 6px;

    .force-header {
      text-align: center;
    }

    .force-name {
      color: #333;
      font-size: var(--font-size-small);
      @include multi-ellipsis(2);
    }

    .content-items {
      @include flex(column, flex-start, center, wrap);
      gap: 8px;
      width: fit-content;
    }

    &.highlight-drop {
      border-color: #897dff;
      background-color: rgba(137, 125, 255, 0.2);
    }
  }

  .force-label.top {
    flex-direction: column-reverse;
    width: 80%;

    .drop-area {
      max-width: 100%;
      overflow-x: auto;
      height: 100%;

      &::-webkit-scrollbar {
        display: none;
      }

      &:hover {

        &::-webkit-scrollbar {
          display: block;
        }
      }
    }

    .content-items {
      @include flex(row, center, center, nowrap);
      gap: 8px;
      width: fit-content;
    }

  }

  .force-label.right-top,
  .force-label.left-top {

    .drop-area {
      max-height: 174px;
      overflow-y: auto;
      padding: 0 5px;
      overflow-x: hidden;

      &::-webkit-scrollbar {
        display: none;
      }

      &:hover {

        &::-webkit-scrollbar {
          display: block;
        }
      }
    }
  }

  .force-label.right-bottom,
  .force-label.left-bottom {
    .drop-area {
      max-height: 130px;
      padding: 0 5px;
      overflow-y: auto;
      overflow-x: hidden;

      &::-webkit-scrollbar {
        display: none;
      }

      &:hover {

        &::-webkit-scrollbar {
          display: block;
        }
      }
    }
  }

  @media screen and (max-width: 1440px) {
    .options-wrapper {
      width: 200px;
    }

    .force-label.right-bottom,
    .force-label.left-bottom {
      .drop-area {
        max-height: 70px;
        overflow-y: auto;
      }
    }
  }
}

.radar-chart {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>
