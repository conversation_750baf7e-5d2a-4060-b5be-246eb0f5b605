<template>
  <div>

  </div>
</template>

<script setup lang="ts">
import { useRouter, useRoute } from 'vue-router'
import { onMounted } from 'vue'
import { setStorage } from '@/utils/storage'
import { useStore } from 'vuex';

const store = useStore();
const router = useRouter();
const route = useRoute();

onMounted(() => {
  if (route.query.access_token && route.query.redirect) {
    setStorage('userInfo', JSON.parse(route.query.userInfo as string || '{}'))
    setStorage('token', route.query.access_token)
    store.commit("user/SET_USERINFO", JSON.parse(route.query.userInfo as string || '{}'));
    store.commit('SET_TOKEN', route.query.access_token);
    const query = { ...route.query }
    delete query.access_token
    delete query.userInfo
    delete query.redirect
    router.replace({
      path: route.query.redirect as string,
      query
    })
  }
})
</script>

<style scoped></style>
