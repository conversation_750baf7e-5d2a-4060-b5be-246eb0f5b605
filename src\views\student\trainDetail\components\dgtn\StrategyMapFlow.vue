<template>
  <div class="point-control">
    <div class="options-wrapper">
      <OptionList :options="options" :used-option-ids="usedOptionIds" :disabled="disabled"
        @drag-start="handleOptionDragStart" />
    </div>
    <div class="base-content content">
      <div class="section-title-light">
        <div class="title-label">{{ taskDetail.questionName }}</div>
      </div>
      <div class="content-wrapper">
        <el-scrollbar height="100%">
          <div class="map-wrapper">
            <div class="main-title-area">
              <OptionItem width="50%" :name="mainTitle" :show-delete="!disabled && !!mainTitle && draggingIndex === -2"
                :gray="draggingIndex !== -2" :empty="!mainTitle" :disabled="disabled"
                @dragenter="handleDragEnter($event)" @dragstart="handleItemDragStart($event, mainTitle)"
                @dragleave="handleDragLeave($event)" @delete="handleDeleteMainTitle" @drop="handleMainTitleDrop($event)"
                @dragover.prevent />
            </div>

            <div class="column-item" v-for="(column, columnIndex) in columnList" :key="columnIndex">
              <el-scrollbar>
                <div class="column-content">
                  <div class="column-title">
                    <OptionItem :width="120" :height="88" :gray="draggingIndex !== -1" :name="column.title"
                      :disabled="disabled" :show-delete="!disabled && !!column.title && draggingIndex === -1"
                      :empty="!column.title" @dragover.prevent @dragenter="handleDragEnter($event)"
                      @dragstart="handleItemDragStart($event, column.title)" @dragleave="handleDragLeave($event)"
                      @drop="handleColumnTitleDrop($event, columnIndex)"
                      @delete="handleDeleteColumnTitle(columnIndex)" />
                  </div>

                  <div class="column-content-area">
                    <div class="column-content-item" v-for="(content, contentIndex) in column.contents"
                      :key="contentIndex">
                      <OptionItem :width="240" :name="content" :disabled="disabled"
                        :show-delete="!disabled && !!content && draggingIndex === columnIndex"
                        :gray="columnIndex !== draggingIndex" :empty="!content" @dragover.prevent
                        @dragstart="handleItemDragStart($event, content)" @dragenter="handleDragEnter($event)"
                        @drop="handleContentDrop($event, columnIndex, contentIndex)"
                        @dragleave="handleDragLeave($event)" @delete="handleDeleteContent(columnIndex, contentIndex)" />
                    </div>
                  </div>
                </div>
              </el-scrollbar>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import OptionList from './OptionList.vue'
import OptionItem from './OptionItem.vue'
import type { OptionItem as OptionItemType } from '@/views/admin/types'
import type { TaskDetailItem } from '../../types'
import * as api from '@/api/student'
interface IProps {
  taskDetail: TaskDetailItem,
  disabled?: boolean,
  apiExtraParams?: Record<string, string>,
  mode?: 'viewAnswer' | 'answer'
}

interface ColumnItem {
  title: string
  contents: string[]
}

interface StrategyMapData {
  mainTitle: string
  columns: ColumnItem[]
}

const props = withDefaults(defineProps<IProps>(), {
  disabled: false,
  apiExtraParams: () => ({}),
  mode: 'answer'
})

const options = ref<OptionItemType[]>([])
const mainTitle = ref('')
const columnList = ref<ColumnItem[]>([])
const isDragging = ref(false)
const draggingIndex = ref(-1); // 当前拖拽的索引, -1是拖拽column-title，0-n是拖拽column-content（根据columnList顺序）， -2是拖拽main-title

const handleOptionDragStart = (option: OptionItemType, event: DragEvent) => {
  if (props.disabled) return
  isDragging.value = true
  event.dataTransfer?.setData('text/plain', JSON.stringify(option))
  currentTarget.value = event.currentTarget as HTMLElement
}

const currentTarget = ref<HTMLElement | null>(null)
const handleItemDragStart = (event: DragEvent, optionName: string) => {
  if (props.disabled) return
  const option = options.value.find(opt => opt.name === optionName)
  if (option) {
    isDragging.value = true
    currentTarget.value = event.currentTarget as HTMLElement
    event.dataTransfer?.setData('text/plain', JSON.stringify(option))
  }
}

const handleDragEnd = () => {
  isDragging.value = false
}

const handleDragEnter = (event: DragEvent) => {
  if (props.disabled) return
  const target = event.currentTarget as HTMLElement
  target.classList.add('highlight-drop')
}

const handleDragLeave = (event: DragEvent) => {
  const target = event.currentTarget as HTMLElement
  target.classList.remove('highlight-drop')
}

const removeOptionFromOtherPlaces = (optionName: string) => {
  return new Promise(async (resolve, reject) => {
    try {
      if (mainTitle.value === optionName) {
        await handleSaveStep({
          itemName: '',
        })
        mainTitle.value = ''
        resolve(true)
      }
      let contentIndex = -1
      for (let i = 0; i < columnList.value.length; i++) {
        const column = columnList.value[i]
        if (column.title === optionName) {
          await handleSaveStep({
            itemName: '',
            rowIndex: i,
          })
          column.title = ''
          resolve(true)
          break
        }
        for (let j = 0; j < column.contents.length; j++) {
          if (column.contents[j] === optionName) {
            contentIndex = j
            await handleSaveStep({
              itemName: '',
              rowIndex: i,
              colIndex: j,
            })
            column.contents[j] = ''
            resolve(true)
            break
          }
        }
        if (contentIndex > -1) {
          break
        }
      }
      resolve(true)
    } catch (error) {
      console.error(error)
      reject(error)
    }
  })
}

const handleSaveStep = async (answer: { itemName: string, rowIndex?: number, colIndex?: number }) => {
  return new Promise(async (resolve, reject) => {
    const params = {
      questionId: props.taskDetail.questionId,
      ...props.apiExtraParams,
      ...answer
    }
    try {
      const { data: { code, msg, data } } = await api.realTimeFeedback(params)
      if (code === 200) {
        if (data === 1) {
          ElMessage.success('回答正确')
        } else if (data === 0) {
          ElMessage.error('回答错误')
        }
        resolve(true)
      } else {
        ElMessage.error(msg)
        reject(msg)
      }
    } catch (error) {
      console.error(error)
      reject(error)
    }
  })
}

const handleMainTitleDrop = async (event: DragEvent) => {
  if (props.disabled || draggingIndex.value !== -2 || currentTarget.value === event.currentTarget) return
  const target = event.currentTarget as HTMLElement
  target.classList.remove('highlight-drop')
  const data = event.dataTransfer?.getData('text/plain')
  if (!data) return

  try {
    if (isDragging.value) {
      const option = JSON.parse(data) as OptionItemType
      if (!option || !option.name || !option.id) return

      await removeOptionFromOtherPlaces(option.name)
      await handleSaveStep({
        itemName: option.name,
      })
      mainTitle.value = option.name
      isDragging.value = false

    }
  } catch (e) {
    isDragging.value = false
    console.error('Invalid drag data format:', e)
  }
}

const handleColumnTitleDrop = async (event: DragEvent, columnIndex: number) => {
  if (props.disabled || draggingIndex.value !== -1 || currentTarget.value === event.currentTarget) return
  const target = event.currentTarget as HTMLElement
  target.classList.remove('highlight-drop')
  const data = event.dataTransfer?.getData('text/plain')
  if (!data) return

  try {
    if (isDragging.value) {
      const option = JSON.parse(data) as OptionItemType
      if (!option || !option.name || !option.id) return

      await removeOptionFromOtherPlaces(option.name)
      const res = await handleSaveStep({
        itemName: option.name,
        rowIndex: columnIndex,
      })
      columnList.value[columnIndex].title = option.name
      isDragging.value = false
      if (res) {
        //如果columnList中所有column的title都放置完成，draggingIndex进入下一阶段
        if (columnList.value.every(column => column.title)) {
          draggingIndex.value = columnList.value.length - 1
        }
      }
    }
  } catch (e) {
    isDragging.value = false
    console.error('Invalid drag data format:', e)
  }
}

const handleContentDrop = async (event: DragEvent, columnIndex: number, contentIndex: number) => {
  if (props.disabled || draggingIndex.value !== columnIndex || currentTarget.value === event.currentTarget) return
  const target = event.currentTarget as HTMLElement
  target.classList.remove('highlight-drop')
  const data = event.dataTransfer?.getData('text/plain')
  if (!data) return

  try {
    if (isDragging.value) {
      const option = JSON.parse(data) as OptionItemType
      if (!option || !option.name || !option.id) return

      await removeOptionFromOtherPlaces(option.name)
      const res = await handleSaveStep({
        itemName: option.name,
        rowIndex: columnIndex,
        colIndex: contentIndex
      })
      columnList.value[columnIndex].contents[contentIndex] = option.name
      isDragging.value = false

      if (res) {
        //如果columnList中所有column的content都放置完成，draggingIndex进入下一阶段
        if (columnList.value[columnIndex].contents.every(content => content)) {
          draggingIndex.value = columnIndex === 0 ? -2 : columnIndex - 1
        }
      }
    }
  } catch (e) {
    isDragging.value = false
    console.error('Invalid drag data format:', e)
  }
}

const handleDeleteMainTitle = () => {
  if (props.disabled) return
  mainTitle.value = ''
  handleSaveStep({
    itemName: '',
  })
}

const handleDeleteColumnTitle = (columnIndex: number) => {
  if (props.disabled) return
  columnList.value[columnIndex].title = ''
  handleSaveStep({
    itemName: '',
    rowIndex: columnIndex,
  })
}

const handleDeleteContent = (columnIndex: number, contentIndex: number) => {
  if (props.disabled) return
  columnList.value[columnIndex].contents[contentIndex] = ''
  handleSaveStep({
    itemName: '',
    rowIndex: columnIndex,
    colIndex: contentIndex,
  })
}

const optionsMap = computed(() => {
  const map = new Map<string, string>()
  options.value.forEach(item => {
    map.set(item.name, item.id)
  })
  return map
})

const usedOptionIds = computed(() => {
  const ids = new Set<string>()
  if (mainTitle.value) {
    const id = optionsMap.value.get(mainTitle.value)
    if (id) ids.add(id)
  }
  columnList.value.forEach(column => {
    if (column.title) {
      const id = optionsMap.value.get(column.title)
      if (id) ids.add(id)
    }
    column.contents.forEach(contentName => {
      const id = optionsMap.value.get(contentName)
      if (id) ids.add(id)
    })
  })
  return ids
})

const initMapLayout = (data: StrategyMapData) => {
  mainTitle.value = ''
  if (Array.isArray(data.columns) && data.columns.length > 0) {
    columnList.value = data.columns.map(column => ({
      title: column.title,
      contents: column.contents?.length ? Array(column.contents.length).fill('') : []
    }))
  }
}

const initStudentAnswer = (answer: StrategyMapData) => {
  mainTitle.value = answer.mainTitle || ''
  columnList.value = answer.columns || []
  if (mainTitle.value || columnList.value.every(column => column.contents.every(content => content))) {
    draggingIndex.value = -2
    return
  }
  if (columnList.value.every(column => column.title)) {
    const index = columnList.value.findIndex(column => column.contents.every(content => content))
    draggingIndex.value = index === -1 ? columnList.value.length - 1 : index === 0 ? -2 : index - 1
  } else {
    draggingIndex.value = -1
  }
}

onMounted(() => {
  options.value = props.taskDetail.taskOptions ? JSON.parse(props.taskDetail.taskOptions) : []

  if (props.taskDetail.stuAnswer) {
    try {
      const savedData = JSON.parse(props.taskDetail.stuAnswer) as StrategyMapData
      if (savedData && savedData.columns) {
        initStudentAnswer(savedData)
      }
    } catch (e) {
      console.error('Error parsing student answer:', e)
    }
  } else {
    const initContent = props.taskDetail.initContent ? JSON.parse(props.taskDetail.initContent) : {}
    initMapLayout(initContent)
  }

  // 添加全局的 dragend 事件监听
  document.addEventListener('dragend', handleDragEnd)
})

// 组件卸载时移除事件监听
onUnmounted(() => {
  document.removeEventListener('dragend', handleDragEnd)
})

const getStuAnswer = () => {
  const answer: StrategyMapData = {
    mainTitle: mainTitle.value,
    columns: columnList.value
  }
  return Promise.resolve(JSON.stringify(answer))
}

defineExpose({
  getStuAnswer
})

watch(() => props.taskDetail, (newVal) => {
  if (newVal.stuAnswer) {
    try {
      const savedData = JSON.parse(props.taskDetail.stuAnswer) as StrategyMapData
      if (savedData && savedData.columns) {
        initStudentAnswer(savedData)
      }
    } catch (e) {
      console.error('Error parsing student answer:', e)
    }
  }
})

</script>

<style scoped lang="scss">
.point-control {
  @include flex(row, flex-start, flex-start, nowrap);
  height: 100%;
  gap: 16px;

  .base-content {
    height: 100%;
    gap: 20px;
    @include flex(column, flex-start, flex-start, nowrap);
    position: relative;

    .content-wrapper {
      width: 100%;
      flex: 1;
      min-height: 0;
      padding: 0 30px;
      background: url('@/assets/images/train/bg-light-task.png') no-repeat bottom center;
      background-size: 100% auto;
    }
  }

  .options-wrapper {
    width: 240px;
    height: 100%;
  }

  .content {
    min-width: 0;
    flex: 1;
    background-image: linear-gradient(to bottom, #fff 0%, rgba(255, 255, 255, 0.5) 100%);
    box-shadow: 0px 2px 4px 0px rgba(120, 147, 187, 0.15);
    border-radius: 6px;
    border: solid 1px #ffffff;
    overflow: hidden;
  }

  .map-wrapper {
    @include flex(column, flex-start, stretch, nowrap);

    .main-title-area {
      @include flex(row, center, center, nowrap);
      margin-bottom: 20px;
    }

    .column-item {
      position: relative;
      background-image: linear-gradient(to right, #cad4f8 53%, rgba(255, 255, 255, 0) 0%);
      background-position: top;
      background-size: 8px 1px;
      background-repeat: repeat-x;
    }

    .column-content {
      --gap: 24px;
      @include flex(row, flex-start, center, nowrap);

      .column-title {
        margin-top: var(--gap);
        margin-bottom: var(--gap);
        margin-right: 54px;
      }

      .column-content-area {

        flex: 1;
        @include flex(row, center, center, nowrap);
        gap: 84px;
      }
    }

    .column-content-item {
      position: relative;
      height: 88px;
      margin-top: var(--gap);
      margin-bottom: var(--gap);
      @include flex(row, center, center, nowrap);

      &::before {
        content: '';
        display: inline-block;
        width: 22px;
        height: 29px;
        position: absolute;
        top: calc(var(--gap) * -1);
        left: 50%;
        transform: translate(-50%, 0);
        background: center / 100% 100% no-repeat url('@/assets/images/icon/icon-arrow-1.png');
      }
    }
  }

  @media screen and (max-width: 1440px) {
    .options-wrapper {
      width: 200px;
    }

    .map-wrapper {
      .column-content {
        --gap: 16px;
      }

      .column-title {
        margin-right: 30px !important;
      }

      .column-content-area {
        gap: 30px !important;
      }
    }
  }
}
</style>
