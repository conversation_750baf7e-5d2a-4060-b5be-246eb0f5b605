<template>
  <teleport v-if="visible" to="body">
    <div class="outlook-summary-overlay">
      <div class="outlook-summary">
        <div class="outlook-summary-body">
          <div class="outlook-summary-header">
            <div class="outlook-summary-header-title">前情提要</div>
            <div class="outlook-summary-header-close" @click="handleClose">
              <el-icon size="20">
                <Close />
              </el-icon>
            </div>
          </div>
          <div class="outlook-summary-content">
            <RichPreview :str="content"></RichPreview>
          </div>
        </div>
        <div class="outlook-summary-footer">
          <el-button class="button-1" type="primary" @click="handleClose">我知道了</el-button>
          <el-button class="button-2" type="primary" @click="handleHidden">不再提示</el-button>
        </div>
      </div>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { Close } from '@element-plus/icons-vue'
import RichPreview from '@/components/global/RichPreview.vue'
defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  content: {
    type: String,
    default: ''
  }
})

const emits = defineEmits(['update:visible', 'handleHidden'])

const handleClose = () => {
  emits('update:visible', false)
}

const handleHidden = () => {
  emits('handleHidden')
}
</script>

<style scoped lang="scss">
.outlook-summary-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.outlook-summary {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 560px;
  border-radius: 8px;
  overflow: hidden;
}

.outlook-summary-body {
  width: 100%;
  min-height: 244px;
  max-height: 50vh;
  background: url('@/assets/images/train/bg-dialog.png') no-repeat center center / 100% 100%;
}

.outlook-summary-header {
  width: 100%;
  height: 42px;
  @include flex(row, space-between, center, nowrap);
  padding: 0 16px;
  color: #333333;
  font-size: 14px;

  .outlook-summary-header-title {
    font-weight: 500;
  }

  .outlook-summary-header-close {
    cursor: pointer;

    &:hover {
      color: #007aff;
    }
  }
}

.outlook-summary-content {
  padding: 0 16px;
  font-size: 14px;
  line-height: 26px;
  color: #333333;
}

.outlook-summary-footer {
  width: 100%;
  background-color: #fff;
  @include flex(row, center, center, nowrap);
  padding: 16px 0 26px;

  .el-button {
    width: 96px;
    border-radius: 4px;
  }
}

.button-1 {
  background: linear-gradient(0deg,
      #ff704b 0%,
      #ff9b6d 50%,
      #ffc68e 99%);
  background-blend-mode: normal,
    normal;
  border: solid 1px #ff885d;

  &:hover {
    background: linear-gradient(0deg,
        #ff885d 0%,
        #ff885d 99%);
  }
}

.button-2 {
  background: linear-gradient(0deg,
      #4b5ee7 0%,
      #6075f3 50%,
      #758cfe 99%),
    linear-gradient(#ffffff,
      #ffffff);
  background-blend-mode: normal,
    normal;
  border: solid 1px #586cee;

  &:hover {
    background: linear-gradient(0deg,
        #586cee 0%,
        #586cee 99%);
  }
}
</style>
