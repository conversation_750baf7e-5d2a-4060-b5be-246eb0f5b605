<template>
  <el-dialog :model-value='visible' :title='dialogType === "add" ? "新增企业" : "编辑企业"' width='800px'
    :before-close='handleClose'>
    <el-form :model="form" ref="formRef" :rules="rules" label-width="100px">
      <el-form-item label="企业名称" prop="enterpriseName">
        <el-input v-model.trim="form.enterpriseName" placeholder="请输入企业名称，20字内" maxlength="20" show-word-limit />
      </el-form-item>
      <el-form-item label="案例资料" prop="enterpriseDesc">
        <div>
          <ckeditor v-model="form.enterpriseDesc" />
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" :loading="loading" @click="submitForm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang='ts'>
import ckeditor from "@/components/ckeditor/index.vue"
import { ref, reactive, watch, type PropType } from 'vue'
import { addEditEnterprise } from '@/api/admin'
import { ElMessage } from 'element-plus'
import type { EnterpriseItem } from '@/views/admin/types'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  dialogType: {
    type: String,
    default: 'add'
  },
  editData: {
    type: Object as PropType<EnterpriseItem | null>,
    default: () => ({})
  }
})

const emits = defineEmits(['update:visible', 'success'])
const formRef = ref()
const loading = ref(false)
const form = reactive({
  enterpriseName: '',
  enterpriseDesc: '',
})

watch(() => props.visible, (newVal) => {
  if (newVal) {
    if (props.dialogType === 'edit' && props.editData) {
      form.enterpriseName = props.editData.enterpriseName
      form.enterpriseDesc = props.editData.enterpriseDesc
    }
    formRef.value?.clearValidate()
  }
})

const rules = {
  enterpriseName: [
    { required: true, message: '请输入企业名称', trigger: 'blur' },
  ],
}

const handleClose = () => {
  formRef.value?.resetFields()
  // 重置表单数据
  Object.assign(form, {
    enterpriseName: '',
    enterpriseDesc: '',
  })
  emits('update:visible', false)
}

const submitForm = async () => {
  if (!formRef.value) return
  try {
    await formRef.value.validate()
    loading.value = true

    const { data: { code, msg } } = await addEditEnterprise({ ...form, enterpriseId: props.editData?.enterpriseId });
    if (code === 200) {
      ElMessage.success('保存成功')
      emits('success', form)
      handleClose()
    } else {
      ElMessage.error(msg || '保存失败')
    }
  } catch (error) {
    console.log('表单验证失败:', error)
  } finally {
    loading.value = false // 关闭加载状态
  }
}
</script>

<style scoped lang='scss'></style>
