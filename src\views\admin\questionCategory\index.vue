<template>
  <div class="list">
    <div class="teacher-main">
      <!-- 搜索栏 -->
      <div class="list-header">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="分类名称：">
            <el-input v-model.trim="searchForm.categoryName" placeholder="请输入" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="getTableList()">查询
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="list-body">
        <!-- 操作按钮 -->
        <div class="list-body-toolbar clearfix">
          <el-button type="primary" @click="handleAddNew">新增分类</el-button>
        </div>
        <!-- 表格 -->
        <el-table :data="tableData" v-loading="loading">
          <el-table-column label="序号" type="index" width="80" />
          <el-table-column prop="categoryName" label="分类名称" />
          <el-table-column prop="categoryName" label="图标">
            <template #default="{ row }">
              <el-image :src="iconMap[row.categoryIcon]" style="width: 20px; height: 20px;" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template #default="{ row }">
              <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
              <el-button type="danger" link @click="handleSingleDelete(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 新增/编辑弹窗 -->
      <AddCategory v-model:visible="addVisible" :dialogType="dialogType" :editData="editData" @success="getTableList" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Search } from "@element-plus/icons-vue";
import * as api from '@/api/admin'
import { onMounted, ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import AddCategory from './components/AddCategory.vue'
import type { CategoryItem, ImageModule } from '@/views/admin/types'


const imagesList: Record<string, ImageModule> = import.meta.glob('../../../assets/images/taskIcon/*.png', { eager: true })
const iconMap: Record<string, string> = Object.keys(imagesList).reduce((acc, rawKey: string) => {
  const key = rawKey.split('/').pop()?.split('.')[0].split('-').pop() || '';
  acc[key] = imagesList[rawKey].default
  return acc
}, {} as Record<string, string>)

const tableData = ref<CategoryItem[]>([])
const loading = ref(false)
const searchForm = ref({
  categoryName: ''
})

const getTableList = async () => {
  loading.value = true;
  const params: Record<string, string> = {};
  if (searchForm.value.categoryName) {
    params.categoryName = searchForm.value.categoryName
  }
  const { data: { code, msg, data } } = await api.getCategoryList(params);
  loading.value = false
  if (code === 200) {
    tableData.value = data;
  } else {
    ElMessage.error(msg)
  }
}

// 处理删除
const handleSingleDelete = (row: CategoryItem) => {
  const params = {
    categoryId: row.categoryId,
  };
  handleDelete(params);
}

const handleDelete = (params: { categoryId: string }, isBatch = false) => {
  ElMessageBox.confirm(`你确定要${isBatch ? "批量" : ""}删除吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      loading.value = true;
      const { data: { code, msg } } = await api.delCategory(params);
      loading.value = false
      if (code === 200) {
        ElMessage.success("删除成功！");
        getTableList();
      } else {
        ElMessage.error(msg)
      }
    })
    .catch(() => { });
}

const addVisible = ref(false);
const dialogType = ref<'add' | 'edit'>("add");
const editData = ref<CategoryItem | null>(null);

// 处理新增
const handleAddNew = () => {
  dialogType.value = "add"
  editData.value = null
  addVisible.value = true
}
// 处理编辑
const handleEdit = (row: CategoryItem) => {
  editData.value = row
  dialogType.value = "edit"
  addVisible.value = true
}

onMounted(() => {
  getTableList();
});
</script>

<style scoped lang="scss">
.list {

  .w-200 {
    width: 200px;
  }

  .list-header {
    padding: 20px 20px 0;
    background-color: #fff;
  }

  .list-body {
    background-color: #fff;
    margin: 20px;
    padding: 20px;
  }

  .list-body-toolbar {
    border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
    padding-bottom: 5px;
  }
}
</style>
