<template>
  <div class="case-materials">
    <div class="section-title-light">
      <div class="title-label">实训资料</div>
    </div>
    <div class="content-wrapper">
      <el-scrollbar height="100%">
        <RichPreview class="case-materials-content" :str="content" />
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import RichPreview from '@/components/global/RichPreview.vue';
import type { TaskDetailItem } from '../../types';
import { computed } from 'vue';

const props = defineProps<{
  taskDetail: TaskDetailItem | null
}>()

const content = computed(() => props.taskDetail?.taskNote || '')
</script>

<style scoped lang="scss">
.case-materials {
  width: 100%;
  max-width: 330px;
  height: 100%;
  background-image: linear-gradient(to bottom, #fff 0%, rgba(255, 255, 255, 0.5) 100%);
  box-shadow: 0px 2px 4px 0px rgba(120, 147, 187, 0.15);
  border-radius: 6px;
  border: solid 1px #ffffff;
  overflow: hidden;
  color: #313052;
  @include flex(column, flex-start, flex-start, nowrap);

  .content-wrapper {
    min-height: 0;
    flex: 1;
  }

  .case-materials-content {
    padding: 0 15px;
  }
}
</style>
