import $http from "../http"
import appSettings from "../settings"
const { webAPI } = appSettings

interface Params {
  [key: string]: any
}

//实训列表
export const getList = (params: Params) => {
  return $http({
    url: webAPI + '/ninBcTrain/getList',
    method: 'get',
    params
  })
}

//实训题目列表
export const getTrainQuestionList = (params: Params) => {
  return $http({
    url: webAPI + '/ninBcQuestion/getTrainQuestionList',
    method: 'get',
    params
  })
}
//获取该实训下的题目
export const getQuestionById = (params: Params) => {
  return $http({
    url: webAPI + '/ninBcTrain/getQuestionById',
    method: 'get',
    params
  })
}
//新增实训
export const addTrain = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcTrain/addTrain',
    method: 'post',
    data
  })
}
//移除题目检测接口
export const delQuestionById = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcTrain/delQuestionById',
    method: 'post',
    data
  })
}
//删除实训
export const delTrain = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcStuTrain/delTrain',
    method: 'post',
    data
  })
}

//获取该实训下的学生列表
export const getStudentList = (params: Params) => {
  return $http({
    url: webAPI + '/ninBcTrain/getStudentList',
    method: 'get',
    params
  })
}
//添加实训下的学生
export const addTrainStudent = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcTrain/addTrainStudent',
    method: 'post',
    data
  })
}
//删除实训下的学生
export const delTrainStudent = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcTrain/delTrainStudent',
    method: 'post',
    data
  })
}
//获取该实训下的学生成绩
export const getStudentTrainList = (params: Params) => {
  return $http({
    url: webAPI + '/ninBcTrain/getStudentTrainList',
    method: 'get',
    params
  })
}
//重置学生做题
export const updateStuAnswer = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcTrain/updateStuAnswer',
    method: 'post',
    data
  })
}
//重置该实训下的学生做题记录
export const updateTrainStuAnswer = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcTrain/updateTrainStuAnswer',
    method: 'post',
    data
  })
}
//学生启用功能
export const stuStartUp = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcTrain/stuStartUp',
    method: 'post',
    data
  })
}
//班级学生列表
export const getClassStudentList = (params: Params) => {
  return $http({
    url: webAPI + '/ninBcTrain/getClassStudentList',
    method: 'get',
    params
  })
}
//查询该实训下班级
export const getClassByTrain = (params: Params) => {
  return $http({
    url: webAPI + '/ninBcTrain/getClassByTrain',
    method: 'get',
    params
  })
}
