<template>
  <div class="business-flow">
    <div class="business-flow-main">
      <div class="graph-wrapper">
        <div class="graph" ref="graphRef"></div>
      </div>
    </div>
    <!-- 添加编辑弹窗 -->
    <el-dialog v-model="editDialogVisible" title="编辑节点" width="550px" :close-on-click-modal="false"
      @close="handleEditDialogClose">
      <el-form :model="editForm" label-width="80px">
        <el-form-item label="是否预设">
          <el-switch v-model="editForm.isPreSet" />
        </el-form-item>
        <el-form-item label="节点名称">
          <el-input v-model="editForm.name" placeholder="请输入节点名称" maxlength="50" show-word-limit
            @keyup.enter="handleEditConfirm" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleEditConfirm">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick, onBeforeUnmount } from 'vue'
import { ElMessage } from 'element-plus'
import { Graph } from '@antv/x6'
import type { Cell, Node } from '@antv/x6'
import type { OptionItem } from '../../../types'
import Hierarchy from '@antv/hierarchy'
import { Keyboard } from '@antv/x6-plugin-keyboard'
import { Selection } from '@antv/x6-plugin-selection'
import { registerMindMap } from '@/components/flow/shapeConfig/mindMap'

const props = defineProps({
  initContent: {
    type: String,
    default: ''
  },
  taskOptions: {
    type: String,
    default: ''
  }
})

interface MindMapData {
  id: string
  type: 'topic' | 'topic-branch' | 'topic-child'
  label: string
  width: number
  height: number
  data?: Record<string, any>
  children?: MindMapData[]
}

interface HierarchyResult {
  id: string
  x: number
  y: number
  data: MindMapData
  children?: HierarchyResult[]
}

// X6 相关接口定义
interface X6CellAttrs {
  title?: {
    text: string
  }
  [key: string]: unknown
}

interface X6Node {
  id: string
  shape: string
  type?: 'topic' | 'topic-branch' | 'topic-child'
  label?: string
  size?: {
    width: number
    height: number
  }
  attrs?: X6CellAttrs
  data?: {
    [key: string]: unknown
  }
}

interface X6Edge {
  id: string
  shape: string
  source: {
    cell: string
    [key: string]: unknown
  }
  target: {
    cell: string
    [key: string]: unknown
  }
}

type X6JSON = (X6Node | X6Edge)[]


// 声明原始数据结构
const rawData: MindMapData = {
  id: '1',
  type: 'topic',
  label: '价值链活动',
  width: 85,
  height: 86,
  data: {
    name: '价值链活动',
  },
}

const graphRef = ref<HTMLDivElement | null>()
const graph = ref<Graph>()
const data = ref<MindMapData>(rawData)

// 初始化图形
const initGraph = async () => {
  await nextTick();
  if (!graphRef.value) return
  graph.value = new Graph({
    container: graphRef.value,
    background: {
      color: '#F5F7FA',
    },
    connecting: {
      snap: {
        radius: 20,
      },
      allowBlank: false,
      allowLoop: false,
      highlight: false,
      sourceAnchor: {
        name: "center",
      },
      targetAnchor: "center",
      connectionPoint: "anchor",
      router: "manhattan",
      validateMagnet() {
        return false
      },
      validateConnection() {
        return false
      },
    },
    panning: {
      enabled: true,
    },
    interacting: {
      nodeMovable: false, //节点拖动
    },
  })
  graph.value.use(new Selection())
  graph.value.use(new Keyboard())

  registerEvent()
  if (props.initContent) {
    data.value = buildTree(JSON.parse(props.initContent))
  }
  render()
  graph.value.centerContent()
}

const registerEvent = () => {
  if (!graph.value) return
  graph.value.on('node:selected', ({ node }) => {
    if (node.shape !== 'topic') {
      node?.addTools({
        name: 'button-remove',
        args: {
          fill: '#666',
          x: '100%',
          y: 0,
          offset: {
            x: 5,
            y: 0
          },
        }
      })
    }
  })
  graph.value.on('node:unselected', ({ node }) => {
    if (node.shape !== 'topic') {
      node?.removeTools()
    }
  })
  graph.value.on('node:removed', ({ node }) => {
    if (removeNode(node.id)) {
      render()
    }
  })

  graph.value.on('node:dblclick', ({ node }) => {
    console.log(node)
    currentEditNode.value = node
    editForm.value.name = node.data?.name || node.attr('text/text') || ''
    editForm.value.isPreSet = node.data?.isPreSet || false
    editDialogVisible.value = true
  })
  graph.value.on('add:topic', ({ node }: { node: Node }) => {
    const { id } = node
    const type = node.prop('type')
    if (addChildNode(id, type)) {
      render()
    }
  })
  graph.value.bindKey(['backspace', 'delete'], () => {
    const selectedNodes = graph.value!.getSelectedCells().filter((item) => item.isNode())
    if (selectedNodes.length) {
      const { id } = selectedNodes[0]
      if (removeNode(id)) {
        render()
      }
    }
  })

  graph.value.bindKey('tab', (e) => {
    e.preventDefault()
    const selectedNodes = graph.value!.getSelectedCells().filter((item) => item.isNode())
    if (selectedNodes.length) {
      const node = selectedNodes[0]
      const type = node.prop('type')
      if (addChildNode(node.id, type)) {
        render()
      }
    }
  })
}

// 将X6 JSON数据转换为树形结构
const buildTree = (jsonData: X6JSON | null): MindMapData => {
  if (!jsonData?.length) {
    return rawData;
  }

  // 提取所有节点和边
  const nodes = jsonData.filter((cell) => cell.shape !== 'mindmap-edge') as X6Node[];
  const edges = jsonData.filter((cell) => cell.shape === 'mindmap-edge') as X6Edge[];

  // 创建节点映射
  interface MindMapDataWithFlag extends MindMapData {
    isChild?: boolean
  }

  const nodeMap = new Map<string, MindMapDataWithFlag>();
  nodes.forEach((node) => {
    nodeMap.set(node.id, {
      id: node.id,
      type: node.type || 'topic',
      label: node.label || node.attrs?.title?.text || '',
      width: node.size?.width || 100,
      height: node.size?.height || 40,
      data: node.data || {
        name: node.attrs?.title?.text || node.label || '',
      },
      children: []
    });
  });

  // 根据边构建父子关系
  edges.forEach((edge) => {
    const sourceId = edge.source.cell;
    const targetId = edge.target.cell;

    if (nodeMap.has(sourceId) && nodeMap.has(targetId)) {
      const sourceNode = nodeMap.get(sourceId)!;
      const targetNode = nodeMap.get(targetId)!;

      // 添加到子节点列表
      sourceNode.children!.push(targetNode);

      // 标记非根节点
      targetNode.isChild = true;
    }
  });

  // 找到根节点
  let rootNode: MindMapDataWithFlag | undefined;
  for (const node of nodeMap.values()) {
    if (!node.isChild) {
      rootNode = node;
      break;
    }
  }

  // 如果没有找到根节点，返回默认数据
  if (!rootNode) {
    return rawData;
  }

  // 移除临时标记
  for (const node of nodeMap.values()) {
    delete node.isChild;
  }

  return rootNode;
}

const render = () => {
  if (!graph.value) return

  const result: HierarchyResult = Hierarchy.mindmap(data.value, {
    direction: 'H',
    getHeight(d: MindMapData) {
      return d.height
    },
    getWidth(d: MindMapData) {
      return d.width
    },
    getHGap() {
      return 140
    },
    getVGap() {
      return 40
    },
    getSide: () => {
      return 'right'
    },
  })
  const cells: Cell[] = []
  const traverse = (hierarchyItem: HierarchyResult) => {
    if (!graph.value) return
    if (hierarchyItem) {
      const { data, children } = hierarchyItem
      cells.push(
        graph.value.createNode({
          id: data.id,
          shape: data.type,
          x: hierarchyItem.x,
          y: hierarchyItem.y,
          width: data.width,
          height: data.height,
          label: data.label,
          type: data.type,
          data: data.data,
          attrs: {
            title: {
              text: data.data?.name || data.label,
            },
          },
        }),
      )
      if (children) {
        children.forEach((item: HierarchyResult) => {
          const { id } = item
          if (!graph.value) return
          const parentType = hierarchyItem.data.type
          const lineStroke = parentType === 'topic' ? '#6a7ef6' : parentType === 'topic-branch' ? '#2cd0ce' : '#ff8460'
          cells.push(
            graph.value.createEdge({
              shape: 'mindmap-edge',
              // connector: { name: 'smooth' },
              attrs: {
                line: {
                  stroke: lineStroke,
                },
              },
              source: {
                cell: hierarchyItem.id,
                port: 'port-right'
              },
              target: {
                cell: id,
                port: 'port-right'
              },
            }),
          )
          traverse(item)
        })
      }
    }
  }
  traverse(result)
  graph.value.resetCells(cells)
}

const findItem = (
  obj: MindMapData,
  id: string,
): {
  parent: MindMapData | null
  node: MindMapData | null
} | null => {
  if (obj.id === id) {
    return {
      parent: null,
      node: obj,
    }
  }
  const { children } = obj
  if (children) {
    for (let i = 0, len = children.length; i < len; i += 1) {
      const res = findItem(children[i], id)
      if (res) {
        return {
          parent: res.parent || obj,
          node: res.node,
        }
      }
    }
  }
  return null
}

const generateId = () => {
  return `${new Date().getTime()}-${Math.random().toString(36).substring(2, 15)}`
}

const addChildNode = (id: string, type: 'topic' | 'topic-branch' | 'topic-child' = 'topic-branch') => {
  const res = findItem(data.value, id)
  const dataItem = res?.node
  if (dataItem) {
    let item: MindMapData | null = null
    const length = dataItem.children ? dataItem.children.length : 0
    if (type === 'topic') {
      item = {
        id: generateId(),
        type: 'topic-branch',
        label: `分支主题${length + 1}`,
        width: 54,
        height: 55,
        data: {
          name: `分支主题${length + 1}`,
        },
      }
    } else if (type === 'topic-branch' || type === 'topic-child') {
      item = {
        id: generateId(),
        type: 'topic-child',
        label: `子主题${length + 1}`,
        width: 144,
        height: 32,
        data: {
          name: `子主题${length + 1}`,
        },
      }
    }
    if (item) {
      if (dataItem.children) {
        dataItem.children.push(item)
      } else {
        dataItem.children = [item]
      }
      return item
    }
  }
  return null
}

const removeNode = (id: string) => {
  const res = findItem(data.value, id)
  const dataItem = res?.parent
  if (dataItem && dataItem.children) {
    const { children } = dataItem
    const index = children.findIndex((item) => item.id === id)
    children.splice(index, 1)
    console.log(data.value)
    return true
  }
  return null
}


// 编辑弹窗相关
const editDialogVisible = ref(false)
const editForm = ref({
  name: '',
  isPreSet: false
})
const currentEditNode = ref<Node | null>(null)


// 处理编辑确认
const handleEditConfirm = () => {
  if (!currentEditNode.value) return
  const newName = editForm.value.name.trim()
  if (!newName) {
    ElMessage.warning('节点名称不能为空')
    return
  }

  currentEditNode.value.setAttrs({
    title: { text: newName },
    text: { text: newName }
  })
  currentEditNode.value.setData({
    ...currentEditNode.value.data,
    name: newName,
    isPreSet: editForm.value.isPreSet
  })

  editDialogVisible.value = false
}

// 处理弹窗关闭
const handleEditDialogClose = () => {
  editForm.value.name = ''
  editForm.value.isPreSet = false
  currentEditNode.value = null
}

onMounted(() => {
  initGraph()
  registerMindMap(Graph)
})

onBeforeUnmount(() => {
  // 清理tooltip
  const tooltip = document.querySelector('.node-tooltip')
  if (tooltip) {
    document.body.removeChild(tooltip)
  }
})

const validate = () => {
  return new Promise((resolve, reject) => {
    const jsonData = graph.value?.toJSON();
    if (!jsonData?.cells?.length) {
      ElMessage.error('思维导图不能为空')
      reject()
    }

    // 转换为树形结构并存储
    const treeData = buildTree(jsonData?.cells as X6JSON);
    // 更新当前数据引用的值
    Object.assign(data.value, treeData);
    const options: OptionItem[] = []
    const allOptions: OptionItem[] = []
    jsonData?.cells?.forEach((item) => {
      if ((item.shape as string).indexOf('edge') === -1) {
        allOptions.push({
          id: item.id as string,
          name: item.data!.name as string
        })
        if (!item.data?.isPreSet) {
          options.push({
            id: item.id as string,
            name: item.data!.name as string
          })
        }
      }
    })

    const filterOptions = Array.from(new Set(allOptions.map((item) => item.name)))
    if (filterOptions.length !== allOptions.length) {
      ElMessage.error('节点名称不能重复')
      reject()
    }
    resolve({
      initContent: JSON.stringify(jsonData?.cells),
      taskOptions: JSON.stringify(options),
    })
  })
}


defineExpose({
  validate
})
</script>

<style scoped lang="scss">
.business-flow {
  @include flex(row, flex-start, flex-start, nowrap);
  gap: 20px;
  position: relative;
  min-height: 600px;

  .business-flow-main {
    height: 600px;
    flex: 1;
    min-width: 0;
    @include flex(column, flex-start, stretch, nowrap);
    gap: 16px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 14px;
    border-radius: 6px;
  }

  .graph-wrapper {
    min-height: 0;
    flex: 1;

    .graph {
      height: 100%;
    }

    :deep {
      .topic-image {
        visibility: hidden;
        cursor: pointer;
      }

      .x6-node:hover .topic-image {
        visibility: visible;
      }

      .x6-node-selected rect {
        stroke-width: 2px;
      }
    }
  }
}


:deep(.el-dialog__body) {
  padding-top: 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
