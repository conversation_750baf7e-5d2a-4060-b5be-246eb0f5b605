
import type { RootState } from "@/store/types"
const state: RootState = {
  token: "",
  theme: "light"
}

const mutations = {
  SET_TOKEN: (state: RootState, data: string) => {
    state.token = data
  },
  USER_LOGOUT: (state: RootState) => {
    state.token = ""
  },
  SET_THEME: (state: RootState, data: string) => {
    state.theme = data
  }
}

const actions = {
  userLogout: ({ commit }: { commit: any }) => {
    commit('USER_LOGOUT');
    commit('user/CLEAR_USERINFO');
    window.localStorage.clear()
  }
}

export default {
  // namespaced: true,
  state,
  mutations,
  actions
}
