<template>
  <div class="header">
    <div class="left">
      <img src="@/assets/images/logo.png">
      <span class="title">商业画布实训平台</span>
    </div>
    <div class="center"></div>
    <!-- <div class="right">
      <el-button type="primary">登录</el-button>
    </div> -->
  </div>
</template>

<script setup lang="ts">

</script>

<style scoped lang="scss">
.header {
  display: flex;
  align-items: center;
  height: 100%;

  &>* {
    flex-shrink: 0;
  }

  .left {
    margin-left: 60px;
    display: flex;
    align-items: center;
    gap: 24px;
    line-height: 30px;

    .title {
      color: #ffffff;
      font-size: 24px;
      font-weight: 700;
    }
  }

  .center {
    flex: 1;
  }

  .right {
    position: relative;
    padding-left: 6px;
    padding-right: 70px;

    &::before {
      content: "";
      position: absolute;
      left: 0;
      top: 0;
      width: 8px;
      height: 36px;
      background: rgba($color: #fff, $alpha: .15);
      transform: skewX(-17deg);
    }

    &::after {
      content: "";
      position: absolute;
      right: -30px;
      top: 0;
      width: 100px;
      height: 36px;
      background: rgba($color: #fff, $alpha: .15);
      transform: skewX(-17deg);
    }

    :deep(.el-button) {
      width: 250px;
      height: 36px;
      flex-shrink: 0;
      background: url('@/assets/images/btn/bg-btn-4.png') no-repeat center;
      background-size: 100%;
      border: 0;
      border-radius: 0;
      padding: 0;
      text-align: center;

      span {
        color: #001e22;
        font-size: 20px;
        font-family: "YouSheBiaoTiHei";
      }
    }
  }
}
</style>
