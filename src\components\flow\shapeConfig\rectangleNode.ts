import { Shape } from "@antv/x6"
import { lightPorts } from "./staticConfig"

class rectangleNode extends Shape.Rect {
}

rectangleNode.config({
  width: 110,
  height: 34,
  zIndex: 100,
  markup: [
    {
      tagName: 'rect',
      selector: 'main',
      attrs: {
        fill: '#677cf6'
      }
    },
    {
      tagName: 'title',
      selector: 'title',
    },
    {
      tagName: 'text',
      selector: 'label',
      attrs: {
        fill: '#000',
        'font-size': 14,
        'text-anchor': 'middle',
        'pointer-events': 'none',
      }
    }
  ],
  data: {
    fontColor: '#ffffff',
    backgroundColor: '#677cf6',
    borderColor: '#31449e'
  },
  attrs: {
    main: {
      stroke: '#31449e',
      fill: '#677cf6',
      refWidth: '100%',
      refHeight: '100%'
    },
    title: {
      text: '',
    },
    label: {
      text: '',
      fill: '#FFFFFF',
      strokeWidth: 2,
      fontSize: 14,
      textWrap: {
        width: '90%', // 宽度减少 10px
        height: '80%', // 高度为参照元素高度的一半
        ellipsis: true, // 文本超出显示范围时，自动添加省略号
        breakWord: true, // 是否截断单词
      },
    }
  },
  ports: { ...lightPorts }
})
export default rectangleNode
