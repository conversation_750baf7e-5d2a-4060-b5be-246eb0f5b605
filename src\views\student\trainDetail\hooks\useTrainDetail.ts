import { ref, watch, computed } from 'vue'
import { useRout<PERSON>, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import * as api from '@/api/student'
import type { TaskItem, TaskListItem, TaskDetailItem } from '../types'

export const useTrainDetail = () => {
  const route = useRoute()
  const router = useRouter()
  const trainId = ref(route.query.trainId as string)
  const practiceOrTrialId = ref(route.query.practiceOrTrialId as string || '0')

  const apiExtraParams = computed(() => {
    const { query } = route
    const params: Record<string, string> = {
      trainId: trainId.value,
      practiceOrTrialId: practiceOrTrialId.value,
    }
    if (query.studentId) {
      params.studentId = query.studentId as string
    }
    if (query.teaClassId) {
      params.teaClassId = query.teaClassId as string
    }
    if (query.expId) {
      params.expId = query.expId as string
    }
    return params
  })

  const loading = ref(false)
  const taskList = ref<TaskListItem[]>([])
  const currentTask = ref<TaskItem | null>(null)
  const taskDetail = ref<TaskDetailItem | null>(null)
  const componentRef = ref<{ getStuAnswer: () => Promise<string> } | null>(null)

  const disabled = computed(() => {
    return taskDetail.value?.showSubmit === 0 || route.query.finish === '1'
  })

  const getStuTrainContent = async () => {
    if (currentTask.value) return
    try {
      loading.value = true
      const params: Record<string, string> = {
        ...apiExtraParams.value
      }
      const { data: { code, msg, data } } = await api.getStuTrainContent(params);
      if (code === 200) {
        taskList.value = (data as TaskListItem[]).filter(item => item.questionList.length)
        if (taskList.value?.length === 1 && taskList.value[0]?.questionList?.length === 1) {
          currentTask.value = taskList.value[0].questionList[0]
          getStuTrainDetail()
        }
      } else {
        ElMessage.error(msg)
      }
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }

  const getStuTrainDetail = async () => {
    try {
      loading.value = true
      const params: Record<string, string> = {
        questionId: currentTask.value!.questionId,
        ...apiExtraParams.value
      }
      const { data: { code, msg, data } } = await api.getStuTrainDetail(params);
      if (code === 200) {
        taskDetail.value = data
        currentTask.value!.ifFinish = taskDetail.value!.ifFinish
      } else {
        ElMessage.error(msg)
      }
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }

  const hiddenDialog = async (type: string) => {
    if (type === 'desc') {
      try {
        loading.value = true
        const params: Record<string, string> = {
          questionId: taskDetail.value!.questionId,
          ...apiExtraParams.value
        }
        const { data: { code } } = await api.stuQuestionHint(params);
        if (code === 200) {
          taskDetail.value!.questionHint = 0
        }
      } catch (error) {
        console.log(error)
      } finally {
        loading.value = false
      }
    }
  }

  const handleSave = async () => {
    try {
      loading.value = true
      const stuAnswer = await componentRef.value?.getStuAnswer()
      if (!stuAnswer) return
      const params: Record<string, string> = {
        questionId: taskDetail.value!.questionId,
        stuAnswer,
        ...apiExtraParams.value
      }
      const { data: { code, msg } } = await api.saveStuAnswer(params);
      if (code === 200) {
        ElMessage.success(msg)
        getStuTrainDetail()
      } else {
        ElMessage.error(msg)
      }
    } catch (error) {
      console.log(error)
    } finally {
      loading.value = false
    }
  }

  const handleSubmit = async () => {
    ElMessageBox.confirm(
      '提交后将无法修改，是否确认提交？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    ).then(async () => {
      try {
        loading.value = true
        const params: Record<string, string> = {
          ...apiExtraParams.value
        }
        const { data: { code, msg } } = await api.submitStuAnswer(params);
        if (code === 200) {
          ElMessage.success(msg)
          router.replace({
            path: route.path,
            query: {
              ...route.query,
              finish: '1'
            }
          })
        } else {
          ElMessage.error(msg)
        }
      } catch (error) {
        console.log(error)
      } finally {
        loading.value = false
      }
    }).catch(() => { })
  }

  const handleReset = () => {
    ElMessageBox.confirm('确定要重置吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      try {
        loading.value = true
        const params: Record<string, string> = {
          questionId: taskDetail.value!.questionId,
          ...apiExtraParams.value
        }
        const { data: { code, msg } } = await api.resetStuAnswer(params);
        if (code === 200) {
          ElMessage.success(msg)
          getStuTrainDetail()
        } else {
          ElMessage.error(msg)
        }
      } catch (error) {
        console.log(error)
      } finally {
        loading.value = false
      }
    }).catch(() => {
      console.log('取消')
      loading.value = false
    })
  }

  watch(() => route.query, (val) => {
    if (val) {
      getStuTrainContent()
    }
  }, { immediate: true })

  return {
    loading,
    taskList,
    currentTask,
    taskDetail,
    disabled,
    componentRef,
    getStuTrainDetail,
    handleSave,
    handleSubmit,
    hiddenDialog,
    apiExtraParams,
    handleReset
  }
}
