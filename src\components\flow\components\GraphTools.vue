<template>
  <div class="graph-tools">
    <div class="tool-group" v-if="tools.includes('undo') || tools.includes('redo')">
      <el-tooltip content="撤销" placement="bottom">
        <div class="tool-item" @click="handleUndo" v-if="tools.includes('undo')">
          <el-icon>
            <SvgIcon icon-name="undo" />
          </el-icon>
        </div>
      </el-tooltip>
      <el-tooltip content="取消撤销" placement="bottom">
        <div class="tool-item" @click="handleRedo" v-if="tools.includes('redo')">
          <el-icon>
            <SvgIcon icon-name="redo" />
          </el-icon>
        </div>
      </el-tooltip>
    </div>
    <div class="tool-group"
      v-if="tools.includes('zoom-out') || tools.includes('zoom-in') || tools.includes('reset-zoom')">
      <el-tooltip content=" 缩小" placement="bottom">
        <div class="tool-item" @click="handleZoomOut" v-if="tools.includes('zoom-out')">
          <el-icon>
            <ZoomOut />
          </el-icon>
        </div>
      </el-tooltip>
      <el-tooltip content="放大" placement="bottom">
        <div class="tool-item" @click="handleZoomIn" v-if="tools.includes('zoom-in')">
          <el-icon>
            <ZoomIn />
          </el-icon>
        </div>
      </el-tooltip>
      <el-tooltip content="恢复缩放" placement="bottom">
        <div class="tool-item" @click="handleResetZoom" v-if="tools.includes('reset-zoom')">
          <el-icon>
            <Refresh />
          </el-icon>
        </div>
      </el-tooltip>
    </div>
    <div class="tool-group" v-if="tools.includes('info')">
      <el-popover width=" 180" placement="top" trigger="hover" :show-after="0" :hide-after="0"
        popper-class="shortcut-tooltip">
        <div class='shortcut-list'>
          <div class='shortcut-item' v-if="tools.includes('copy')">
            <span class='key'>Ctrl</span>
            <span class='key'>C</span>
            <span class='desc'>复制</span>
          </div>
          <div class='shortcut-item' v-if="tools.includes('paste')">
            <span class='key'>Ctrl</span>
            <span class='key'>V</span>
            <span class='desc'>粘贴</span>
          </div>
          <div class='shortcut-item' v-if="tools.includes('cut')">
            <span class='key'>Ctrl</span>
            <span class='key'>X</span>
            <span class='desc'>剪切</span>
          </div>
          <div class='shortcut-item' v-if="tools.includes('undo')">
            <span class='key'>Ctrl</span>
            <span class='key'>Z</span>
            <span class='desc'>撤销</span>
          </div>
          <div class='shortcut-item' v-if="tools.includes('redo')">
            <span class='key'>Ctrl</span>
            <span class='key'>Shift</span>
            <span class='key'>Z</span>
            <span class='desc'>取消撤销</span>
          </div>
          <div class='shortcut-item' v-if="tools.includes('zoom-out')">
            <span class='key'>Ctrl</span>
            <span class='key'>滚轮</span>
            <span class='desc'>放大/缩小</span>
          </div>
        </div>
        <template #reference>
          <div class="tool-item">
            <el-icon>
              <SvgIcon icon-name="keyBoard" />
            </el-icon>
          </div>
        </template>
      </el-popover>
    </div>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from "@/components/global/SvgIcon.vue";
import { ZoomOut, ZoomIn, Refresh } from '@element-plus/icons-vue'

interface IProps {
  tools?: string[]
}

withDefaults(defineProps<IProps>(), {
  tools: () => ['undo', 'redo', 'zoom-out', 'zoom-in', 'reset-zoom', 'info']
})

const emits = defineEmits(['undo', 'redo', 'zoom-out', 'zoom-in', 'reset-zoom'])

const handleUndo = () => {
  emits('undo')
}

const handleRedo = () => {
  emits('redo')
}

const handleZoomOut = () => {
  emits('zoom-out')
}

const handleZoomIn = () => {
  emits('zoom-in')
}

const handleResetZoom = () => {
  emits('reset-zoom')
}
</script>

<style scoped lang="scss">
.graph-tools {
  position: absolute;
  top: 8px;
  left: 10px;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.2);
  z-index: 100;

  .tool-group {
    display: flex;
    align-items: center;
  }

  .tool-group+.tool-group {
    border-left: 1px solid #e4e7ed;
    padding-left: 4px;
  }

  .tool-item {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;

    &:hover {
      background-color: #f5f7fa;

      svg {
        color: #758cfe;
      }
    }

    &:active {
      background-color: #e4e7ed;

      svg {
        color: #758cfe;
      }
    }

    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;

      &:hover {
        background-color: transparent;
      }
    }

    .el-icon {
      font-size: 16px;
      color: #606266;
    }
  }

  .el-divider {
    margin: 0 4px;
    height: 16px;
  }
}
</style>
<style lang="scss">
.shortcut-tooltip {
  padding: 8px;

  .shortcut-list {
    .shortcut-item {
      display: flex;
      align-items: center;
      gap: 4px;
      margin-bottom: 4px;

      &>* {
        flex-shrink: 0;
      }

      &:last-child {
        margin-bottom: 0;
      }

      .key {
        padding: 2px 4px;
        background: #f5f7fa;
        border: 1px solid #dcdfe6;
        border-radius: 3px;
        font-size: 12px;
        color: #606266;
        min-width: 20px;
        text-align: center;
      }

      .desc {
        font-size: 12px;
        color: #606266;
        margin-left: 8px;
      }
    }
  }
}
</style>
