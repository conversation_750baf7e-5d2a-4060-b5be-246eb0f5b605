import { Graph as X6Graph, Path } from '@antv/x6'
import iconAdd from '@/assets/images/icon/icon-add.png'

export const registerMindMap = (Graph: typeof X6Graph) => {
  // 中心主题或分支主题
  Graph.registerNode(
    'topic',
    {
      inherit: 'rect',
      markup: [
        {
          tagName: 'rect',
          selector: 'body',
        },
        {
          tagName: 'rect',
          selector: 'border',
        },
        {
          tagName: 'image',
          selector: 'img',
        },
        {
          tagName: 'title',
          selector: 'title',
        },
        {
          tagName: 'text',
          selector: 'label',
        },
        {
          tagName: 'defs',
          children: [
            {
              tagName: 'linearGradient',
              selector: 'gradient',
              attrs: {
                id: 'nodeGradient',
                x1: '0%',
                y1: '0%',
                x2: '0%',
                y2: '100%'
              },
              children: [
                {
                  tagName: 'stop',
                  selector: 'gradientStart',
                  attrs: {
                    offset: '2%',
                    'stop-color': '#96a8ff'
                  }
                },
                {
                  tagName: 'stop',
                  selector: 'gradientEnd',
                  attrs: {
                    offset: '98%',
                    'stop-color': '#5369f1'
                  }
                }
              ]
            }
          ]
        },
      ],
      attrs: {
        body: {
          rx: 40,
          ry: 40,
          stroke: '#4A90E2',
          fill: 'url(#nodeGradient)',
          strokeWidth: 0,
        },
        border: {
          ref: 'body',
          refWidth: '100%',
          refHeight: '100%',
          rx: 40,
          ry: 40,
          refX: 2,
          refY: 2,
          stroke: '#bfc9ff',
          fill: 'transparent',
          strokeWidth: 1,
        },
        title: {
          text: '',
        },
        label: {
          fontSize: 16,
          fill: '#FFFFFF',
          fontWeight: 'bold',
          lineHeight: 22,
          textWrap: {
            width: '80%',
            height: '60%',
            ellipsis: true,
            breakWord: true,
          },
        },
        img: {
          ref: 'body',
          refX: '50%',
          refY: '100%',
          refX2: -12,
          refY2: -2,
          width: 24,
          height: 24,
          'xlink:href': iconAdd,
          event: 'add:topic',
          class: 'topic-image',
        },
      },
      ports: {
        groups: {
          right: {
            position: 'right',
            markup: [
              {
                tagName: 'circle',
                selector: 'border',
              },
              {
                tagName: 'circle',
                selector: 'circle',
              },
            ],
            attrs: {
              border: {
                r: 11,
                magnet: true,
                stroke: '#6a7ef6',
                strokeWidth: 1,
                fill: '#fff',
              },
              circle: {
                r: 5,
                magnet: true,
                strokeWidth: 0,
                fill: '#6a7ef6',
              },
            },
          },
        },
        items: [
          {
            id: 'port-right',
            group: 'right',
            args: { dx: 20 },
          },
        ],
      },
    },
    true,
  )

  // 中心主题或分支主题
  Graph.registerNode(
    'topic-branch',
    {
      inherit: 'rect',
      markup: [
        {
          tagName: 'rect',
          selector: 'body',
        },
        {
          tagName: 'rect',
          selector: 'border',
        },
        {
          tagName: 'image',
          selector: 'img',
        },
        {
          tagName: 'title',
          selector: 'title',
        },
        {
          tagName: 'text',
          selector: 'label',
        },
        {
          tagName: 'defs',
          children: [
            {
              tagName: 'linearGradient',
              selector: 'gradient',
              attrs: {
                id: 'branchGradient',
                x1: '0%',
                y1: '0%',
                x2: '0%',
                y2: '100%'
              },
              children: [
                {
                  tagName: 'stop',
                  selector: 'gradientStart',
                  attrs: {
                    offset: '2%',
                    'stop-color': '#4fedb0'
                  }
                },
                {
                  tagName: 'stop',
                  selector: 'gradientEnd',
                  attrs: {
                    offset: '98%',
                    'stop-color': '#08b1af'
                  }
                }
              ]
            }
          ]
        },
      ],
      attrs: {
        body: {
          rx: 40,
          ry: 40,
          stroke: 'transparent',
          fill: 'url(#branchGradient)',
          strokeWidth: 0,
        },
        border: {
          ref: 'body',
          refWidth: '100%',
          refHeight: '100%',
          rx: 40,
          ry: 40,
          refX: 2,
          refY: 2,
          stroke: '#a9fffd',
          fill: 'transparent',
          strokeWidth: 1,
        },
        title: {
          text: '',
        },
        label: {
          fontSize: 14,
          fill: '#FFFFFF',
          lineHeight: 18,
          textWrap: {
            width: '75%',
            height: '80%',
            ellipsis: true,
            breakWord: true,
          },
        },
        img: {
          ref: 'body',
          refX: '50%',
          refY: '100%',
          refX2: -8,
          refY2: -2,
          width: 16,
          height: 16,
          'xlink:href': iconAdd,
          event: 'add:topic',
          class: 'topic-image',
        },
      },
      ports: {
        groups: {
          right: {
            position: 'right',
            markup: [
              {
                tagName: 'circle',
                selector: 'border',
              },
              {
                tagName: 'circle',
                selector: 'circle',
              },
            ],
            attrs: {
              border: {
                r: 11,
                magnet: true,
                stroke: '#2cd0ce',
                strokeWidth: 1,
                fill: '#fff',
              },
              circle: {
                r: 5,
                magnet: true,
                strokeWidth: 0,
                fill: '#2cd0ce',
              },
            },
          },
        },
        items: [
          {
            id: 'port-right',
            group: 'right',
            args: { dx: 20 },

          },
        ],
      },
    },
    true,
  )
  // 中心主题或分支主题
  Graph.registerNode(
    'topic-child',
    {
      inherit: 'rect',
      markup: [
        {
          tagName: 'rect',
          selector: 'body',
        },
        {
          tagName: 'rect',
          selector: 'border',
        },
        {
          tagName: 'image',
          selector: 'img',
        },
        {
          tagName: 'title',
          selector: 'title',
        },
        {
          tagName: 'text',
          selector: 'label',
        },
        {
          tagName: 'defs',
          children: [
            {
              tagName: 'linearGradient',
              selector: 'gradient',
              attrs: {
                id: 'childGradient',
                x1: '0%',
                y1: '0%',
                x2: '0%',
                y2: '100%'
              },
              children: [
                {
                  tagName: 'stop',
                  selector: 'gradientStart',
                  attrs: {
                    offset: '2%',
                    'stop-color': '#ffc18a'
                  }
                },
                {
                  tagName: 'stop',
                  selector: 'gradientEnd',
                  attrs: {
                    offset: '98%',
                    'stop-color': '#ff8460'
                  }
                }
              ]
            }
          ]
        },
      ],
      attrs: {
        body: {
          rx: 16,
          ry: 16,
          stroke: 'transparent',
          fill: 'url(#childGradient)',
          strokeWidth: 0,
        },
        border: {
          ref: 'body',
          refWidth: '100%',
          refHeight: '100%',
          rx: 16,
          ry: 16,
          refX: 2,
          refY: 2,
          stroke: '#ffe7c2',
          fill: 'transparent',
          strokeWidth: 1,
        },
        title: {
          text: '',
        },
        label: {
          fontSize: 14,
          fill: '#FFFFFF',
          lineHeight: 18,
          textWrap: {
            width: '75%',
            height: '80%',
            ellipsis: true,
            breakWord: true,
          },
        },
        img: {
          ref: 'body',
          refX: '50%',
          refY: '100%',
          refX2: -16,
          refY2: -2,
          width: 24,
          height: 24,
          'xlink:href': iconAdd,
          event: 'add:topic',
          class: 'topic-image',
        },
      },
      ports: {
        groups: {
          right: {
            position: 'right',
            markup: [
              {
                tagName: 'circle',
                selector: 'border',
              },
              {
                tagName: 'circle',
                selector: 'circle',
              },
            ],
            attrs: {
              border: {
                r: 11,
                magnet: true,
                stroke: '#ff9169',
                strokeWidth: 1,
                fill: '#fff',
              },
              circle: {
                r: 5,
                magnet: true,
                strokeWidth: 0,
                fill: '#ff9169',
              },
            },
          },
        },
        items: [
          {
            id: 'port-right',
            group: 'right',
            args: { dx: 20 },
          },
        ],
      },
    },
    true,
  )


  // 连接器
  Graph.registerConnector(
    'mindmap',
    (sourcePoint, targetPoint, routerPoints, options) => {
      // 计算水平和垂直方向的差异
      const dx = targetPoint.x - sourcePoint.x
      const dy = targetPoint.y - sourcePoint.y

      // 根据连接方向调整控制点计算
      const isRightToLeft = dx < 0

      // 计算控制点位置，考虑连接方向
      let sourceControlX, sourceControlY, targetControlX, targetControlY

      if (isRightToLeft) {
        // 当连接是从右到左时，反转控制点的水平偏移方向
        sourceControlX = sourcePoint.x - Math.abs(dx) * 0.85
        targetControlX = targetPoint.x + Math.abs(dx) * 0.83
      } else {
        // 从左到右的正常情况
        sourceControlX = sourcePoint.x + dx * 0.85
        targetControlX = targetPoint.x - dx * 0.83
      }

      // 垂直方向控制点位置，考虑垂直方向
      const isBottomToTop = dy < 0

      if (isBottomToTop) {
        // 当连接是从下到上时
        sourceControlY = sourcePoint.y - Math.abs(dy) * 0.02
        targetControlY = targetPoint.y + Math.abs(dy) * 0.01
      } else {
        // 当连接是从上到下时
        sourceControlY = sourcePoint.y + Math.abs(dy) * 0.02
        targetControlY = targetPoint.y - Math.abs(dy) * 0.01
      }

      // 对于长距离连接，适当调整控制点位置以保持曲线美观
      const distance = Math.sqrt(dx * dx + dy * dy)
      if (distance > 300) {
        const scale = 300 / distance
        sourceControlX = sourcePoint.x + (sourceControlX - sourcePoint.x) * scale
        targetControlX = targetPoint.x + (targetControlX - targetPoint.x) * scale
      }

      // 使用三次贝塞尔曲线实现连接效果
      const pathData = `
     M ${sourcePoint.x} ${sourcePoint.y}
     C ${sourceControlX} ${sourceControlY}, ${targetControlX} ${targetControlY}, ${targetPoint.x} ${targetPoint.y}
    `
      return options.raw ? Path.parse(pathData) : pathData
    },
    true,
  )

  Graph.registerEdge(
    'mindmap-edge',
    {
      inherit: 'edge',
      connector: {
        name: 'mindmap',
      },
      attrs: {
        line: {
          targetMarker: '',
          stroke: '#6a7ef6',
          strokeWidth: 2,
        },
      },
      zIndex: 0,
    },
    true,
  )
}

export const unregisterMindMap = (Graph: typeof X6Graph) => {
  Graph.unregisterNode('topic')
  Graph.unregisterNode('topic-branch')
  Graph.unregisterNode('topic-child')
  Graph.unregisterEdge('mindmap-edge')
}
