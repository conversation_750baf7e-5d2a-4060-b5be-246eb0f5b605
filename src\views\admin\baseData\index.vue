<template>
  <div class="list">
    <div class="teacher-main">
      <!-- 搜索栏 -->
      <div class="list-header">
        <el-form :inline="true" :model="searchForm">
          <el-form-item label="企业名称：">
            <el-input v-model.trim="searchForm.enterpriseName" placeholder="请输入" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" :icon="Search" @click="getTableList()">查询
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <div class="list-body">
        <!-- 操作按钮 -->
        <div class="list-body-toolbar clearfix">
          <el-button type="primary" @click="handleAddNew">新增企业</el-button>
        </div>
        <!-- 表格 -->
        <el-table :data="tableData" v-loading="loading">
          <el-table-column label="序号" type="index" width="80" />
          <el-table-column prop="enterpriseName" label="企业名称" />
          <el-table-column label="操作">
            <template #default="{ row }">
              <el-button type="primary" link @click="handleCopy(row)">复制</el-button>
              <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
              <el-button type="primary" link @click="handleBase(row)">基础数据</el-button>
              <el-button type="danger" link @click="handleSingleDelete(row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 新增/编辑弹窗 -->
      <AddEnterprise v-model:visible="addVisible" :dialogType="dialogType" :editData="editData"
        @success="getTableList" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Search } from "@element-plus/icons-vue";
import * as api from '@/api/admin'
import { onMounted, ref } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import AddEnterprise from './components/AddEnterprise.vue'
import { useRouter, useRoute } from 'vue-router'
import type { EnterpriseItem } from '@/views/admin/types'

const router = useRouter()
const route = useRoute()
const tableData = ref<EnterpriseItem[]>([])
const loading = ref(false)
const searchForm = ref({
  enterpriseName: ''
})

const getTableList = async () => {
  loading.value = true;
  const params: Record<string, string> = {};
  if (searchForm.value.enterpriseName) {
    params.enterpriseName = searchForm.value.enterpriseName
  }
  const { data: { code, msg, data } } = await api.getEnterpriseList(params);
  loading.value = false
  if (code === 200) {
    tableData.value = data;
  } else {
    ElMessage.error(msg)
  }
}

// 处理删除
const handleSingleDelete = (row: EnterpriseItem) => {
  const params = {
    enterpriseId: row.enterpriseId,
  };
  handleDelete(params);
}

const handleDelete = (params: { enterpriseId: string }, isBatch = false) => {
  ElMessageBox.confirm(`你确定要${isBatch ? "批量" : ""}删除吗？`, "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(async () => {
      loading.value = true;
      const { data: { code, msg } } = await api.delEnterprise(params);
      loading.value = false
      if (code === 200) {
        ElMessage.success("删除成功！");
        getTableList();
      } else {
        ElMessage.error(msg)
      }
    })
    .catch(() => { });
}

const addVisible = ref(false);
const dialogType = ref<'add' | 'edit'>("add");
const editData = ref<EnterpriseItem | null>(null);

// 处理新增
const handleAddNew = () => {
  dialogType.value = "add"
  editData.value = null
  addVisible.value = true
}
// 处理编辑
const handleEdit = (row: EnterpriseItem) => {
  editData.value = row
  dialogType.value = "edit"
  addVisible.value = true
}

const handleBase = (row: EnterpriseItem) => {
  router.push({
    path: '/admin/enterpriseData',
    query: {
      enterpriseId: row.enterpriseId,
      enterpriseName: row.enterpriseName,
      platformCode: route.query.platformCode
    }
  })
}

const handleCopy = (row: EnterpriseItem) => {
  ElMessageBox.confirm(`确定复制企业[${row.enterpriseName}]吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
  })
    .then(async ({ }) => {
      const params = {
        enterpriseId: row.enterpriseId,
      }
      const { data: { code, msg } } = await api.copyEnterprise(params);
      if (code === 200) {
        ElMessage.success("复制成功！");
        getTableList();
      } else {
        ElMessage.error(msg)
      }
    })
    .catch(() => { });
}

onMounted(() => {
  getTableList();
});
</script>

<style scoped lang="scss">
.list {

  .w-200 {
    width: 200px;
  }

  .list-header {
    padding: 20px 20px 0;
    background-color: #fff;
  }

  .list-body {
    background-color: #fff;
    margin: 20px;
    padding: 20px;
  }

  .list-body-toolbar {
    border-bottom: 1px solid #ddd;
    margin-bottom: 15px;
    padding-bottom: 5px;
  }
}
</style>
