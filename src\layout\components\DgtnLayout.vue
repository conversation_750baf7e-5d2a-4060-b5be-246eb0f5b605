<template>
  <div class="base-layout">
    <Background type="dgtn"></Background>
    <div class="header">
      <slot name="header"></slot>
    </div>
    <div class="content">
      <slot></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import Background from './Background.vue'

</script>

<style scoped lang="scss">
.base-layout {
  @include flex(column, center, center);
  overflow: hidden;
  width: 100vw;
  height: 100vh;

  .header {
    width: 100%;
    flex-shrink: 0;
  }

  .content {
    width: 100%;
    flex: 1;
    min-height: 0;
    overflow: hidden;
    background-size: 100% 32px;
  }
}
</style>
