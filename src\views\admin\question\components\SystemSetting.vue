<template>
  <div class="system-setting">
    <div class="tip-text">请先设置制度和部门，再进行勾选。</div>
    <div class="operation-wrapper">
      <el-button type="primary" @click="addColumn">新增列</el-button>
      <el-button type="primary" @click="addRow">新增行</el-button>
    </div>
    <div class="table-wrapper">
      <div class="table-scroll">
        <table class="setting-table">
          <thead>
            <tr>
              <th>制度名称</th>
              <th v-for="(_, colIndex) in columnCount" :key="colIndex">
                <div class="header-cell">
                  <el-input v-model="columnHeaders[colIndex]" maxlength="10" placeholder="请输入部门名称" class="w-100" />
                  <el-icon class="delete-icon" @click="deleteColumn(colIndex)">
                    <Delete />
                  </el-icon>
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(_, rowIndex) in rowCount" :key="rowIndex">
              <td>
                <div class="header-cell">
                  <el-input v-model="rowHeaders[rowIndex]" placeholder="请输入制度名称" maxlength="50" class="w-200" />
                  <el-icon class="delete-icon" @click="deleteRow(rowIndex)">
                    <Delete />
                  </el-icon>
                </div>
              </td>
              <td v-for="(_, colIndex) in columnCount" :key="colIndex">
                <el-checkbox v-model="checkData[rowIndex][colIndex]" :true-value="1" :false-value="0" />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Delete } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const props = defineProps({
  initContent: {
    type: String,
    default: ''
  }
})

const columnCount = ref(2) // 初始2列
const rowCount = ref(2) // 初始2行
const columnHeaders = ref<string[]>(new Array(columnCount.value).fill(''))
const rowHeaders = ref<string[]>(new Array(rowCount.value).fill(''))

// 初始化二维数组
const initCheckData = (rows: number, cols: number) => {
  return Array(rows).fill(0).map(() => Array(cols).fill(0))
}

const checkData = ref<number[][]>(initCheckData(rowCount.value, columnCount.value))

const addColumn = () => {
  columnCount.value++
  columnHeaders.value.push('')
  // 为每一行添加新的列
  checkData.value.forEach(row => row.push(0))
}

const addRow = () => {
  rowCount.value++
  rowHeaders.value.push('')
  // 添加新的行
  checkData.value.push(new Array(columnCount.value).fill(0))
}

const deleteColumn = (index: number) => {
  columnCount.value--
  columnHeaders.value.splice(index, 1)
  // 从每一行中删除对应列
  checkData.value.forEach(row => row.splice(index, 1))
}

const deleteRow = (index: number) => {
  rowCount.value--
  rowHeaders.value.splice(index, 1)
  // 删除对应行
  checkData.value.splice(index, 1)
}

// 对外暴露验证方法
const validate = () => {
  return new Promise((resolve, reject) => {
    // 检查部门名称是否都填写
    const emptyColumnHeader = columnHeaders.value.some(header => !header.trim())
    if (emptyColumnHeader) {
      ElMessage.error('请填写所有部门名称')
      return reject('部门名称不能为空')
    }

    // 检查制度名称是否都填写
    const emptyRowHeader = rowHeaders.value.some(header => !header.trim())
    if (emptyRowHeader) {
      ElMessage.error('请填写所有制度名称')
      return reject('制度名称不能为空')
    }

    // 检查是否有勾选项
    // const hasChecked = checkData.value.some(row => row.some(checked => checked))
    // if (!hasChecked) {
    //   ElMessage.error('请至少勾选一项')
    //   return reject('请至少勾选一项')
    // }


    resolve({
      initContent: JSON.stringify({
        columnHeaders: columnHeaders.value,
        rowHeaders: rowHeaders.value,
        data: checkData.value
      })
    })
  })
}

onMounted(() => {
  if (props.initContent) {
    const { columnHeaders: _columnHeaders, rowHeaders: _rowHeaders, data } = JSON.parse(props.initContent)
    columnHeaders.value = _columnHeaders
    rowHeaders.value = _rowHeaders
    checkData.value = data
  }
})

defineExpose({
  validate
})

</script>

<style scoped lang="scss">
.system-setting {
  .tip-text {
    color: var(--el-text-color-secondary);
    margin-bottom: 16px;
  }

  .operation-wrapper {
    margin-bottom: 16px;
    @include flex(row, flex-start, center, nowrap);
  }

  .table-wrapper {
    border: 1px solid var(--el-border-color);
    border-radius: 4px;
    padding: 16px;
  }

  .table-scroll {
    width: 100%;
    overflow-x: auto;

    // 优化滚动条样式
    &::-webkit-scrollbar {
      height: 6px;
    }

    &::-webkit-scrollbar-thumb {
      background: var(--el-border-color);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-track {
      background: var(--el-fill-color-light);
    }
  }

  .setting-table {
    min-width: 100%;
    width: max-content; // 确保表格不会被压缩
    border-collapse: collapse;

    th,
    td {
      padding: 12px;
      text-align: center;
      border: 1px solid var(--el-border-color);
      min-width: 200px;

      .el-input {
        width: 200px;
      }
    }

    th {
      background-color: var(--el-fill-color-light);
      font-weight: normal;
      color: var(--el-text-color-regular);
      min-width: 140px;

      .el-input {
        width: 140px;
      }
    }

    td:first-child {
      background-color: var(--el-fill-color-light);
    }

    .header-cell {
      @include flex(row, center, center, nowrap);
      gap: 8px;

      .delete-icon {
        cursor: pointer;
        color: var(--el-text-color-secondary);
        font-size: 16px;

        &:hover {
          color: var(--el-color-danger);
        }
      }
    }
  }
}
</style>
