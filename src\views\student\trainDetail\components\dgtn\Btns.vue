<template>
  <div class="btns">
    <el-button v-if="taskDetail?.showAnswer" type="success" plain @click="visibleAnswer = true">查看答案</el-button>
    <el-button v-if="taskDetail?.taskNote" type="primary" plain @click="handleShowDesc(1)">案例资料</el-button>
    <el-button v-if="taskDetail?.taskDesc" type="primary" plain @click="handleShowDesc(2)">{{ taskDetail?.questionType
      === 7 ? '前景提要' : '任务说明'
    }}</el-button>
    <el-button v-if="taskDetail?.ninBcQuestionFileList?.length" type="primary" plain
      @click="visibleAttach = true">附件资料</el-button>
    <el-button v-if="taskDetail?.showIfReset" class="primary-btn" type="primary" @click="handleReset()"
      :disabled="disabled">重置</el-button>
    <el-button v-if="!disabled" class="primary-btn" type="primary" @click="handleSubmit()"
      :disabled="disabled">暂存当前做题</el-button>
    <Answer v-model:visible="visibleAnswer" :taskDetail="taskDetail"></Answer>
    <!-- 前景提要和任务说明,战略地图绘制和战略分析的叫做前景提要，其余叫做任务说明 -->
    <OutlookSummary v-model:visible="visibleOutlookSummary" :content="taskDesc" @handleHidden="handleHiddenDesc" />
    <NinDialog v-model:visible="visibleDesc" :title="descTitle" width="750px">
      <RichPreview :str="taskDesc">
      </RichPreview>
    </NinDialog>
    <AttachmentList v-model:visible="visibleAttach" :attachments="taskDetail?.ninBcQuestionFileList"></AttachmentList>
  </div>
</template>


<script setup lang="ts">
import OutlookSummary from './OutlookSummary.vue'
import RichPreview from '@/components/global/RichPreview.vue'
import NinDialog from '@/components/global/NinDialog.vue'
import Answer from '../Answer.vue'
import AttachmentList from '../AttachmentList.vue'
import { ref, watch } from 'vue'
import type { TaskDetailItem } from '../../types'

interface Props {
  taskDetail: TaskDetailItem | null
  disabled?: boolean,
}

const props = withDefaults(defineProps<Props>(), {
  taskList: () => [],
  disabled: false,
})

const emit = defineEmits(['submit', 'hiddenDialog', 'reset'])



const visibleDesc = ref(false)
const visibleAttach = ref(false)
const visibleOutlookSummary = ref(false)
const taskDesc = ref('');
const descTitle = ref('');
const handleShowDesc = (type: number) => {
  if (type === 1) {
    descTitle.value = '案例资料'
    taskDesc.value = props.taskDetail?.taskNote || ''
    visibleDesc.value = true
  } else {
    descTitle.value = props.taskDetail?.questionType === 7 ? '前景提要' : '任务说明'
    taskDesc.value = props.taskDetail?.taskDesc || ''
    visibleOutlookSummary.value = props.taskDetail?.questionType === 7
    visibleDesc.value = props.taskDetail?.questionType !== 7
  }
}

const handleSubmit = (isAll = 0) => {
  emit('submit', isAll)
}

const visibleAnswer = ref(false)

const handleHiddenDesc = () => {
  visibleOutlookSummary.value = false
  emit('hiddenDialog', 'desc')
}

const handleReset = () => {
  emit('reset')
}

watch(() => props.taskDetail, (newVal, oldVal) => {
  if (newVal?.questionId !== oldVal?.questionId) {
    //只有战略地图绘制需要主动展示弹窗
    visibleOutlookSummary.value = props.taskDetail?.questionType === 7 && newVal?.questionHint !== 1 && !props.disabled && !!newVal?.taskDesc
    taskDesc.value = newVal?.taskDesc || ''
  }
}, { immediate: true })

</script>

<style scoped lang="scss">
.btns {
  display: flex;
  position: absolute;
  bottom: 16px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 99;

  :deep(.el-button) {
    min-width: 80px;
    padding: 0 10px;
    height: 28px;
    border-radius: 4px;

    &.primary-btn {
      background-image: linear-gradient(0deg,
          #4b5ee7 0%,
          #6075f3 50%,
          #758cfe 99%),
        linear-gradient(#ffffff,
          #ffffff);
      background-blend-mode: normal,
        normal;
      box-shadow: 0px -1px 0px 0px #667ef9,
        0px 1px 0px 0px #3446c8;
      border: 0;

      &:hover:not(.is-disabled) {
        background-image: linear-gradient(0deg,
            #4b5ee7 0%,
            #6075f3 50%,
            #4b5ee7 99%),
          linear-gradient(#ffffff,
            #ffffff);
      }
    }

    &.el-button--success.is-plain {
      background-color: #ebffff;
      border-color: #30c1a1;
      color: #0bb694;

      &:hover:not(.is-disabled) {
        background-color: #0bb694;
        color: #fff;
      }
    }

    &.el-button--primary.is-plain {
      background-color: #fff;
      border-color: #738afd;
      color: #5064ea;

      &:hover:not(.is-disabled) {
        background-color: #5064ea;
        color: #fff;
      }
    }
  }
}
</style>
