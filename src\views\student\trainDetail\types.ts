export interface TaskItem {
  questionId: string
  questionName: string
  ifFinish: number
  questionType: number
}

export interface TaskListItem {
  questionList: TaskItem[];
  categoryName: string;
  categoryIcon: string;
  questionType: string;
}

export interface NinFileItem {
  createTime: string;
  createUserCode: string;
  createUserId: string;
  fileExt: string;
  fileName: string;
  filePath: string;
  fileSize: string;
  modifyTime: string;
  modifyUserId: string;
  questionFileId: string;
  questionId: string;
}

export interface TaskDetailItem extends TaskItem {
  stuScore: string;
  questionScore: string;
  stuAnswer: string;
  taskDesc: string;
  taskNote: string;
  taskOptions: string;
  ninBcQuestionFileList: NinFileItem[]
  initContent: string
  showAnswer: number
  showSubmit: number
  enterpriseId: string
  rightNodeIdOrEdgeIds?: string[]
  questionHint: number
  questionSecondType: number
  showIfReset: number
}

