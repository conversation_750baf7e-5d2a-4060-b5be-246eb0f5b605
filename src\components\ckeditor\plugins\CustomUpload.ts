import * as api from "@/api/common";
import {
  ElMessage
} from 'element-plus'
class CustomUploadAdapter {
  private loader: any

  constructor(loader: any) {
    this.loader = loader;
  }
  // 启动上载过程
  upload() {
    return this.loader.file
      .then((file: File) => new Promise((resolve, reject) => {
        if (file.size / 1024 > 10240) {
          ElMessage({
            message: "图片不能大于10M！",
            type: "error",
            duration: 1500
          })
          reject();
        } else {
          const parmas = {
            upfile: file,
          }
          api.upload(parmas).then(res => {
            if (res.status === 200 && res.data.code === 200) {
              const data = res.data.data.fullUrl;
              // data值为：{ default: 'http://example.com/images/image.png' }
              const response = {
                default: `${data}`
              }
              resolve(response);
            } else {
              reject()
            }
          }).catch(error => {
            reject(error)
          });
        }


      }));
  }
  abort() {
    //可以书写删除服务器图片的逻辑
  }

}

export default CustomUploadAdapter
