<template>
  <div class="barrier" v-loading="loading">
    <Header :taskList="taskList" :multipleTask="multipleTask" disabled :taskDetail="taskDetail" />
    <div class="barrier-content">
      <div class="barrier-topic">
        <el-scrollbar>
          踏上成为京北建工集团建设FSSC项目总负责人的冒险之旅!在这个徽中;的接利用详尽的体团背界任务资料和任务要求，一路过关斩将。简单的上手操作与深入浅出的知识元素，你体验不同的场景，让你身临其境，享受头脑风暴的乐趣。
          踏上成为京北建工集团建设FSSC项目总负责人的冒险之旅!在这个徽中;的接利用详尽的体团背界任务资料和任务要求，一路过关斩将。简单的上手操作与深入浅出的知识元素，你体验不同的场景，让你身临其境，享受头脑风暴的乐趣。踏上成为京北建工集团建设FSSC项目总负责人的冒险之旅!在这个徽中;的接利用详尽的体团背界任务资料和任务要求，一路过关斩将。简单的上手操作与深入浅出的知识元素，你体验不同的场景，让你身临其境，享受头脑风暴的乐趣。踏上成为京北建工集团建设FSSC项目总负责人的冒险之旅!在这个徽中;的接利用详尽的体团背界任务资料和任务要求，一路过关斩将。简单的上手操作与深入浅出的知识元素，你体验不同的场景，让你身临其境，享受头脑风暴的乐趣。踏上成为京北建工集团建设FSSC项目总负责人的冒险之旅!在这个徽中;的接利用详尽的体团背界任务资料和任务要求，一路过关斩将。简单的上手操作与深入浅出的知识元素，你体验不同的场景，让你身临其境，享受头脑风暴的乐趣。
        </el-scrollbar>
      </div>
      <div class="barrier-content-inner">
        <div class="barrier-node" v-for="i in 9" :key="i">
          <div class="nove-level">
            <SvgIcon iconName="start" size="20" fill="#ff1493" />
            <SvgIcon iconName="start" size="20" fill="#f39fdb" />
            <SvgIcon iconName="start" size="20" fill="#f39fdb" />
          </div>
          <div class="node-content">
            <div class="node-bg">
              <div class="node-bg-inner">
                <img src="@/assets/images/barrier/icon-node.png" alt="FSSC战略定位">
              </div>
            </div>
            <div class="node-label">FSSC战略定位</div>
          </div>
        </div>
        <div class="point">
          <div class="point-inner"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import SvgIcon from '@/components/global/SvgIcon.vue';
import Header from './trainDetail/components/dgtn/Header.vue';
import { useTrainDetail } from './trainDetail/hooks/useTrainDetail'
import { computed } from 'vue';

const {
  loading,
  taskList,
  taskDetail,
} = useTrainDetail()

const multipleTask = computed(() => {
  return !(taskList.value.length === 1 && taskList.value[0]?.questionList?.length === 1)
})
</script>

<style scoped lang="scss">
.barrier {
  width: 100vw;
  height: 100vh;
  background: url('@/assets/images/barrier/bg.png') no-repeat center center;
  background-size: 100% 100%;
  @include flex(column, center, center, nowrap);

  .barrier-content {
    flex: 1;
    min-height: 0;
  }

  .barrier-topic {
    width: 1094px;
    height: 160px;
    background: url('@/assets/images/barrier/bg-topic.png') no-repeat center center;
    background-size: 100% 100%;
    margin: 37px auto 0;
    padding: 40px 58px 38px;
    font-size: 14px;
    line-height: 26px;
    color: #333333;
    position: relative;

    &::after,
    &::before {
      content: '';
      position: absolute;
      width: 3px;
      height: 56px;
      background-color: #566aff;
      top: -37px;
      display: inline-block;
    }

    &::after {
      left: 58px;
    }

    &::before {
      right: 58px;
    }
  }

  .barrier-node {
    &.yellow {
      .node-bg-inner {
        background-image: linear-gradient(-30deg,
            #fa9b20 0%,
            #ffd167 100%),
          linear-gradient(#2aadff,
            #2aadff);
        background-blend-mode: normal,
          normal;

        &::before {
          background-color: rgba($color: #a95f00, $alpha: 0.38);
        }
      }
    }

    &.green {
      .node-bg-inner {
        background-image: linear-gradient(-30deg,
            #0abcab 0%,
            #42f8dd 100%),
          linear-gradient(#2aadff,
            #2aadff);

        &::before {
          background-color: rgba($color: #049486, $alpha: 0.38);
        }
      }
    }

    &.cyan {
      .node-bg-inner {
        background-image: linear-gradient(-30deg,
            #1f93ff 0%,
            #40e0ff 100%),
          linear-gradient(#2aadff,
            #2aadff);

        &::before {
          background-color: rgba($color: #006bce, $alpha: 0.38);
        }
      }
    }

    .node-bg {
      width: 92px;
      height: 86px;
      border: solid 1px #ecefff;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background-color: rgba($color: #ffffff, $alpha: 0.2);
        border: solid 1px #ecefff;
        position: absolute;
        left: -6px;
        top: 6px;
      }
    }

    .nove-level {
      width: 90px;
      height: 22px;
      background: url('@/assets/images/barrier/bg-start.png') no-repeat center center;
      background-size: 100% 100%;
      @include flex(row, flex-start, center, nowrap);
      padding-left: 8px;
      gap: 4px;
      margin-bottom: 4px;
    }

    .node-bg-inner {
      width: 100%;
      height: 100%;
      background-image: linear-gradient(-30deg,
          #5160fa 0%,
          #a3adff 100%),
        linear-gradient(#2aadff,
          #2aadff);
      background-blend-mode: normal,
        normal;
      position: relative;
      z-index: 1;

      &::before {
        content: '';
        position: absolute;
        width: 62px;
        height: 50px;
        background-color: rgba($color: #2323d5, $alpha: 0.38);
        box-shadow: 0px 1px 0px 0px rgba(255, 255, 255, 0.15),
          inset 0px 1px 2px 0px rgba(20, 48, 105, 0.17);
        border-radius: 2px;
        left: 50%;
        top: 22px;
        transform: translateX(-50%);
      }

      img {
        width: 41px;
        height: 58px;
        margin-top: 14px;
        margin-left: 20px;
        position: relative;
        z-index: 2;
      }
    }

    .node-label {
      margin-top: 13px;
      font-size: 16px;
      line-height: 26px;
      color: #333333;
      font-weight: bold;
    }
  }

  .point {
    width: 22px;
    height: 12px;
    background-color: #ffffff;
    border: solid 1px #5164f2;
    border-radius: 50%;
    @include flex(row, center, center, nowrap);

    .point-inner {
      width: 12px;
      height: 6px;
      background-color: #5164f2;
      border-radius: 50%
    }
  }
}
</style>
