<template>
  <el-dialog :title="type === 'add' ? '新增实训' : '编辑实训'" :model-value='visible' width="700px" @close="handleClose">
    <el-form ref="formRef" :model="formData" :rules="rules" label-width="100px" v-loading="loading">
      <el-form-item label="实训名称" prop="trainName">
        <el-input v-model="formData.trainName" maxlength="20" show-word-limit placeholder="请输入实训名称" />
      </el-form-item>
      <!-- <el-form-item label="实训时间" required>
        <el-col :span="11">
          <el-form-item prop="beginTime">
            <el-date-picker v-model="formData.beginTime" type="date" placeholder="选择开始时间" value-format="YYYY-MM-DD"
              style="width: 100%;" :disabled-date="handleDisabledstartTime" />
          </el-form-item>
        </el-col>
        <el-col class="text-center" :span="2"> - </el-col>
        <el-col :span="11">
          <el-form-item prop="endTime">
            <el-date-picker v-model="formData.endTime" type="date" placeholder="选择结束时间" value-format="YYYY-MM-DD"
              style="width: 100%;" :disabled-date="handleDisabledendTime" />
          </el-form-item>
        </el-col>
      </el-form-item> -->

      <el-form-item label="实训任务" prop="tasks" required>
        <div>
          <div class="task-select">
            <el-select v-model="taskForm.questionType" placeholder="请选择题型" style="width: 200px" @change="getTaskList">
              <el-option v-for="item in QUESTION_TYPE" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
            <el-select v-model="taskForm.taskId" placeholder="请选择题目" style="width: 200px">
              <el-option v-for="item in taskList" :key="item.questionId" :label="item.questionName"
                :value="item.questionId" />
            </el-select>
            <el-button type="primary" @click="addTask">新增</el-button>
          </div>
          <div class="task-list">
            <el-table :data="formData.tasks" border small>
              <el-table-column prop="type" label="题型" min-width="120px">
                <template #default="scope">
                  {{ QUESTION_TYPE_MAP.get(scope.row.questionType) }}
                </template>
              </el-table-column>
              <el-table-column prop="questionName" label="题目名称" min-width="120px"></el-table-column>
              <el-table-column label="操作" width="180">
                <template #default="scope">
                  <el-button type="primary" link @click="removeTask(scope.$index)">移除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button :loading="loading" type="primary" @click="handleSubmit">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useDebounceFn } from '@vueuse/core'
import { QUESTION_TYPE } from '@/utils/constant'
import * as api from '@/api/teacher'
import type { Question } from '@/views/admin/types'

const QUESTION_TYPE_MAP = Object.values(QUESTION_TYPE).reduce((acc, cur) => {
  acc.set(cur.value, cur.label);
  return acc;
}, new Map());

interface Props {
  visible: boolean
  type: 'add' | 'edit'
  editData: Record<string, any> | null
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const taskForm = reactive({
  questionType: '',
  taskId: '',
})
const taskList = ref<Question[]>([])


// 使用防抖包装的获取任务列表函数
const getTaskList = useDebounceFn(async () => {
  try {
    taskList.value = []
    taskForm.taskId = ''
    const { data: { code, msg, data } } = await api.getTrainQuestionList({
      questionType: taskForm.questionType
    })

    if (code === 200) {
      taskList.value = data
    } else {
      ElMessage.error(msg)
    }
  } catch (error) {
    console.error(error)
    ElMessage.error('获取题目列表失败')
  }
}, 300) // 300ms 的防抖时间

const loading = ref(false)
const formRef = ref<FormInstance>()
const formData = ref({
  trainName: '',
  beginTime: '',
  endTime: '',
  tasks: [] as Question[]
})

const rules = reactive<FormRules>({
  trainName: [
    { required: true, message: '请输入实训名称', trigger: 'blur' },
  ],
  beginTime: [{ type: 'date', required: true, message: '请选择开始时间', trigger: 'change', },],
  endTime: [{ type: 'date', required: true, message: '请选择结束时间', trigger: 'change', },],
  tasks: [
    { required: true, message: '请选择实训任务', trigger: 'change', type: 'array' },
  ]
})
const handleDisabledstartTime = (time: Date) => {
  if (!formData.value.endTime) {
    return time.getTime() < Date.now() - 8.64e7
  } else {
    return time.getTime() > new Date(formData.value.endTime).getTime() - 8.64e7
  }
}
const handleDisabledendTime = (time: Date) => {
  if (!formData.value.beginTime) {
    return time.getTime() < Date.now() - 8.64e7
  } else {
    return time.getTime() < new Date(formData.value.beginTime).getTime() - 8.64e6
  }
}

const getQuestionById = async () => {
  try {
    loading.value = true
    const { data: { code, msg, data } } = await api.getQuestionById({ trainId: props.editData?.trainId || '' });
    if (code === 200) {
      formData.value.tasks = data
    } else {
      ElMessage.error(msg)
    }
  } catch (error) {
    console.error(error)
  } finally {
    loading.value = false
  }

}

// 监听编辑数据变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    if (props.type === 'edit') {
      if (!props.editData) return
      formData.value = {
        trainName: props.editData.trainName,
        endTime: props.editData.endTime,
        beginTime: props.editData.beginTime,
        tasks: []
      }
      getQuestionById()
    }
    formRef.value?.clearValidate()
  }
})

// 添加任务
const addTask = () => {
  if (!taskForm.taskId) return ElMessage.warning('请选择题目')
  // 防止重复添加
  if (formData.value.tasks.some(item => item.questionId === taskForm.taskId)) return ElMessage.warning('该题目已添加')
  const task = taskList.value.find(item => item.questionId === taskForm.taskId)
  if (task) {
    formData.value.tasks.push(task)
  }
}

// 移除任务
const removeTask = (index: number) => {
  formData.value.tasks.splice(index, 1)
}

// 处理弹窗关闭
const handleClose = () => {
  loading.value = false
  taskForm.questionType = ''
  taskForm.taskId = ''
  taskList.value = []
  formRef.value?.resetFields()
  formRef.value?.clearValidate()
  formData.value = {
    trainName: '',
    beginTime: '',
    endTime: '',
    tasks: []
  }
  emit('update:visible', false)
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value) return

  await formRef.value.validate(async (valid) => {
    if (valid) {
      try {
        loading.value = true
        const params: Record<string, string> = {
          trainName: formData.value.trainName,
          // beginTime: formData.value.beginTime,
          // endTime: formData.value.endTime,
        }
        params.questionIds = JSON.stringify(formData.value.tasks.map(item => item.questionId))
        if (props.type === 'edit' && props.editData) {
          params.trainId = props.editData.trainId
          const { data: { code, msg } } = await api.delQuestionById(params);
          if (code === 200) {
            addTrain(params)
          } else if (code === 412) {
            ElMessageBox.confirm(
              '本次修改包含移除操作，移除题目将清除做题数据，确认保存吗？',
              '提示',
              {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
              }
            ).then(() => {
              addTrain(params)
            })
          } else {
            ElMessage.error(msg)
          }
        } else {
          addTrain(params)
        }
      } catch (error) {
        console.error(error)
        ElMessage.error('操作失败')
      } finally {
        loading.value = false
      }

    }
  })
}

const addTrain = async (params: Record<string, string>) => {
  const { data: { code, msg } } = await api.addTrain(params);
  if (code === 200) {
    ElMessage.success(msg)
    emit('success')
    handleClose()
  } else {
    ElMessage.error(msg)
  }
}
</script>

<style scoped lang="scss">
.task-select {
  display: flex;
  gap: 14px;
  margin-bottom: 14px;
}
</style>
