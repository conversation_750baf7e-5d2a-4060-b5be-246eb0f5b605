<template>
  <TrainMain v-if="!isDgtn" />
  <DgtnTrainMain v-else />
</template>

<script setup lang="ts">
import { computed, onBeforeMount, defineAsyncComponent } from 'vue'
import { useStore } from 'vuex'
import { useRoute } from 'vue-router'
const TrainMain = defineAsyncComponent(() => import('./components/TrainMain.vue'))
const DgtnTrainMain = defineAsyncComponent(() => import('./components/dgtn/DgtnTrainMain.vue'))

const store = useStore()
const route = useRoute()
const isDgtn = computed(() => store.getters.theme === 'light')
onBeforeMount(() => {
  store.commit('SET_THEME', route.query.ifdesign ? 'light' : 'dark')
})
</script>
