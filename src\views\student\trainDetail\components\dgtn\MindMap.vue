<template>
  <div class="organizational ">
    <div class="options-wrapper">
      <OptionList icon-src="improve" :options="options" :usedOptionIds="usedOptionIds" :disabled="disabled"
        @drag-start="handleOptionDragStart" />
    </div>
    <div class="base-content content">
      <div class="section-title-light">
        <div class="title-label">{{ taskDetail.questionName }}</div>
      </div>
      <div class="content-wrapper">
        <GraphTools :tools="['zoom-out', 'zoom-in', 'reset-zoom']" @zoom-out="handleZoomOut" @zoom-in="handleZoomIn"
          @reset-zoom="handleResetZoom" />
        <div class="graph" ref="graphRef"></div>
      </div>
    </div>

  </div>
</template>

<script setup lang="ts">
import GraphTools from '@/components/flow/components/GraphTools.vue'
import { ElMessage } from 'element-plus'
import OptionList from './OptionList.vue'
import { onMounted, ref, nextTick, onBeforeUnmount, watch, computed } from 'vue'
import { Graph } from '@antv/x6'
import { Dnd } from '@antv/x6-plugin-dnd'
import { registerMindMap } from '@/components/flow/shapeConfig/mindMap'
import { registerNodeTools } from "@/components/flow/tools/registerNodeTools";
import type { OptionItem } from '@/views/admin/types'
import type { TaskDetailItem } from '../../types'
import type { Cell } from '@antv/x6'
import * as api from '@/api/student'

interface IProps {
  taskDetail: TaskDetailItem,
  disabled?: boolean
  mode?: 'viewAnswer' | 'answer' // viewAnswers：查看答案模式，answer：答题模式
  apiExtraParams?: Record<string, string>
}

const props = withDefaults(defineProps<IProps>(), {
  disabled: false,
  mode: 'answer',
  apiExtraParams: () => ({})
})

const usedOptionName = ref<Set<string>>(new Set())
const options = ref<OptionItem[]>([])
const currentOption = ref<OptionItem>()

const optionsMap = computed(() => {
  return options.value.reduce((acc, option) => {
    acc[option.name] = option.id
    return acc
  }, {} as Record<string, string>)
})

const usedOptionIds = computed(() => {
  return new Set(Array.from(usedOptionName.value).map((name) => optionsMap.value[name]))
})

const graphRef = ref<HTMLDivElement | null>()
const graph = ref<Graph>()
const dnd = ref<Dnd>();

const initGraph = async () => {
  await nextTick();
  if (!graphRef.value) return
  graph.value = new Graph({
    container: graphRef.value,
    connecting: {
      snap: {
        radius: 20,
      },
      allowBlank: false,
      allowLoop: false,
      highlight: false,
      sourceAnchor: {
        name: "center",
      },
      targetAnchor: "center",
      connectionPoint: "anchor",
      router: "manhattan",
      validateMagnet() {
        return false
      },
      validateConnection() {
        return false
      },
    },
    panning: {
      enabled: true,
    },
    interacting: {
      nodeMovable: false, //节点拖动
    },
    embedding: {
      enabled: true,
      frontOnly: true,
      findParent({ node }) {
        const bbox = node.getBBox()
        return this.getNodes().filter((node) => {
          if (!node.data?.isPreSet) {
            const targetBBox = node.getBBox()
            return bbox.isIntersectWithRect(targetBBox)
          }
          return false
        })
      }
    }
  });
  dnd.value = new Dnd({
    target: graph.value,
    validateNode() {
      return true
    },
  });
  resigterEvent()
  injectData()

}
const handleZoomOut = () => {
  if (!graph.value) return
  const zoom = graph.value.zoom()
  if (zoom > 0.5) {
    graph.value.zoom(-0.1)
  }
}

const handleZoomIn = () => {
  if (!graph.value) return
  const zoom = graph.value.zoom()
  if (zoom < 2) {
    graph.value.zoom(0.1)
  }
}

const handleResetZoom = () => {
  if (!graph.value) return
  graph.value.zoom(1, {
    absolute: true
  })
}
const handleSaveStep = async (answer: {
  id: string,
  name: string,
}) => {
  return new Promise(async (resolve, reject) => {
    const params = {
      questionId: props.taskDetail.questionId,
      ...props.apiExtraParams,
      ...answer
    }
    try {
      const { data: { code, msg, data } } = await api.mindMappFeedback(params)
      if (code === 200) {
        if (data === 1) {
          ElMessage.success('回答正确')
        } else if (data === 0) {
          ElMessage.error('回答错误')
        }
        resolve(true)
      } else {
        ElMessage.error(msg)
        reject(msg)
      }
    } catch (error) {
      console.error(error)
      reject(error)
    }
  })
}

const onDelete = async (data: any) => {
  try {
    const cell = data.cell as Cell
    await handleSaveStep({
      id: cell.id,
      name: ''
    })
    usedOptionName.value.delete(cell.data.name)
    cell?.setData({
      name: ''
    })
    cell?.setAttrs({
      text: {
        text: ' ',
      },
      title: {
        text: ' '
      }
    })
    cell?.removeTools()
  } catch (error) {
    console.error(error)
  }
}

const handleInitData = () => {
  if (props.taskDetail.initContent) {
    const initData = JSON.parse(props.taskDetail.initContent)
    console.log(initData)
    initData.forEach((node: any) => {
      if (node.shape.indexOf('edge') === -1 && !node.data?.isPreSet) {
        node.data.name = ''
        node.attrs.text.text = ''
        node.attrs.title.text = ''
        node.tools = {}
      }
    })
    return initData
  }
  return []
}

const handleStuAnswer = () => {
  if (props.taskDetail.stuAnswer) {
    const stuAnswer = JSON.parse(props.taskDetail.stuAnswer)
    stuAnswer.forEach((node: any) => {
      if (node.shape.indexOf('edge') === -1) {
        console.log(node)
        if (!node.data.name) {
          if (!node.attrs.title) {
            node.attrs.title = {
              text: ''
            }
          }
          if (!node.attrs.text) {
            node.attrs.text = {
              text: ''
            }
          }
          node.attrs.title.text = ''
          node.attrs.text.text = ''
          node.tools = {}
        } else {
          // console.log(node)
          //如果已拖拽进去的，收集已使用options
          usedOptionName.value.add(node.data.name)
          if (!props.disabled && !node.data.isPreSet) {
            node.tools = {
              items: [{
                name: 'cus-button-remove',
                args: {
                  fill: '#666',
                  x: '100%',
                  y: 0,
                  offset: {
                    x: 5,
                    y: 0
                  },
                  onClick: onDelete
                }
              }]
            }
          }
        }

      }

      // 线和没有拖拽进去的节点，禁止模式下，把tools
      if (node.shape.indexOf('edge') > -1 || node.data?.isPreSet || (node.shape.indexOf('edge') === -1 && !node.data.name) || props.disabled) {
        node.tools = {}
      }
    })
    return stuAnswer
  }
  return []
}

const injectData = () => {
  if (!graph.value) return
  const data = props.taskDetail.stuAnswer ? handleStuAnswer() : handleInitData()
  graph.value.fromJSON(data)
  // graph.value.zoomToFit()
  graph.value.centerContent()
}

// 注册事件
const resigterEvent = () => {
  if (!graph.value) return
  graph.value.on('node:added', ({ node }) => {
    setTimeout(() => {
      if (!node.parent) {
        graph.value!.removeNode(node)
      }
    });
  })

  graph.value.on('node:embedded', async ({ node, currentParent }) => {
    try {
      node.remove()
      await handleSaveStep({
        id: currentParent!.id,
        name: node.data.name
      })
      //如果父节点原本已经放置了子节点，直接把子节点删除掉
      if (currentParent?.data?.name) {
        usedOptionName.value.delete(currentParent.data.name)
      }

      //作为子节点直接把name放入父节点中，然后再把子节点删除掉
      currentParent?.setAttrs({
        text: {
          text: node.data.name
        },
        title: {
          text: node.data.name
        }
      })
      currentParent?.setData({
        name: node.data.name,
        id: node.data.id
      })
      currentParent?.addTools({
        name: 'cus-button-remove',
        args: {
          fill: '#666',
          x: '100%',
          y: 0,
          offset: {
            x: 5,
            y: 0
          },
          onClick: onDelete
        }
      })

      usedOptionName.value.add(node.data.name)
    } catch (error) {
      node.remove()
      console.error(error)
    }

  });
}

const handleOptionDragStart = (item: OptionItem, event: DragEvent) => {
  const node = graph.value!.createNode({
    id: item.id,
    shape: 'circle',
    width: 50,
    height: 50,
    attrs: {
      body: {
        fill: '#4a69ff',
        stroke: '#fff',
        strokeWidth: 1,
      },
      label: {
        fontSize: 14,
        fill: '#FFFFFF',
        lineHeight: 18,
        textWrap: {
          width: '75%',
          height: '80%',
          ellipsis: true,
          breakWord: true,
        },
      },
    }
  });
  currentOption.value = item
  node.attrs!.text.text = item.name;
  node.setData({
    name: item.name,
    id: item.id
  });
  dnd.value?.start(node, event);
}


const getStuAnswer = () => {
  return Promise.resolve(JSON.stringify(graph.value!.toJSON().cells))
}

defineExpose({
  getStuAnswer
})

onMounted(() => {
  registerMindMap(Graph)
  registerNodeTools(Graph);
  options.value = props.taskDetail.taskOptions ? JSON.parse(props.taskDetail.taskOptions) : []
  initGraph()
})

onBeforeUnmount(() => {
})


//为了提交之后，更新disabled状态
watch(() => props.disabled, (newVal) => {
  //如果禁止了，要重新更新一下画布
  if (newVal) {
    graph.value?.clearCells()
    injectData()
  }
})

watch(() => props.taskDetail, (newVal) => {
  if (newVal.stuAnswer) {
    graph.value?.clearCells()
    injectData()
  }
})

</script>

<style scoped lang="scss">
.organizational {
  @include flex(row, flex-start, flex-start, nowrap);
  height: 100%;
  gap: 16px;

  .base-content {
    height: 100%;
    @include flex(column, flex-start, flex-start, nowrap);
    position: relative;

    .content-wrapper {
      width: 100%;
      flex: 1;
      min-height: 0;
      background: url('@/assets/images/train/bg-light-task.png') no-repeat bottom center;
      background-size: 100% auto;
      position: relative;
    }
  }

  .options-wrapper {
    width: 240px;
    height: 100%;
  }

  .content {
    min-width: 0;
    flex: 1;
    background-image: linear-gradient(to bottom, #fff 0%, rgba(255, 255, 255, 0.5) 100%);
    box-shadow: 0px 2px 4px 0px rgba(120, 147, 187, 0.15);
    border-radius: 6px;
    border: solid 1px #ffffff;
    overflow: hidden;
  }

  .graph {
    height: 100%;

    :deep {
      .topic-image {
        visibility: hidden;
        cursor: pointer;
      }
    }
  }

  @media screen and (max-width: 1440px) {
    .options-wrapper {
      width: 200px;
    }
  }
}
</style>
