<template>
    <el-dialog :model-value="visible" title="未登录提示" width="850px" :before-close="handleClose" draggable :showClose="false"
        :close-on-click-modal="false">
        <el-form>
            <el-form-item>
                <el-input type="textarea" :rows="10" v-model="str" placeholder="请粘贴token到下面输入框，模拟登录"></el-input>
            </el-form-item>
            <el-form-item>
                <el-input type="textarea" :rows="10" v-model="userInfo" placeholder="请粘贴userInfo到下面输入框，模拟登录"></el-input>
            </el-form-item>
        </el-form>
        <template #footer>
            <span class="dialog-footer">
                <el-button @click="handleClose">取消</el-button>
                <el-button type="primary" @click="setToken">设置token，模拟登录</el-button>
            </span>
        </template>
    </el-dialog>
</template>
<script setup lang="ts">
import { ElMessage } from 'element-plus';
import { ref } from 'vue';
import { useStore } from 'vuex';

defineProps({
    visible: {
        type: Boolean,
        default: false
    }
})

const str = ref('');
const userInfo = ref('')
const store = useStore();
const emit = defineEmits(["close"]);
const setToken = () => {
    if (str.value == "") {
        ElMessage.error("请输入token");
        return;
    } else {
        store.commit('SET_TOKEN', str.value);
        str.value = "";
        ElMessage.success("token设置成功！");
        emit("close")
    }
    if (userInfo.value == "") {
        ElMessage.error("请输入userinfo");
        return;
    } else {
        store.commit("user/SET_USERINFO", JSON.parse(userInfo.value));
        userInfo.value = "";
        ElMessage.success("userinfo设置成功！");
        emit("close")
    }
}
const handleClose = () => {
    emit("close");
}

</script>
