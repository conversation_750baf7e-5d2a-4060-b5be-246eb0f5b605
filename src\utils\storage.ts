// 获取localStorage
export const getStorage = (name: string) => {
    let storage = window.localStorage.getItem(name) || undefined
    if (storage) {
        storage = JSON.parse(storage)
    }
    return storage
}

// 设置localStorage
export const setStorage = (name: string, data: any) => {
    if (name && data) {
        window.localStorage.setItem(name, JSON.stringify(data))
    }
}

// 删除localStorage
export const removeStorage = (name: string) => {
    if (name) {
        window.localStorage.removeItem(name)
    }
}