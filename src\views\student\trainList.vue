<template>
  <div class="train-list" v-loading="loading">
    <div class="train-list-inner">
      <div class="list-header">
        <div class="title">
          <span class="title-label">实训列表</span>
          <span class="num-info">当前共有
            <b class="num"> {{ pageTotal }} </b>
            个实训</span>
        </div>
        <div class="search">
          <el-input class="search-input" v-model="ninSearchForm.trainName" placeholder="请输入实训名称">
            <template #suffix>
              <el-icon @click="handleCurrentChange(1, getTableList, true)" class="search-icon">
                <search />
              </el-icon>
            </template>
          </el-input>
        </div>
      </div>
      <div v-if="tableData.length" class="list-container flex flex-wrap">
        <div v-for="(item, index) in tableData" :key="index" class="course-item">
          <div class="course-content">
            <div class="course-content-inner">
              <div class="bg-wrapper">
                <img src="@/assets/images/icon/icon-train.png" alt="" class="course-icon">
              </div>
              <div class="course-info flex-1">
                <h3 class="title" :title="item.trainName">{{ item.trainName }}</h3>
                <div class="time">{{ item.beginTime?.slice(0, 10) }} 至 {{ item.endTime?.slice(0, 10) }}</div>
              </div>
            </div>
            <div class="icons" :class="{ 'offset-right': Object.keys(item.typeCountMap).length < 5 }">
              <div v-for="(value, key) in item.typeCountMap">
                <span :class="[`icon-${+key + 1}`]"></span>
                <span>{{ value }}</span>
              </div>
            </div>
            <div class="btns flex justify-center items-center">
              <span v-if="item.status === 0" class="status disabled">未开始</span>
              <template v-else>
                <span v-if="item.status === 1" class="status">进行中</span>
                <span v-else class="score">{{ item.totalStuTrainScore || 0 }}分</span>
                <el-button class="enter-btn" @click="handleEnter(item)" :disabled="item.status === 2">进入实训</el-button>
              </template>
            </div>
          </div>
        </div>
      </div>
      <el-empty v-else :image="iconEmpty" :image-size="360" />
      <div class="pagination-wrapper">
        <Pagination :total="pageTotal" :pageSize="pageSize" :currentPage="pageNum" layout=" prev, pager, next"
          @currentChange="handleCurrentChange($event, getTableList)" @sizeChange="handleSizeChange($event, getTableList)">
        </Pagination>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import iconEmpty from '@/assets/images/icon/icon-empty.png'
import Pagination from "@/components/global/Pagination.vue";
import { Search } from '@element-plus/icons-vue'
import { toRefs } from 'vue'
import { useRouter, useRoute } from "vue-router";
import * as api from '@/api/student'
import { ElMessage } from 'element-plus'
import { TableOptions } from "@/utils/tableOptions";
import { encodeName } from '@/utils/tools'

interface TrainingItem {
  trainId: string
  trainName: string
  beginTime: string
  endTime: string
  status?: number // 0: 未开始, 1: 进行中, 2: 已结束
  score?: number
}

const router = useRouter()
const route = useRoute()

const t = new TableOptions(12);
const {
  loading,
  pageNum,
  tableData,
  pageSize,
  pageTotal,
  ninSearchForm,
  tempSearchForm,
} = toRefs(t);
const { handleCurrentChange, handleSizeChange } = t;

const getTrainingStatus = (beginTime: string, endTime: string) => {
  const now = new Date().getTime()
  const start = new Date(beginTime).getTime()
  const end = new Date(endTime).getTime()

  if (now < start) return 0 // 未开始
  if (now > end) return 2 // 已结束
  return 1 // 进行中
}

const getTableList = async () => {
  try {
    loading.value = true
    const params: Record<string, string | number> = {
      page: pageNum.value,
      size: pageSize.value,
    };
    if (tempSearchForm.value.trainName) {
      params['trainName'] = tempSearchForm.value.trainName
    }
    const { data: { code, msg, data } } = await api.getStuTrainList(params);
    if (code === 200) {
      tableData.value = data.list.map((item: TrainingItem) => ({
        ...item,
        status: getTrainingStatus(item.beginTime, item.endTime)
      }));
      pageTotal.value = data.total;
    } else {
      ElMessage.error(msg)
    }
  } catch (error) {
    console.log(error)
  } finally {
    loading.value = false
  }
}

const handleEnter = (item: TrainingItem) => {
  const { href } = router.resolve({
    path: '/student/trainDetail',
    query: {
      trainId: item.trainId,
      studentId: route.query.studentId,
      trainName: encodeName(item.trainName)
    }
  })
  window.open(href, '_blank')
}

getTableList()
</script>

<style scoped lang="scss">
.train-list {
  padding: 20px;
  height: 100%;
  overflow-y: auto;

  .train-list-inner {
    max-width: 1608px;
    margin: 0 auto;
  }

  .list-header {
    @include flex(row, space-between, center, nowrap);
    margin-bottom: 32px;

    .title {
      padding: 0 20px;
      color: #ffffff;
      @include flex(row, flex-start, center, nowrap);
      position: relative;
      height: 47px;
      @include text-ellipsis();

      &::before {
        content: "";
        position: absolute;
        left: 0;
        bottom: 6px;
        transform: skewX(-20deg);
        width: 100%;
        height: 22px;
        background: linear-gradient(90deg, #cccccc33 0%, #27f2ef4d 9.75%, #2ba0ad33 19.9%, #11456900 88.5%, #2ba0ad33 100%);
      }
    }

    .title-label {
      margin-right: 8px;
      font-family: "YouSheBiaoTiHei";
      font-size: 36px;
      position: relative;
      z-index: 1;
    }

    .num-info {
      font-size: 16px;
      padding-left: 16px;
      position: relative;
      z-index: 1;
      margin-top: 11px;
      line-height: 20px;

      &::after {
        content: "";
        position: absolute;
        left: 0;
        top: 5px;
        transform: skewX(-20deg);
        width: 5px;
        height: 10px;
        background: #08F3E0;
      }

      &::before {
        content: "";
        position: absolute;
        left: 1px;
        top: 8px;
        transform: skewX(-20deg);
        width: 5px;
        height: 10px;
        background: #DBB642;
      }

      .num {
        color: #DBB642;
        padding: 0 6px;
      }
    }

    .search {
      :deep(.el-input) {
        width: 300px;
        height: 36px;
        justify-content: space-between;
        align-items: center;
        flex-shrink: 0;
        border: 1px solid #2ba0ad1a;
        color: #fff;

        .el-input__wrapper {
          background-color: transparent;
          border-radius: 0;
          box-shadow: 0 0 0 1px #2BA0AD;
        }

        .el-input__inner {
          font-size: 14px;
          background-color: transparent;
          color: #fff;

          &::placeholder {
            color: #fff;
          }
        }
      }

    }

    .search-icon {
      cursor: pointer;
      color: #fff;
    }
  }

  .list-container {
    margin: 0 auto 30px;
    gap: 24px;
  }


  .course-item {
    max-width: 384px;
    width: calc((100% - 48px) / 3);
    height: 186px;
    background: url('@/assets/images/train/bg-item.png') no-repeat center;
    background-size: 100% 100%;
    overflow: hidden;
  }

  .course-content-inner {
    @include flex(row, start, stretch);
  }

  .bg-wrapper {
    width: 102px;
    height: 110px;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
    }
  }

  .icon-wrapper {
    width: 40px;
    height: 40px;
    margin-bottom: 10px;

    .course-icon {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .course-info {
    padding: 24px 24px 0 0;
    color: #fff;
    font-family: "Microsoft YaHei UI";

    .title {
      font-size: 18px;
      font-weight: 700;
      line-height: 23px;
      margin-bottom: 4px;
      @include multi-ellipsis(2, 23px);
    }

    .time {
      font-size: 14px;
      line-height: 18px;
      margin-bottom: 13px;
    }
  }

  .icons {
    padding: 0 30px;
    @include flex(row, start, center);
    gap: 4px;
    font-family: "Microsoft JhengHei UI";
    color: #fff;
    margin-bottom: 18px;

    &.offset-right {
      padding-left: 102px;
    }

    &>div {
      @include flex(row, start, center);
      gap: 4px;
      flex: 1;
    }

    @for $i from 1 through 6 {
      .icon-#{$i} {
        display: inline-block;
        background-image: url('@/assets/images/train/icon-#{$i}.png');
        background-size: cover;
        background-repeat: no-repeat;
        width: 20px;
        height: 20px;
      }
    }

  }

  .btns {
    font-family: "YouSheBiaoTiHei";
    font-size: 20px;

    .status,
    .score {
      width: 138px;
      height: 36px;
      background: url('@/assets/images/btn/bg-btn-4.png') no-repeat;
      background-size: 100% 100%;
      text-align: center;
      line-height: 36px;
      color: #001E22;

      &.disabled {
        width: 276px;
        background: url('@/assets/images/btn/bg-btn-3.png') no-repeat;
        background-size: 100% 100%;
        color: #fff;
      }
    }

    .score {
      background: url('@/assets/images/btn/bg-btn-2.png') no-repeat;
      background-size: 100% 100%;
      color: #835A0A;
    }

    .enter-btn {
      width: 138px;
      height: 36px;
      background: url('@/assets/images/btn/bg-btn-1.png') no-repeat;
      background-size: 100% 100%;
      border: 0;
      color: #fff;
      font-family: "YouSheBiaoTiHei";
      font-size: 20px;

      &.is-disabled {
        filter: grayscale(60%);
      }
    }
  }

  .pagination-wrapper {
    @include flex(row, center, center);


    :deep(.el-pagination) {
      --el-pagination-button-bg-color: transparent;
      --el-pagination-button-color: #fff;
      --el-color-primary: #27D4F2;
      --el-color-white: #04478C;

      button,
      .number {
        box-shadow: 0 0 0 1px #27D4F2;
        border-radius: 0;
      }

      button {
        background-color: #27D4F2;
        color: #04478C;

        &:disabled {
          background-color: transparent;
          color: #27D4F2;
        }
      }

      .el-pager li.is-active {
        font-weight: normal;
      }
    }
  }

  .el-empty {
    --el-text-color-secondary: #fff;
  }
}
</style>
