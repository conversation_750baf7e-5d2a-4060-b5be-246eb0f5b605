import { ToolsView, NodeView } from '@antv/x6'

interface AnswerButtonOptions extends ToolsView.ToolItem.Options {
  x?: number
  y?: number
  offset?: {
    x?: number
    y?: number
  }
}

export class AnswerButton extends ToolsView.ToolItem<NodeView, AnswerButtonOptions> {
  private button: HTMLDivElement | null = null

  render() {
    this.button = this.createButton()
    this.container.appendChild(this.button)
    this.updatePosition()

    // 监听节点位置变化
    this.cellView.cell.on('change:position', () => {
      this.updatePosition()
    })

    return this
  }

  private createButton() {
    const btn = document.createElement('div')
    btn.className = 'answer-button'
    const isAnswer = this.cellView.cell.data?.answer

    btn.style.cssText = `
      position: absolute;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 24px;
      border-radius: 12px;
      cursor: pointer;
      font-size: 12px;
      color: white;
      background-color: ${isAnswer ? '#F56C6C' : '#67C23A'};
      transition: all 0.3s;
      transform: translate(116px, 40px) ;
      user-select: none;
    `

    btn.textContent = isAnswer ? '取消做题' : '设为做题'

    btn.addEventListener('click', this.onClick.bind(this))
    btn.addEventListener('mousedown', this.onMouseDown)
    return btn
  }

  private updatePosition() {
    if (!this.button) return
    const { x = 0, y = -30, offset } = this.options
    const bbox = this.cellView.getBBox()

    // 使用节点的实际位置
    const left = bbox.x + bbox.width / 2 + x + (offset?.x || 0)
    const top = bbox.y + y + (offset?.y || 0)

    this.button.style.left = `${left}px`
    this.button.style.top = `${top}px`
  }

  private onClick(e: MouseEvent) {
    e.preventDefault()
    e.stopPropagation()
    e.stopImmediatePropagation()

    const node = this.cellView.cell
    const isAnswer = node.data?.answer

    // 更新节点数据
    node.setData({
      ...node.data,
      answer: !isAnswer
    })

    const text = isAnswer ? node.data.name : ' '
    node.setAttrs({
      title: { text },
      label: { text }
    })

    // 更新按钮样式和文本
    if (this.button) {
      this.button.style.backgroundColor = isAnswer ? '#67C23A' : '#F56C6C'
      this.button.textContent = isAnswer ? '设为做题' : '取消做题'
    }
  }

  private onMouseDown(e: MouseEvent) {
    e.stopPropagation()
  }

  protected onRemove() {
    if (this.button) {
      this.button.removeEventListener('click', this.onClick)
      this.button.removeEventListener('mousedown', this.onMouseDown)
      // 移除位置变化监听
      this.cellView.cell.off('change:position', this.updatePosition)
    }
    super.onRemove()
  }
}

AnswerButton.config({
  tagName: 'div',
  isSVGElement: false,
})

// // 注册工具
// export const registerAnswerButton = (Graph: typeof import('@antv/x6').Graph) => {
//   Graph.registerNodeTool('answer-button', AnswerButton, false)
// }



