import { createRouter, createWebHashHistory } from 'vue-router'
import Layout from '@/layout/index.vue'

const router = createRouter({
  history: createWebHashHistory(),
  routes: [
    {
      path: '/',
      redirect: '/404',
    },
    {
      path: '/student',
      component: Layout,
      redirect: '/student/trainList',
      children: [
        {
          path: 'trainList',
          component: () => import('@/views/student/trainList.vue'),
          meta: {
            title: '实训列表',
          }
        },
      ]
    },
    {
      path: '/student/trainDetail',
      component: () => import('@/views/student/trainDetail/index.vue'),
      meta: {
        title: '实训详情',
      }
    },
    {
      path: '/apiLogin',
      component: () => import('@/views/apiLogin.vue'),
    },
    {
      path: '/admin',
      component: () => import('@/layout/admin.vue'),
      redirect: '/admin/questionList',
      children: [
        {
          path: 'questionList',
          component: () => import('@/views/admin/question/list.vue'),
          meta: {
            title: '题目管理',
          }
        },
        {
          path: 'enterpriseList',
          component: () => import('@/views/admin/baseData/index.vue'),
          meta: {
            title: '企业管理',
          }
        },
        {
          path: 'enterpriseData',
          component: () => import('@/views/admin/baseData/enterpriseData.vue'),
          meta: {
            title: '基础数据管理',
          }
        },
        {
          path: 'questionCategory',
          component: () => import('@/views/admin/questionCategory/index.vue'),
          meta: {
            title: '题目分类管理',
          }
        },
        {
          path: 'questionContent',
          component: () => import('@/views/admin/question/content.vue'),
          meta: {
            title: '内容管理',
          }
        },
      ]
    },
    {
      path: '/teacher',
      component: () => import('@/layout/admin.vue'),
      redirect: '/teacher/trainingList',
      children: [
        {
          path: 'trainingList',
          component: () => import('@/views/teacher/training/list.vue'),
        },
        {
          path: 'stuManage',
          component: () => import('@/views/teacher/training/stuManage.vue'),
        },
        {
          path: 'achievement',
          component: () => import('@/views/teacher/training/achievement.vue'),
        },
      ]
    },
    {
      path: '/infoSet',
      component: () => import('@/views/infoSet.vue'),
    },
    {
      path: '/login',
      component: () => import('@/views/login.vue'),
    },
    {
      path: '/adminLogin',
      component: () => import('@/views/adminLogin.vue'),
    },
    {
      path: '/student/barrier',
      component: () => import('@/views/student/barrier.vue'),
      meta: {
        title: '关卡',
      }
    },
    {
      path: '/404',
      component: () => import('@/views/404.vue'),
    },
    {
      path: '/:pathMatch(.*)*',
      name: '404',
      component: () => import('@/views/404.vue'),
    },
  ],
})

export default router
