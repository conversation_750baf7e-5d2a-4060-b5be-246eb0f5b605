import $http from "../http"
import appSettings from "../settings"
const { webAPI } = appSettings

interface Params {
  [key: string]: any
}

//实训题目列表
export const getList = (params: Params) => {
  return $http({
    url: webAPI + '/ninBcQuestion/getList',
    method: 'get',
    params
  })
}
//查询题目管理
export const getQuestionContent = (params: Params) => {
  return $http({
    url: webAPI + '/ninBcQuestion/getQuestionContent',
    method: 'get',
    params
  })
}
//新增/编辑实训题目
export const addOrUp = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcQuestion/addOrUp',
    method: 'post',
    data
  })
}
//删除/批量删除实训题目
export const delBcQuestion = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcQuestion/delBcQuestion',
    method: 'post',
    data
  })
}
//保存题目管理
export const saveQuestionContent = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcQuestion/saveQuestionContent',
    method: 'post',
    data,
    upload: true
  })
}

/**
 * 企业管理
 */
//获取企业列表
export const getEnterpriseList = (params: Params) => {
  return $http({
    url: webAPI + '/ninBcEnterprise/getEnterpriseList',
    method: 'get',
    params
  })
}

//新增/编辑企业
export const addEditEnterprise = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcEnterprise/addEditEnterprise',
    method: 'post',
    data
  })
}

//删除企业
export const delEnterprise = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcEnterprise/delEnterprise',
    method: 'post',
    data
  })
}

//复制企业
export const copyEnterprise = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcEnterprise/copyEnterprise',
    method: 'post',
    data
  })
}


/**
 * 基础数据
 */

//获取基础数据列表
export const getBaseDataList = (params: Params) => {
  return $http({
    url: webAPI + '/ninBcBasicData/getList',
    method: 'get',
    params
  })
}

export const importBaseData = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcBasicData/importBasicData',
    method: 'post',
    data,
    upload: true
  })
}

// 获取模板文件流
export const downLoadTemplate = () => {
   return $http({
    url: webAPI + '/ninBcBasicData/downLoadTemplate',
    method: 'get',
    responseType: 'blob'
  })
}



//新增/编辑基础数据
export const addEditBasicData = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcBasicData/addEditBasicData',
    method: 'post',
    data
  })
}

//删除基础数据
export const delBasicData = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcBasicData/delBasicData',
    method: 'post',
    data
  })
}

//排序基础数据
export const changeDataSeq = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcBasicData/changeSeq',
    method: 'post',
    data
  })
}

/**
 * 题目分类管理
 */

//列表
export const getCategoryList = (params: Params) => {
  return $http({
    url: webAPI + '/ninBcQuestionCategory/getCategoryList',
    method: 'get',
    params
  })
}

//新增/编辑
export const addEditCategory = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcQuestionCategory/addEditCategory',
    method: 'post',
    data
  })
}

//删除
export const delCategory = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcQuestionCategory/delCategory',
    method: 'post',
    data
  })
}

//排序
export const changeCategorySeq = (data: Params) => {
  return $http({
    url: webAPI + '/ninBcQuestionCategory/changeSeq',
    method: 'post',
    data
  })
}
