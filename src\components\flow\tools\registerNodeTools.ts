import customDelete from "./customDelete";
import { AnswerButton } from "./answerBtn";
import { Graph } from "@antv/x6"


export const registerNodeTools = (graph: typeof Graph) => {
  graph.registerNodeTool('cus-button-remove', customDelete, true)
  graph.registerNodeTool('answer-button', AnswerButton, true)
}

export const unregisterNodeTools = (graph: typeof Graph) => {
  graph.unregisterNodeTool('cus-button-remove')
  graph.unregisterNodeTool('answer-button')
}

