<template>
  <div class="options">
    <div class="section-title-light">
      <div class="title-label">选项</div>
    </div>
    <div class="content-wrapper">
      <el-scrollbar height="100%">
        <TransitionGroup tag="div" name="option-fade" class="option-list">
          <OptionItem width="100%" v-for="item in optionList" :key="item.id" :name="item.name"
            :empty="usedOptionIds.has(item.id)" :disabled="disabled || usedOptionIds.has(item.id)"
            @dragstart="handleDragStart(item, $event)" />
        </TransitionGroup>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import OptionItem from './OptionItem.vue'
import type { OptionItem as OptionItemType } from '@/views/admin/types'

const props = defineProps<{
  options: OptionItemType[]
  usedOptionIds: Set<string>
  disabled?: boolean,
}>()

const emit = defineEmits<{
  'drag-start': [item: OptionItemType, event: DragEvent]
}>()

const handleDragStart = (item: OptionItemType, event: DragEvent) => {
  emit('drag-start', item, event)
}

const optionList = computed(() => {
  return props.options.filter(item => !props.usedOptionIds.has(item.id))
})
</script>

<style scoped lang="scss">
.options {
  width: 100%;
  height: 100%;
  background-image: linear-gradient(to bottom, #fff 0%, rgba(255, 255, 255, 0.5) 100%);
  box-shadow: 0px 2px 4px 0px rgba(120, 147, 187, 0.15);
  border-radius: 6px;
  border: solid 1px #ffffff;
  overflow: hidden;
  gap: 12px;
  @include flex(column, flex-start, flex-start, nowrap);

  .content-wrapper {
    width: 100%;
    flex: 1;
    min-height: 0;
  }

  .option-list {
    padding: 0 16px;
    transition: all 0.3s ease-in-out;
    overflow: hidden;
  }

  :deep(.option-item) {
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 12px;
  }
}

.option-fade-move,
.option-fade-enter-active,
.option-fade-leave-active {
  transition: all 0.5s cubic-bezier(0.55, 0, 0.1, 1);
}

.option-fade-enter-from,
.option-fade-leave-to {
  opacity: 0;
  transform: scaleY(0.01) translate(30px, 0);
}


.option-fade-leave-active {
  position: absolute;
}
</style>
