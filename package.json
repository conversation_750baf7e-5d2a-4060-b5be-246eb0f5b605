{"name": "business-draw", "version": "0.0.0", "private": true, "type": "module", "scripts": {"serve": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@antv/hierarchy": "^0.6.14", "@antv/x6": "^2.18.1", "@antv/x6-plugin-clipboard": "^2.1.6", "@antv/x6-plugin-dnd": "^2.1.1", "@antv/x6-plugin-history": "^2.2.4", "@antv/x6-plugin-keyboard": "^2.2.3", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-plugin-snapline": "^2.1.7", "@antv/x6-plugin-transform": "^2.1.8", "@ckeditor/ckeditor5-build-decoupled-document": "^35.2.1", "@ckeditor/ckeditor5-vue": "^4.0.1", "axios": "^1.7.9", "element-plus": "^2.10.1", "js-base64": "^3.7.7", "js-md5": "^0.8.3", "lodash": "^4.17.21", "nprogress": "^0.2.0", "qs": "^6.14.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "vuex": "^4.1.0", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@tsconfig/node22": "^22.0.0", "@types/ckeditor__ckeditor5-build-decoupled-document": "^37.0.4", "@types/node": "^22.10.2", "@types/nprogress": "^0.2.3", "@types/qs": "^6.9.18", "@vitejs/plugin-vue": "^5.2.4", "@vue/eslint-config-prettier": "^10.1.0", "@vue/eslint-config-typescript": "^14.1.3", "@vue/tsconfig": "^0.7.0", "eslint": "^9.14.0", "eslint-plugin-vue": "^9.30.0", "npm-run-all2": "^7.0.2", "prettier": "^3.3.3", "sass": "^1.83.1", "typescript": "~5.6.3", "vite": "^5.4.19", "vite-plugin-checker": "^0.9.3", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-devtools": "^7.6.8", "vue-tsc": "^2.1.10"}}