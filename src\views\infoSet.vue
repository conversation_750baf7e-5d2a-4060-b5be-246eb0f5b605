<template>
  <div class="home-wrap">
    <img src="@/assets/images/no_address.png" class="img">
    <p class="title">非法地址，请从正确入口进入！</p>
    <div class="login" v-if="isShowLogin">
      <el-button type="primary" @click="isOpenDialog = true" v-if="!hasToken">设置Token</el-button>
      <SetToken :visible="isOpenDialog" @close="isOpenDialog = false"></SetToken>
      <div v-if="hasToken">
        <el-button type="success" @click="handleGoto('student')">学生</el-button>
        <el-button type="success" @click="handleGoto('teacher')">教师</el-button>
        <el-button type="success" @click="handleGoto('admin')">出题</el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, ref } from "vue";
import SetToken from "@/components/SetToken.vue";
import { useStore } from "vuex";
import { useRouter } from "vue-router";

const store = useStore();
const router = useRouter();
const isOpenDialog = ref(false);
const isShowLogin = computed(() => {
  return process.env.NODE_ENV !== 'production'
})
const hasToken = computed(() => {
  return store.getters.token
})
const handleGoto = (value: string) => {
  let path = '',
    query: any = {}
  if (value == 'student') {
    path = '/student/trainList';
    query.trialId = '1089628350307454976'
  }
  if (value == 'teacher') {
    path = '/teacher/trainingList';
  }
  if (value == 'admin') {
    path = '/admin/questionList';
  }
  router.push({
    path,
    query
  })
}
const closePage = () => {
  if (!isShowLogin.value && window.name != '') {
    window.close();
  }
}
onMounted(() => {
  // localStorage.clear();
  closePage()
})
</script>

<style scoped lang="scss">
.home-wrap {
  text-align: center;
  padding-top: 15%;

  .img {
    max-width: 300px;
  }

  .title {
    font-size: 24px;
    margin-top: 30px;
    color: #504df2;
  }

  .login {
    margin-top: 20px;
  }
}
</style>
