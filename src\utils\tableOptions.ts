import { ElMessage, ElMessageBox } from 'element-plus'
import { ref } from 'vue'

/**
 * updated：2022-06-07  by hanh
 * vue3.0中，列表页面常用方法
 */

export class TableOptions {
    pageSize = ref<number>(10)
    pageNum = ref<number>(1)
    pageTotal = ref<number>(0)
    tableData = ref<any[]>([])
    loading = ref<boolean>(false)
    ninSearchForm: any = ref({}) //默认表单对象
    tempSearchForm: any = ref({}) //中间搜索变量，临时储存

    /**
     * 构造方法
     * @param pageSize //每页条数
     * @param pageNum //页码
     * @param pageTotal //总页数
     */
    constructor(pageSize?: number, pageNum?: number, pageTotal?: number) {
        if (pageSize) this.pageSize.value = pageSize
        if (pageNum) this.pageNum.value = pageNum
        if (pageTotal) this.pageTotal.value = pageTotal
    }

    // 每页显示条数
    handleSizeChange = async (value: number, callback: Function) => {
        this.pageSize.value = value;
        this.pageNum.value = 1;
        await callback();
    };
    // 翻页 (查询时候，传true过去)
    handleCurrentChange = async (value: number, callback: Function, isSearch = false) => {
        if (isSearch) {
            this.tempSearchForm.value = {
                ...this.ninSearchForm.value
            }
        } else {
            this.ninSearchForm.value = {
                ...this.tempSearchForm.value
            }
        }
        this.pageNum.value = value;
        await callback();
    };
    // 删除当前页最后一条数据，回到上一页
    handleDeleteChange = async (tableData: Array<any>, callback: Function) => {
        if (tableData.length == 0 && this.pageNum.value != 1) {
            if (this.pageNum.value > 1) {
                this.pageNum.value = this.pageNum.value - 1;
            } else {
                this.pageNum.value = 1
            }
            await callback();
        }
    };
}

/**
* 通用对话框
* @param alias {*} 接口别名
* @param methodName {*} 方法名
* @param param {*} 参数
* @param msgText {*} 提示信息
* @param confirmContent {*} 对话框内容
* @param callback {*} 回调
*/
export const commonConfirmMethods = (
    alias: any,
    methodName: string,
    param: any,
    msgText: string,
    confirmContent: string,
    callback: Function,
    failText?: "操作失败!"
) => {
    ElMessageBox.confirm(
        confirmContent,
        '提示信息',
        {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning',
            showClose: false
        }
    )
        .then(() => {
            (alias as any)[methodName](param).then((res: any) => {
                if (res.data.code == 200) {
                    ElMessage({
                        type: 'success',
                        message: msgText,
                    })
                    callback();
                } else {
                    ElMessage.error(failText)
                }
            });

        })
        .catch(() => { })

};
