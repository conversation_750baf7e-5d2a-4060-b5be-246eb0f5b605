export interface Question {
  createTime: string
  createUserCode: string
  questionId: string
  questionName: string
  questionType: number
  questionSecondType: number
  taskNote: string
  enterpriseId: string
}

export interface OptionItem {
  id: string
  name: string
}

export interface BaseDataItem {
  basicCode: string
  basicId: string
  parentId: string
  basicType: number
  basicName: string
  seq: number
}

export interface BaseDataItemTree extends BaseDataItem {
  children?: BaseDataItemTree[]
}

export interface CategoryItem {
  categoryIcon: string;
  categoryId: string;
  categoryName: string;
}

export interface ImageModule {
  default: string
}

export interface EnterpriseItem {
  enterpriseId: string;
  enterpriseName: string;
  enterpriseDesc: string;
}
